# Test script for Donut functionality in the C2 agent

# Ensure Donut is installed
if (-not (Get-Command donut -ErrorAction SilentlyContinue)) {
    Write-Host "Donut is not installed. Please install it first." -ForegroundColor Red
    Write-Host "You can download it from: https://github.com/TheWover/donut/releases" -ForegroundColor Yellow
    exit
}

# Compile the example C program
Write-Host "Compiling the example C program..." -ForegroundColor Cyan
$clPath = "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cl.exe"
if (-not (Test-Path $clPath)) {
    Write-Host "Visual Studio compiler not found. Please adjust the path in the script." -ForegroundColor Red
    exit
}

# Create the examples directory if it doesn't exist
$examplesDir = Join-Path $PSScriptRoot "examples"
if (-not (Test-Path $examplesDir)) {
    New-Item -ItemType Directory -Path $examplesDir | Out-Null
}

# Compile the C example
$helloC = Join-Path $examplesDir "hello.c"
$helloExe = Join-Path $examplesDir "hello.exe"
& $clPath /nologo /O2 /Fe:$helloExe $helloC

# Test Donut directly
Write-Host "Testing Donut directly..." -ForegroundColor Cyan
$outputBin = Join-Path $examplesDir "hello_shellcode.bin"
& donut -f 1 -o $outputBin $helloExe

if (Test-Path $outputBin) {
    Write-Host "Donut successfully generated shellcode: $outputBin" -ForegroundColor Green
    Write-Host "Shellcode size: $((Get-Item $outputBin).Length) bytes" -ForegroundColor Green
} else {
    Write-Host "Failed to generate shellcode with Donut" -ForegroundColor Red
    exit
}

# Test the agent with Donut
Write-Host "To test the agent with Donut, run the following command:" -ForegroundColor Cyan
Write-Host "donut:$helloExe:arg1 arg2" -ForegroundColor Yellow

Write-Host "`nFor .NET assemblies, you can specify class and method:" -ForegroundColor Cyan
Write-Host "donut:path\to\assembly.dll:arg1,arg2" -ForegroundColor Yellow

Write-Host "`nDonut testing completed!" -ForegroundColor Green
