# Payloads Directory

This directory contains payloads that can be used with the `donut` command.

## Usage

1. Place your payload files (EXE, DLL, .NET assemblies, etc.) in this directory
2. Use the `donut` command with just the filename:

```
donut <agent_id> <filename> [arguments]
```

For example:
```
donut abc123 hello.exe "arg1 arg2"
```

## Supported File Types

- Windows EXE files
- Windows DLL files
- .NET assemblies
- VBScript files
- JavaScript files

## Security Note

Be careful about what payloads you place in this directory. Only use payloads from trusted sources.
