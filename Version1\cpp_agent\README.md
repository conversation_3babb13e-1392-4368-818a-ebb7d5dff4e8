# C++ Agent for C2 Framework

This directory contains a C++ implementation of the agent for the C2 framework. The C++ agent provides better stealth, lower-level system access, and improved performance compared to the Go agent.

## Key Features

- **Completely Independent**: No external dependencies required - works on any Windows machine
- **Small Footprint**: Minimal size with only essential components
- **Statically Linked**: All libraries are statically linked for maximum portability

## Features

- **HTTP(S) Communication**: Disguised as legitimate traffic with realistic headers
- **Encrypted Communication**: All C2 traffic is encrypted using AES-128
- **Jittered Beaconing**: Randomized beacon intervals to avoid detection patterns
- **Command Execution**:
  - Shell commands
  - BOF/COFF files
  - Donut-generated shellcode
- **In-Memory Execution**: No disk writes for payload execution
- **Stealth Features**: Minimal footprint and anti-analysis techniques

## Building

### Prerequisites

You only need GCC (MinGW) to build the agent:

#### For Building
- MinGW-w64 with GCC 8.1.0 or higher
- Python 3.6 or higher (for the build script)

#### For Running
- Any Windows machine (no dependencies required)

### Build Instructions

#### Building with Python Script (Recommended)

The easiest way to build the agent is using the provided Python script:

```
cd cpp_agent
python build.py
```

Additional options:

- Build in debug mode: `python build.py --debug`
- Clean before building: `python build.py --clean`
- Simple build (single command): `python build.py --simple`
- Clean only: `python build.py --clean`

#### Building with Make

You can also use Make to build the agent:

```
cd cpp_agent
mingw32-make -f Makefile
```

#### Building with CMake

If you prefer to use CMake:

```
cd cpp_agent
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

For Visual Studio:

```
cd cpp_agent
mkdir build
cd build
cmake .. -A x64
cmake --build . --config Release
```

The executable will be located at `build/bin/Release/cpp_agent.exe`.

## Usage

### Basic Usage

Run the agent:

```
cpp_agent.exe
```

This will start the agent in stealth mode (hidden console window).

### Debug Mode

To run the agent in debug mode with console output:

```
cpp_agent.exe --debug
```

## Integration with C2 Framework

The C++ agent is compatible with the existing C2 framework. It uses the same communication protocol and command format as the Go agent.

### Command Types

The agent supports the following command types:

- `shell`: Execute shell commands
- `bof`: Execute Beacon Object Files
- `donut`: Execute payloads using Donut

### Communication

The agent communicates with the C2 server using HTTP(S) requests to the following endpoints:

- Beaconing: `http://127.0.0.1:8080/api/v1/saas/telemetry`
- File Upload: `http://127.0.0.1:8080/api/v1/files/upload`

All communication is encrypted using AES-128 with the key and IV defined in the agent configuration.

## Customization

To customize the agent, modify the following files:

- `include/agent.h`: Configuration constants
- `src/agent.cpp`: Core agent functionality
- `src/http.cpp`: HTTP communication
- `src/crypto.cpp`: Encryption/decryption
- `src/shellcode.cpp`: Shellcode execution
- `src/coffloader.cpp`: BOF/COFF loader
- `include/mini_json.h`: Minimal JSON parser implementation
- `include/mini_donut.h`: Minimal shellcode generation implementation

## Security Considerations

- The agent uses standard Windows APIs for network communication and cryptography
- Shellcode execution is performed using VirtualAlloc/CreateThread, which may be detected by security products
- Consider implementing additional obfuscation or evasion techniques for production use

## Independence Features

- **No External Dependencies**: All required functionality is embedded in the agent
- **Statically Linked**: All C++ runtime libraries are statically linked
- **Embedded JSON Parser**: Custom minimal JSON parser instead of external library
- **Embedded Shellcode Generator**: Custom minimal shellcode generator instead of external Donut
- **Self-Contained**: Works on any Windows machine without additional installations

## Notes

- This implementation is designed to be compatible with the existing C2 framework
- The BOF/COFF loader is a simplified implementation
- The shellcode generator is a minimal implementation that can be enhanced for specific needs
