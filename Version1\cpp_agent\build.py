#!/usr/bin/env python3
"""
Build script for C++ Agent
This script automates the build process for the C++ agent using GCC.
"""

import os
import sys
import subprocess
import argparse
import shutil
from pathlib import Path

def check_compiler(compiler):
    """Check if the compiler is available."""
    try:
        subprocess.run([compiler, '--version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

def create_directories():
    """Create necessary directories if they don't exist."""
    directories = ['bin', 'obj']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def download_json_library():
    """No longer needed as we use our own mini JSON implementation."""
    pass

def build_with_gcc(debug=False, clean=False):
    """Build the C++ agent using GCC."""
    # Check if GCC is installed
    if not check_compiler('g++'):
        print("GCC (g++) is not installed or not in PATH.")
        print("Please install MinGW-w64 and add it to your PATH.")
        return False

    # Clean if requested
    if clean:
        clean_build()

    # Create directories and download dependencies
    create_directories()
    download_json_library()

    # Set compiler flags
    cxxflags = [
        '-std=c++17',
        '-Wall',
        '-Wextra',
        '-pedantic',
        '-DUNICODE',
        '-D_UNICODE',
        '-D_CRT_SECURE_NO_WARNINGS',
        '-static',  # Statically link everything
        '-static-libgcc',  # Statically link libgcc
        '-static-libstdc++'  # Statically link libstdc++
    ]

    # Add debug flags if requested
    if debug:
        cxxflags.append('-g')
        cxxflags.append('-O0')
        cxxflags.append('-DDEBUG')
    else:
        cxxflags.append('-O2')

    # Set include paths
    includes = ['-Iinclude']

    # Set libraries
    libs = ['-lwinhttp', '-lcrypt32', '-lrpcrt4']

    # Source files
    source_files = [
        'src/main.cpp',
        'src/agent.cpp',
        'src/http.cpp',
        'src/crypto.cpp',
        'src/shellcode.cpp',
        'src/coffloader.cpp',
        'src/custom_loader.cpp',
        'src/ps_shellcode.cpp',
        'src/uuid_shellcode.cpp',
        'src/reflective_loader.cpp'
    ]

    # Object files
    object_files = [f'obj/{Path(src).stem}.o' for src in source_files]

    # Compile source files
    print("Compiling source files...")
    for src, obj in zip(source_files, object_files):
        cmd = ['g++'] + cxxflags + includes + ['-c', src, '-o', obj]
        print(f"Compiling {src}...")
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if result.returncode != 0:
            print(f"Failed to compile {src}:")
            print(result.stderr.decode())
            return False

    # Link object files
    print("Linking...")
    output = 'bin/cpp_agent.exe'
    cmd = ['g++'] + object_files + libs + ['-o', output]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if result.returncode != 0:
        print("Failed to link:")
        print(result.stderr.decode())
        return False

    # Copy to current directory
    shutil.copy(output, 'cpp_agent.exe')

    print("Build successful!")
    print(f"Executable is located at: {os.path.abspath(output)}")
    return True

def build_simple_gcc(debug=False):
    """Build the C++ agent using GCC in a single command."""
    # Check if GCC is installed
    if not check_compiler('g++'):
        print("GCC (g++) is not installed or not in PATH.")
        print("Please install MinGW-w64 and add it to your PATH.")
        return False

    # Create directories and download dependencies
    create_directories()
    download_json_library()

    # Set compiler flags
    cxxflags = [
        '-std=c++17',
        '-Wall',
        '-Wextra',
        '-pedantic',
        '-DUNICODE',
        '-D_UNICODE',
        '-D_CRT_SECURE_NO_WARNINGS',
        '-static',  # Statically link everything
        '-static-libgcc',  # Statically link libgcc
        '-static-libstdc++'  # Statically link libstdc++
    ]

    # Add debug flags if requested
    if debug:
        cxxflags.append('-g')
        cxxflags.append('-O0')
        cxxflags.append('-DDEBUG')
    else:
        cxxflags.append('-O2')

    # Set include paths
    includes = ['-Iinclude']

    # Set libraries
    libs = ['-lwinhttp', '-lcrypt32', '-lrpcrt4']

    # Source files
    source_files = [
        'src/main.cpp',
        'src/agent.cpp',
        'src/http.cpp',
        'src/crypto.cpp',
        'src/shellcode.cpp',
        'src/coffloader.cpp',
        'src/custom_loader.cpp',
        'src/ps_shellcode.cpp',
        'src/uuid_shellcode.cpp',
        'src/reflective_loader.cpp'
    ]

    # Compile and link in one step
    print("Compiling and linking...")
    output = 'bin/cpp_agent.exe'
    cmd = ['g++'] + cxxflags + includes + source_files + libs + ['-o', output]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if result.returncode != 0:
        print("Failed to build:")
        print(result.stderr.decode())
        return False

    # Copy to current directory
    shutil.copy(output, 'cpp_agent.exe')

    print("Build successful!")
    print(f"Executable is located at: {os.path.abspath(output)}")
    return True

def clean_build():
    """Clean build files."""
    print("Cleaning build files...")
    directories = ['bin', 'obj']
    for directory in directories:
        if os.path.exists(directory):
            shutil.rmtree(directory)

    if os.path.exists('cpp_agent.exe'):
        os.remove('cpp_agent.exe')

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Build script for C++ Agent')
    parser.add_argument('--debug', action='store_true', help='Build in debug mode')
    parser.add_argument('--clean', action='store_true', help='Clean build files before building')
    parser.add_argument('--simple', action='store_true', help='Use simple build (single command)')
    args = parser.parse_args()

    if args.clean and not (args.simple or args.debug):
        clean_build()
        return 0

    if args.simple:
        success = build_simple_gcc(args.debug)
    else:
        success = build_with_gcc(args.debug, args.clean)

    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
