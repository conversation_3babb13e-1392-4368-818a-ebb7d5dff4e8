#include <stdio.h>
#include <windows.h>

int main(int argc, char* argv[]) {
    printf("Hello from Donut-generated shellcode!\n");
    
    // Print command line arguments
    printf("Arguments:\n");
    for (int i = 0; i < argc; i++) {
        printf("  [%d] %s\n", i, argv[i]);
    }
    
    // Get system information
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    
    printf("\nSystem Information:\n");
    printf("  Processor Architecture: %d\n", sysInfo.wProcessorArchitecture);
    printf("  Number of Processors: %d\n", sysInfo.dwNumberOfProcessors);
    printf("  Page Size: %d bytes\n", sysInfo.dwPageSize);
    
    // Get current process information
    DWORD pid = GetCurrentProcessId();
    HANDLE hProcess = GetCurrentProcess();
    
    printf("\nProcess Information:\n");
    printf("  Process ID: %d\n", pid);
    
    // Get current username
    char username[256];
    DWORD usernameSize = sizeof(username);
    if (GetUserNameA(username, &usernameSize)) {
        printf("  Current User: %s\n", username);
    }
    
    // Sleep for a moment to allow output to be seen
    printf("\nSleeping for 5 seconds...\n");
    Sleep(5000);
    
    return 0;
}
