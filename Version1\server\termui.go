package main

import (
	"fmt"
	"sort"
	"strings"
	"time"
)

// ANSI color codes
const (
	Reset   = "\033[0m"
	Bold    = "\033[1m"
	Red     = "\033[31m"
	Green   = "\033[32m"
	Yellow  = "\033[33m"
	Blue    = "\033[34m"
	Magenta = "\033[35m"
	Cyan    = "\033[36m"
	White   = "\033[37m"
	BgRed   = "\033[41m"
	BgBlack = "\033[40m"
)

// Banner displays the C2 server ASCII art banner
func Banner() string {
	banner := `
` + Red + Bold + `
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣴⣀⣤⡀⣎⣹⠀⢀⣤⣄⠀⠀⠀⠀⠀⢠⣤⡄⠀⢿⣹⢆⣠⣄⢢⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢠⣦⣤⡻⣧⣤⠽⠛⠛⠒⢶⣷⣾⡏⠉⣿⡋⠙⣿⣶⡷⠒⠚⠛⠫⣤⣤⠟⣤⣴⢤⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⡇⢠⠗⠀⠀⠀⠀⠀⠀⠀⠀⠀⠛⢦⣄⡴⠚⠁⠀⠀⠀⠀⠀⠀⠀⠀⠸⣦⢈⠇⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣿⣿⣿⠈⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠙⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠁⢸⢿⣿⡆⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⢰⣻⣷⡆⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⣨⣔⣿⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⡴⠶⢶⡄⠀⠀⣀⣀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢸⢲⣥⡀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣾⡿⡆
	⠈⢿⣿⣤⣤⣤⣤⠤⠤⠤⢤⣀⡀⠀⠀⠘⣿⣿⣇⠀⠀⠀⠀⠀⠀⠀⠀⠀⢿⡄⠀⢈⡷⠚⠉⠉⠉⠙⢷⠀⠀⠀⠀⠀⠀⠀⢸⣾⣷⠇⠀⠀⢀⣀⣤⠤⠤⠤⣤⣤⣤⣤⣽⣿⠃
	⠀⠀⢀⣻⣿⣿⠟⠋⠉⠉⠉⠉⠛⠳⣦⣐⣧⣄⢻⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠻⢶⣿⣀⠀⠀⠀⠀⠀⠸⠀⠀⠀⠀⠀⠀⠀⣸⣀⣴⢇⣴⠾⠛⠉⠉⠉⠉⠙⠻⢿⣽⣟⡁⠀⠀
	⠀⢀⡾⠁⠟⠀⠀⠀⠀⠀⠀⠀⣴⣶⣤⣭⣳⣦⣼⣇⠀⠀⠀⠀⠀⠀⣴⠞⠛⠳⣾⡏⠻⣷⡀⠀⠀⠀⠀⠀⠀⡤⠤⠄⠤⢴⣧⢴⣶⣫⣥⣶⣶⡄⠀⠀⠀⠀⠀⠀⠹⠈⢻⡄⠀
	⠀⢿⡇⠀⠀⠀⠀⠀⠀⠀⢠⠀⣿⣤⣄⠀⠙⢿⣧⠘⢦⡀⠀⠀⠀⢸⡇⠀⠀⠀⢈⣿⡄⢸⡇⠀⠀⠀⠰⣶⠁⠀⠀⠀⣠⠋⣸⣿⠏⠉⣢⣤⣽⠃⡀⠀⠀⠀⠀⠀⠀⠀⠀⣿⡄
	⢸⠘⢷⠀⠀⠀⠀⠀⠀⠀⠀⢳⠈⠙⠟⠀⠀⠈⢻⡟⡿⠳⣄⠀⠀⠘⣧⡀⠸⠶⠟⠉⣹⡿⣥⣄⣢⡥⠞⠁⠀⠀⢀⠜⢿⢻⣿⠁⠀⠀⠻⢟⠁⡼⠁⠀⠀⠀⠀⠀⠀⠀⠾⠏⡇
	⠈⣦⣀⠀⠀⠀⠀⠀⠀⠀⠀⢨⠀⠀⠀⠀⠀⠀⠀⠀⢧⣀⣈⣦⡀⠀⠈⠙⠶⠦⠶⠾⠋⠀⠀⠀⠀⠀⠀⠀⢀⣴⣋⣀⣠⠃⠀⠀⠀⠀⠀⠈⠄⡇⠀⠀⠀⠀⠀⠀⠀⠀⣀⣠⡇
	⠀⠻⣍⠛⠀⠀⠀⠀⣰⣄⢰⣾⣄⡀⠀⠀⠀⠀⠀⠀⠀⠀⢠⠃⠘⢦⣀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⡴⠋⠈⣧⠀⠀⠀⠀⠀⠀⠀⠀⢀⣨⣿⣆⣠⣦⠀⠀⠀⠀⠘⢋⡟⠀
	⠀⠀⠈⠳⢤⣀⣀⣴⣿⡿⠟⢉⣴⣷⠀⠀⠀⠀⠀⠀⠀⠀⠈⠲⠴⢒⠏⠳⣄⠀⠀⠀⠀⠀⠀⠀⣀⡴⠋⡗⠲⠔⠋⠀⠀⠀⠀⠀⠀⠀⠀⢸⣷⡽⡛⢿⣿⣧⣀⣀⣠⠶⠋⠀⠀
	⠀⠀⠀⠀⠀⠈⠉⠉⠁⠀⠀⠀⠉⠉⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠸⡤⣤⣿⡟⢦⡀⠀⠀⡴⠊⣿⣥⣤⠗⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠉⠁⡇⠀⠀⠉⠉⠁⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠙⠽⠃⣧⣒⣿⢶⢻⢛⣮⡏⠫⠚⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⡠⠐⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠙⠒⢡⠟⣎⠓⠊⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠐⠓⠊⠁⠉⠉⠧⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣷⣶⣾⠇⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
` + Reset + Red + `
  				     Voynich
				Kommand & Kontrol
  				[ Version 1.0.0 ]
` + Reset + `
  			Type 'help' for available commands
`
	return banner
}

// HelpMenu returns the help menu text
func HelpMenu() string {
	help := Bold + "Available Commands:" + Reset + `

` + Bold + Green + "list" + Reset + ` or ` + Bold + Green + "agents" + Reset + `
    List all connected agents

` + Bold + Green + "alias <agent_id> <alias_name>" + Reset + `
    Set a friendly alias for an agent
    Example: alias abc123 workstation1
    Note: You can use just the first few characters of the agent ID

` + Bold + Green + "shell <agent_id> <command>" + Reset + `
    Execute a shell command on an agent
    Example: shell abc123 whoami
    Note: You can use the alias instead of the agent ID

` + Bold + Green + "task <agent_id> <command>" + Reset + `
    Alias for the shell command
    Example: task abc123 whoami

` + Bold + Green + "bof <agent_id> <path> <entry_point> [args]" + Reset + `
    Execute a Beacon Object File on an agent
    Example: bof abc123 C:\path\to\bof.o go arg1,arg2

` + Bold + Green + "donut <agent_id> <file_name> [args]" + Reset + `
    Execute a payload using Donut on an agent
    Example: donut abc123 hello.exe "arg1 arg2"
    Note: Files should be in the 'payloads' directory

` + Bold + Green + "results <agent_id>" + Reset + `
    View the results of tasks executed by an agent
    Example: results abc123
    Note: You can use the alias instead of the agent ID

` + Bold + Green + "clear-results <agent_id>" + Reset + `
    Clear all task results for an agent
    Example: clear-results abc123
    Note: This cannot be undone

` + Bold + Green + "read-info <file_path>" + Reset + `
    Read and decrypt an encrypted info file
    Example: read-info LAPTOP-ABC123_info.dat
    Note: The file must be encrypted with the same key as the server

` + Bold + Green + "list-uploads <agent_id>" + Reset + `
    List all files uploaded by an agent
    Example: list-uploads abc123
    Note: Files are automatically uploaded from agents

` + Bold + Green + "clear" + Reset + `
    Clear the terminal screen

` + Bold + Green + "debug" + Reset + `
    Display debug information about the server and agents

` + Bold + Green + "help" + Reset + `
    Display this help menu

` + Bold + Green + "exit" + Reset + `
    Exit the C2 server
`
	return help
}

// FormatPrompt returns a formatted command prompt
func FormatPrompt() string {
	return Red + Bold + "c2" + Reset + Bold + "> " + Reset
}

// FormatInfo formats an informational message
func FormatInfo(message string) string {
	return Blue + "[*] " + Reset + message
}

// FormatSuccess formats a success message
func FormatSuccess(message string) string {
	return Green + "[+] " + Reset + message
}

// FormatError formats an error message
func FormatError(message string) string {
	return Red + "[-] " + Reset + message
}

// FormatWarning formats a warning message
func FormatWarning(message string) string {
	return Yellow + "[!] " + Reset + message
}

// FormatCommand formats a command message
func FormatCommand(message string) string {
	return Magenta + "[>] " + Reset + message
}

// FormatAgentList formats the agent list output in a concise format for red team operations
func FormatAgentList(agents map[string]*Agent) string {
	if len(agents) == 0 {
		return FormatWarning("No agents connected")
	}

	var sb strings.Builder
	sb.WriteString(FormatInfo("Connected Agents: "+fmt.Sprintf("%d total", len(agents))) + "\n")
	sb.WriteString("\n")
	sb.WriteString(Bold + "ID    Name                OS                  Last Seen           Status\n")
	sb.WriteString("-------------------------------------------------------------------------\n" + Reset)

	// Sort agents by last seen time (most recent first)
	type agentWithTime struct {
		id       string
		agent    *Agent
		lastSeen time.Time
	}

	sortedAgents := make([]agentWithTime, 0, len(agents))
	for id, agent := range agents {
		sortedAgents = append(sortedAgents, agentWithTime{id, agent, agent.LastSeen})
	}

	sort.Slice(sortedAgents, func(i, j int) bool {
		return sortedAgents[i].lastSeen.After(sortedAgents[j].lastSeen)
	})

	for _, a := range sortedAgents {
		agent := a.agent

		// Create a short ID (first 6 chars)
		shortID := agent.ID
		if len(shortID) > 6 {
			shortID = shortID[:6]
		}

		// Use alias if available, otherwise use hostname
		displayName := agent.Name
		if agent.Alias != "" {
			displayName = agent.Alias
		}
		if len(displayName) > 18 {
			displayName = displayName[:15] + "..."
		}

		// Extract OS info
		osInfo := "Unknown"
		if agent.OSVersion != "" {
			osInfo = agent.OSVersion
		}
		if len(osInfo) > 20 {
			osInfo = osInfo[:17] + "..."
		}

		// Calculate time since last check-in
		timeSince := time.Since(agent.LastSeen)
		statusColor := Green
		statusText := "ACTIVE"

		// If more than 10 minutes, mark as stale
		if timeSince > 10*time.Minute {
			statusColor = Yellow
			statusText = "STALE"
		}

		// If more than 1 hour, mark as inactive
		if timeSince > 1*time.Hour {
			statusColor = Red
			statusText = "INACTIVE"
		}

		sb.WriteString(fmt.Sprintf("%s%-6s%s %-18s %-20s %-20s %s%s%s\n",
			Cyan, shortID, Reset,
			Yellow+displayName+Reset,
			osInfo,
			agent.LastSeen.Format("2006-01-02 15:04:05"),
			statusColor, statusText, Reset))
	}

	return sb.String()
}

// ClearScreen returns the ANSI escape sequence to clear the screen
func ClearScreen() string {
	return "\033[H\033[2J"
}
