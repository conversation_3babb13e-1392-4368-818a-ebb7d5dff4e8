#!/usr/bin/env python3
"""
Beacon Object File (BOF) Argument Generator

This script helps generate properly formatted arguments for BOFs.
Based on the TrustedSec COFFLOADER project.
"""

import sys
import struct
import binascii

def pack_int(value):
    """Pack an integer argument."""
    return struct.pack("<i", value)

def pack_short(value):
    """Pack a short integer argument."""
    return struct.pack("<h", value)

def pack_string(value):
    """Pack a string argument."""
    if value is None:
        return struct.pack("<i", 0)
    
    encoded_value = value.encode('utf-8')
    return struct.pack("<i", len(encoded_value)) + encoded_value

def pack_wstring(value):
    """Pack a wide (Unicode) string argument."""
    if value is None:
        return struct.pack("<i", 0)
    
    encoded_value = value.encode('utf-16le')
    return struct.pack("<i", len(encoded_value)) + encoded_value

def pack_binary(value):
    """Pack binary data from a hex string."""
    if value is None:
        return struct.pack("<i", 0)
    
    binary_data = binascii.unhexlify(value)
    return struct.pack("<i", len(binary_data)) + binary_data

def generate_args(arg_types, arg_values):
    """Generate formatted arguments for a BOF."""
    if len(arg_types) != len(arg_values):
        print("Error: Number of argument types must match number of values")
        sys.exit(1)
    
    result = b''
    
    for i, arg_type in enumerate(arg_types):
        value = arg_values[i]
        
        if arg_type == 'int':
            result += pack_int(int(value))
        elif arg_type == 'short':
            result += pack_short(int(value))
        elif arg_type == 'string':
            result += pack_string(value)
        elif arg_type == 'wstring':
            result += pack_wstring(value)
        elif arg_type == 'binary':
            result += pack_binary(value)
        else:
            print(f"Error: Unknown argument type '{arg_type}'")
            sys.exit(1)
    
    return result

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python beacon_generate.py <arg_type1> <arg_value1> [<arg_type2> <arg_value2> ...]")
        print("Argument types: int, short, string, wstring, binary")
        print("Example: python beacon_generate.py int 1 string \"Hello, World!\"")
        sys.exit(1)
    
    # Parse arguments
    arg_types = []
    arg_values = []
    
    i = 1
    while i < len(sys.argv):
        if i + 1 < len(sys.argv):
            arg_types.append(sys.argv[i])
            arg_values.append(sys.argv[i + 1])
            i += 2
        else:
            print("Error: Each argument type must have a corresponding value")
            sys.exit(1)
    
    # Generate formatted arguments
    formatted_args = generate_args(arg_types, arg_values)
    
    # Output as a hex string
    hex_string = binascii.hexlify(formatted_args).decode('utf-8')
    print(f"b'{hex_string}'")

if __name__ == "__main__":
    main()
