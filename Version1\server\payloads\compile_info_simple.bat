@echo off
echo Compiling info_simple.c...

REM Check if Visual Studio compiler is available
where cl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Visual Studio compiler (cl.exe) not found.
    echo Trying MinGW compiler...
    
    where gcc.exe >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo MinGW compiler (gcc.exe) not found.
        echo Please install Visual Studio or MinGW to compile this program.
        exit /b 1
    )
    
    echo Compiling with MinGW...
    gcc -o info_simple.exe info_simple.c -lws2_32
) else (
    echo Compiling with Visual Studio...
    cl.exe /nologo /O2 /W4 /Fe:info_simple.exe info_simple.c /link ws2_32.lib
)

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed.
    exit /b 1
)

echo Compilation successful. Created info_simple.exe
echo.
echo You can now use the info_simple.exe payload with the donut command:
echo   donut [agent_id] info_simple.exe
echo.
