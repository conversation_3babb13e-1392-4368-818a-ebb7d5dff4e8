#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <windows.h>

namespace c2 {
namespace coff {

// Execute a COFF/BOF object file
bool executeBOF(const std::vector<uint8_t>& bofBytes, const std::string& entryPoint, const std::string& args, std::string& output);

// Load COFF symbols
bool loadCOFFSymbols(const std::vector<uint8_t>& bofBytes, void** entryPoint);

// Parse arguments for BOF
std::vector<uint8_t> parseBOFArgs(const std::string& args);

} // namespace coff
} // namespace c2
