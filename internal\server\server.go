// Package server provides the core CNC server functionality.
//
// This demonstrates how C2 frameworks coordinate between operators and agents,
// including command queuing, session management, and communication protocols.
package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"educational-cnc/internal/agent"
	"educational-cnc/internal/auth"
	"educational-cnc/internal/crypto"
)

// Config holds the server configuration
type Config struct {
	// Network settings
	HTTPPort int    `json:"http_port"` // Port for agent communication
	SSHPort  int    `json:"ssh_port"`  // Port for operator SSH access
	
	// Security settings
	EncryptionKey crypto.Key `json:"-"` // Encryption key (not serialized)
	
	// Operational settings
	BeaconTimeout   time.Duration `json:"beacon_timeout"`   // How long before agent is stale
	SessionTimeout  time.Duration `json:"session_timeout"`  // How long operator sessions last
	CleanupInterval time.Duration `json:"cleanup_interval"` // How often to clean up expired data
}

// DefaultConfig returns a default server configuration
func DefaultConfig() *Config {
	return &Config{
		HTTPPort:        8080,
		SSHPort:         2222,
		EncryptionKey:   crypto.DefaultKey,
		BeaconTimeout:   10 * time.Minute,
		SessionTimeout:  24 * time.Hour,
		CleanupInterval: 5 * time.Minute,
	}
}

// Server represents the main CNC server
type Server struct {
	config       *Config
	authManager  *auth.AuthManager
	agentManager *agent.AgentManager
	httpServer   *http.Server
	
	// Internal state
	running bool
	mutex   sync.RWMutex
	
	// Channels for graceful shutdown
	shutdown chan struct{}
	done     chan struct{}
}

// NewServer creates a new CNC server instance
func NewServer(config *Config) (*Server, error) {
	// Validate configuration
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}
	
	// Create authentication manager
	authManager, err := auth.NewAuthManager()
	if err != nil {
		return nil, fmt.Errorf("failed to create auth manager: %w", err)
	}
	
	// Create agent manager
	agentManager := agent.NewAgentManager()
	
	server := &Server{
		config:       config,
		authManager:  authManager,
		agentManager: agentManager,
		shutdown:     make(chan struct{}),
		done:         make(chan struct{}),
	}
	
	return server, nil
}

// Start begins the server operations
func (s *Server) Start() error {
	s.mutex.Lock()
	if s.running {
		s.mutex.Unlock()
		return fmt.Errorf("server is already running")
	}
	s.running = true
	s.mutex.Unlock()
	
	log.Printf("Starting Educational CNC Server...")
	log.Printf("HTTP API listening on port %d", s.config.HTTPPort)
	log.Printf("SSH interface listening on port %d", s.config.SSHPort)
	
	// Start HTTP server for agent communication
	if err := s.startHTTPServer(); err != nil {
		return fmt.Errorf("failed to start HTTP server: %w", err)
	}
	
	// Start SSH server for operator interface
	go func() {
		if err := s.startSSHServer(); err != nil {
			log.Printf("SSH server error: %v", err)
		}
	}()
	
	// Start background tasks
	go s.backgroundTasks()
	
	log.Printf("Server started successfully!")
	log.Printf("Connect via SSH: ssh admin@localhost -p %d", s.config.SSHPort)
	log.Printf("Default credentials: admin / admin123")
	
	return nil
}

// Stop gracefully shuts down the server
func (s *Server) Stop() error {
	s.mutex.Lock()
	if !s.running {
		s.mutex.Unlock()
		return fmt.Errorf("server is not running")
	}
	s.running = false
	s.mutex.Unlock()
	
	log.Printf("Shutting down server...")
	
	// Signal shutdown
	close(s.shutdown)
	
	// Stop HTTP server
	if s.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := s.httpServer.Shutdown(ctx); err != nil {
			log.Printf("HTTP server shutdown error: %v", err)
		}
	}
	
	// Wait for background tasks to finish
	select {
	case <-s.done:
		log.Printf("Server stopped gracefully")
	case <-time.After(10 * time.Second):
		log.Printf("Server shutdown timeout")
	}
	
	return nil
}

// IsRunning returns whether the server is currently running
func (s *Server) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.running
}

// GetStats returns server statistics
func (s *Server) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	// Agent statistics
	agentStats := s.agentManager.GetAgentStats()
	stats["agents"] = agentStats
	
	// Server uptime would go here
	stats["uptime"] = "Not implemented yet"
	
	// Memory usage, etc. could be added
	
	return stats
}

// startHTTPServer initializes and starts the HTTP server for agent communication
func (s *Server) startHTTPServer() error {
	mux := http.NewServeMux()
	
	// Register HTTP handlers for agent communication
	s.registerHTTPHandlers(mux)
	
	s.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", s.config.HTTPPort),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
	
	// Start server in goroutine
	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("HTTP server error: %v", err)
		}
	}()
	
	return nil
}

// backgroundTasks runs periodic maintenance tasks
func (s *Server) backgroundTasks() {
	ticker := time.NewTicker(s.config.CleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.shutdown:
			close(s.done)
			return
		case <-ticker.C:
			// Update agent status based on last seen time
			s.agentManager.UpdateAgentStatus()
			
			// Clean up expired sessions
			s.authManager.CleanupExpiredSessions()
			
			// Log statistics periodically
			stats := s.agentManager.GetAgentStats()
			log.Printf("Agent Status: %d total, %d active, %d stale, %d inactive",
				stats["total"], stats["active"], stats["stale"], stats["inactive"])
		}
	}
}

// validateConfig checks if the server configuration is valid
func validateConfig(config *Config) error {
	if config.HTTPPort <= 0 || config.HTTPPort > 65535 {
		return fmt.Errorf("invalid HTTP port: %d", config.HTTPPort)
	}
	
	if config.SSHPort <= 0 || config.SSHPort > 65535 {
		return fmt.Errorf("invalid SSH port: %d", config.SSHPort)
	}
	
	if config.HTTPPort == config.SSHPort {
		return fmt.Errorf("HTTP and SSH ports cannot be the same")
	}
	
	if err := crypto.ValidateKey(config.EncryptionKey); err != nil {
		return fmt.Errorf("invalid encryption key: %w", err)
	}
	
	if config.BeaconTimeout <= 0 {
		return fmt.Errorf("beacon timeout must be positive")
	}
	
	if config.SessionTimeout <= 0 {
		return fmt.Errorf("session timeout must be positive")
	}
	
	if config.CleanupInterval <= 0 {
		return fmt.Errorf("cleanup interval must be positive")
	}
	
	return nil
}

// GetAuthManager returns the authentication manager (for testing/debugging)
func (s *Server) GetAuthManager() *auth.AuthManager {
	return s.authManager
}

// GetAgentManager returns the agent manager (for testing/debugging)
func (s *Server) GetAgentManager() *agent.AgentManager {
	return s.agentManager
}

// GetConfig returns the server configuration
func (s *Server) GetConfig() *Config {
	return s.config
}
