#pragma once

#include <string>
#include <vector>
#include <windows.h>
#include <wincrypt.h>

namespace common {
namespace crypto {

// Base64 encoding and decoding
std::string base64_encode(const std::string& input);
std::string base64_decode(const std::string& input);

// AES encryption and decryption
std::vector<uint8_t> aes_encrypt(const std::vector<uint8_t>& plaintext, const std::vector<uint8_t>& key, const std::vector<uint8_t>& iv);
std::vector<uint8_t> aes_decrypt(const std::vector<uint8_t>& ciphertext, const std::vector<uint8_t>& key, const std::vector<uint8_t>& iv);

// XOR encryption and decryption (fallback)
std::vector<uint8_t> xor_encrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key);
std::vector<uint8_t> xor_decrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key);

// Secure random data generation
std::vector<uint8_t> generate_random(size_t length);

// Hash functions
std::string calculate_sha256(const std::vector<uint8_t>& data);
std::string calculate_md5(const std::vector<uint8_t>& data);

// PKCS7 padding
std::vector<uint8_t> pkcs7_pad(const std::vector<uint8_t>& data, size_t block_size);
std::vector<uint8_t> pkcs7_unpad(const std::vector<uint8_t>& data);

} // namespace crypto
} // namespace common
