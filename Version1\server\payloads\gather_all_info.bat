@echo off
echo System Information Gathering Tool
echo ===============================
echo.

REM Create output directory
if not exist "info_output" mkdir info_output

echo Gathering information using info_simple.exe...
if exist info_simple.exe (
    info_simple.exe > info_output\info_simple_output.txt
    echo - Saved to info_output\info_simple_output.txt
) else (
    echo - info_simple.exe not found. Run compile_info_simple.bat first.
)

echo.
echo Gathering information using info.exe...
if exist info.exe (
    info.exe > info_output\info_output.txt
    echo - Saved to info_output\info_output.txt
) else (
    echo - info.exe not found. Run compile_info.bat first.
)

echo.
echo Gathering information using PowerShell...
powershell -ExecutionPolicy Bypass -File info.ps1 > info_output\info_ps_output.txt
if %ERRORLEVEL% EQU 0 (
    echo - Saved to info_output\info_ps_output.txt
) else (
    echo - PowerShell script execution failed.
)

echo.
echo Information gathering complete.
echo All output files are in the info_output directory.
echo.
