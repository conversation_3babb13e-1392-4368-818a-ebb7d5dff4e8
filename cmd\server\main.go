// Educational CNC Server
//
// This is the main entry point for the educational Command & Control server.
// It demonstrates how C2 frameworks are structured and operated.
//
// EDUCATIONAL USE ONLY - Use responsibly in authorized environments!
package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"educational-cnc/internal/server"
)

func main() {
	// Parse command line flags
	var (
		httpPort = flag.Int("http-port", 8080, "HTTP port for agent communication")
		sshPort  = flag.Int("ssh-port", 2222, "SSH port for operator access")
		help     = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	// Display startup banner
	showBanner()

	// Create server configuration
	config := server.DefaultConfig()
	config.HTTPPort = *httpPort
	config.SSHPort = *sshPort

	// Create and start the server
	srv, err := server.NewServer(config)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Start the server
	if err := srv.Start(); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}

	// Wait for shutdown signal
	waitForShutdown(srv)
}

// showBanner displays the startup banner
func showBanner() {
	banner := `
╔══════════════════════════════════════════════════════════════╗
║                    Educational CNC Framework                 ║
║                     Command & Control Server                 ║
╚══════════════════════════════════════════════════════════════╝

🎓 EDUCATIONAL USE ONLY 🎓
Learn cybersecurity concepts responsibly!

Starting server...
`
	fmt.Print(banner)
}

// showHelp displays usage information
func showHelp() {
	fmt.Println("Educational CNC Server")
	fmt.Println("======================")
	fmt.Println()
	fmt.Println("A minimal Command & Control framework for educational purposes.")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Printf("  %s [options]\n", os.Args[0])
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -http-port int    HTTP port for agent communication (default 8080)")
	fmt.Println("  -ssh-port int     SSH port for operator access (default 2222)")
	fmt.Println("  -help             Show this help message")
	fmt.Println()
	fmt.Println("Default Credentials:")
	fmt.Println("  Username: admin")
	fmt.Println("  Password: admin123")
	fmt.Println()
	fmt.Println("Connect via SSH:")
	fmt.Println("  ssh admin@localhost -p 2222")
	fmt.Println()
	fmt.Println("⚠️  IMPORTANT: Use only in authorized environments!")
}

// waitForShutdown waits for a shutdown signal and gracefully stops the server
func waitForShutdown(srv *server.Server) {
	// Create channel to receive OS signals
	sigChan := make(chan os.Signal, 1)
	
	// Register channel to receive specific signals
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	
	// Wait for signal
	sig := <-sigChan
	log.Printf("Received signal: %v", sig)
	
	// Gracefully shutdown the server
	fmt.Println("\nShutting down server...")
	if err := srv.Stop(); err != nil {
		log.Printf("Error during shutdown: %v", err)
	}
	
	fmt.Println("Server stopped. Goodbye!")
}
