#pragma once

#include <string>
#include <map>
#include <windows.h>
#include <winhttp.h>

namespace c2 {
namespace http {

// HTTP request structure
struct HttpRequest {
    std::wstring url;
    std::wstring method;
    std::wstring userAgent;
    std::wstring contentType;
    std::wstring accept;
    std::wstring acceptEncoding;
    std::wstring acceptLanguage;
    std::wstring cacheControl;
    std::wstring connection;
    std::string data;
    std::wstring cookie;
    std::map<std::wstring, std::wstring> headers; // Additional headers
};

// HTTP response structure
struct HttpResponse {
    DWORD statusCode;
    std::string body;
    bool success;
    std::string error;
};

// Send HTTP request
HttpResponse sendRequest(const HttpRequest& request);

// Parse URL components
bool parseUrl(const std::wstring& url, std::wstring& hostname, std::wstring& path, INTERNET_PORT& port, bool& isHttps);

} // namespace http
} // namespace c2
