// Package agent provides agent management functionality for the educational CNC.
//
// This demonstrates how C2 frameworks track and manage compromised systems,
// including registration, heartbeat monitoring, and task queuing.
package agent

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// Agent represents a compromised system connected to the CNC
type Agent struct {
	// Identification
	ID       string `json:"id"`        // Unique identifier for this agent
	Hostname string `json:"hostname"`  // Computer name of the compromised system
	Alias    string `json:"alias"`     // Friendly name set by operators
	
	// System Information
	OS           string `json:"os"`            // Operating system
	Architecture string `json:"architecture"`  // CPU architecture (x86, x64, arm)
	Username     string `json:"username"`      // Current user context
	ProcessID    int    `json:"process_id"`    // Agent process ID
	
	// Network Information
	InternalIP string `json:"internal_ip"`   // Internal IP address
	ExternalIP string `json:"external_ip"`   // External IP address (if known)
	
	// Status and Timing
	Status       AgentStatus `json:"status"`        // Current agent status
	FirstSeen    time.Time   `json:"first_seen"`    // When agent first connected
	LastSeen     time.Time   `json:"last_seen"`     // Last heartbeat received
	LastCommand  time.Time   `json:"last_command"`  // Last command executed
	
	// Task Management
	PendingTasks []Task       `json:"pending_tasks"` // Commands waiting to be executed
	TaskHistory  []TaskResult `json:"task_history"`  // Results of completed tasks
	
	// Session Management
	SessionID string `json:"session_id"` // Current session identifier
}

// AgentStatus represents the current state of an agent
type AgentStatus string

const (
	StatusActive   AgentStatus = "active"   // Agent is responding to heartbeats
	StatusStale    AgentStatus = "stale"    // Agent hasn't checked in recently
	StatusInactive AgentStatus = "inactive" // Agent appears to be offline
	StatusError    AgentStatus = "error"    // Agent reported an error
)

// Task represents a command to be executed by an agent
type Task struct {
	ID        string            `json:"id"`         // Unique task identifier
	Type      string            `json:"type"`       // Type of task (shell, upload, etc.)
	Command   string            `json:"command"`    // Command to execute
	Arguments map[string]string `json:"arguments"`  // Additional parameters
	CreatedAt time.Time         `json:"created_at"` // When task was created
	CreatedBy string            `json:"created_by"` // Operator who created the task
}

// TaskResult represents the result of an executed task
type TaskResult struct {
	TaskID      string    `json:"task_id"`      // ID of the original task
	Success     bool      `json:"success"`      // Whether task completed successfully
	Output      string    `json:"output"`       // Command output or result data
	Error       string    `json:"error"`        // Error message if task failed
	ExecutedAt  time.Time `json:"executed_at"`  // When task was executed
	Duration    int64     `json:"duration"`     // Execution time in milliseconds
}

// BeaconData represents the data sent by an agent during check-in
type BeaconData struct {
	AgentID     string       `json:"agent_id"`
	Hostname    string       `json:"hostname"`
	SessionID   string       `json:"session_id"`
	Timestamp   time.Time    `json:"timestamp"`
	SystemInfo  SystemInfo   `json:"system_info"`
	TaskResults []TaskResult `json:"task_results,omitempty"`
}

// SystemInfo contains detailed system information from the agent
type SystemInfo struct {
	OS           string `json:"os"`
	Architecture string `json:"architecture"`
	Username     string `json:"username"`
	ProcessID    int    `json:"process_id"`
	InternalIP   string `json:"internal_ip"`
	ExternalIP   string `json:"external_ip"`
}

// AgentManager handles all agent-related operations
type AgentManager struct {
	agents map[string]*Agent
	mutex  sync.RWMutex
}

// NewAgentManager creates a new agent manager
func NewAgentManager() *AgentManager {
	return &AgentManager{
		agents: make(map[string]*Agent),
	}
}

// RegisterAgent registers a new agent or updates an existing one
func (am *AgentManager) RegisterAgent(beacon *BeaconData) *Agent {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	agent, exists := am.agents[beacon.AgentID]
	if !exists {
		// New agent - create it
		agent = &Agent{
			ID:           beacon.AgentID,
			Hostname:     beacon.Hostname,
			OS:           beacon.SystemInfo.OS,
			Architecture: beacon.SystemInfo.Architecture,
			Username:     beacon.SystemInfo.Username,
			ProcessID:    beacon.SystemInfo.ProcessID,
			InternalIP:   beacon.SystemInfo.InternalIP,
			ExternalIP:   beacon.SystemInfo.ExternalIP,
			Status:       StatusActive,
			FirstSeen:    beacon.Timestamp,
			LastSeen:     beacon.Timestamp,
			SessionID:    beacon.SessionID,
			PendingTasks: make([]Task, 0),
			TaskHistory:  make([]TaskResult, 0),
		}
		am.agents[beacon.AgentID] = agent
	} else {
		// Existing agent - update it
		agent.LastSeen = beacon.Timestamp
		agent.SessionID = beacon.SessionID
		agent.Status = StatusActive
		
		// Update system info if it changed
		if beacon.SystemInfo.InternalIP != "" {
			agent.InternalIP = beacon.SystemInfo.InternalIP
		}
		if beacon.SystemInfo.ExternalIP != "" {
			agent.ExternalIP = beacon.SystemInfo.ExternalIP
		}
	}

	// Process any task results
	if len(beacon.TaskResults) > 0 {
		agent.TaskHistory = append(agent.TaskHistory, beacon.TaskResults...)
		agent.LastCommand = beacon.Timestamp
	}

	return agent
}

// GetAgent retrieves an agent by ID
func (am *AgentManager) GetAgent(agentID string) (*Agent, bool) {
	am.mutex.RLock()
	defer am.mutex.RUnlock()
	
	agent, exists := am.agents[agentID]
	return agent, exists
}

// GetAllAgents returns all registered agents
func (am *AgentManager) GetAllAgents() []*Agent {
	am.mutex.RLock()
	defer am.mutex.RUnlock()
	
	agents := make([]*Agent, 0, len(am.agents))
	for _, agent := range am.agents {
		agents = append(agents, agent)
	}
	return agents
}

// FindAgentByPrefix finds an agent by ID prefix or alias
func (am *AgentManager) FindAgentByPrefix(identifier string) (*Agent, error) {
	am.mutex.RLock()
	defer am.mutex.RUnlock()
	
	// First check for exact ID match
	if agent, exists := am.agents[identifier]; exists {
		return agent, nil
	}
	
	// Check for alias match
	for _, agent := range am.agents {
		if agent.Alias == identifier {
			return agent, nil
		}
	}
	
	// Check for ID prefix match
	var matches []*Agent
	for id, agent := range am.agents {
		if len(identifier) <= len(id) && id[:len(identifier)] == identifier {
			matches = append(matches, agent)
		}
	}
	
	if len(matches) == 0 {
		return nil, fmt.Errorf("no agent found with identifier: %s", identifier)
	}
	
	if len(matches) > 1 {
		return nil, fmt.Errorf("multiple agents match identifier: %s", identifier)
	}
	
	return matches[0], nil
}

// QueueTask adds a task to an agent's pending tasks
func (am *AgentManager) QueueTask(agentID string, task Task) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()
	
	agent, exists := am.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}
	
	agent.PendingTasks = append(agent.PendingTasks, task)
	return nil
}

// GetNextTask retrieves and removes the next pending task for an agent
func (am *AgentManager) GetNextTask(agentID string) (*Task, error) {
	am.mutex.Lock()
	defer am.mutex.Unlock()
	
	agent, exists := am.agents[agentID]
	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}
	
	if len(agent.PendingTasks) == 0 {
		return nil, nil // No tasks pending
	}
	
	// Get first task and remove it from queue
	task := agent.PendingTasks[0]
	agent.PendingTasks = agent.PendingTasks[1:]
	
	return &task, nil
}

// SetAlias sets a friendly alias for an agent
func (am *AgentManager) SetAlias(agentID, alias string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()
	
	agent, exists := am.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}
	
	// Check if alias is already in use
	for _, a := range am.agents {
		if a.Alias == alias && a.ID != agentID {
			return fmt.Errorf("alias already in use: %s", alias)
		}
	}
	
	agent.Alias = alias
	return nil
}

// UpdateAgentStatus updates the status of agents based on last seen time
func (am *AgentManager) UpdateAgentStatus() {
	am.mutex.Lock()
	defer am.mutex.Unlock()
	
	now := time.Now()
	for _, agent := range am.agents {
		timeSince := now.Sub(agent.LastSeen)
		
		if timeSince > 1*time.Hour {
			agent.Status = StatusInactive
		} else if timeSince > 10*time.Minute {
			agent.Status = StatusStale
		} else {
			agent.Status = StatusActive
		}
	}
}

// GetAgentStats returns statistics about agents
func (am *AgentManager) GetAgentStats() map[string]int {
	am.mutex.RLock()
	defer am.mutex.RUnlock()
	
	stats := map[string]int{
		"total":    len(am.agents),
		"active":   0,
		"stale":    0,
		"inactive": 0,
	}
	
	for _, agent := range am.agents {
		switch agent.Status {
		case StatusActive:
			stats["active"]++
		case StatusStale:
			stats["stale"]++
		case StatusInactive:
			stats["inactive"]++
		}
	}
	
	return stats
}

// ToJSON converts an agent to JSON for serialization
func (a *Agent) ToJSON() ([]byte, error) {
	return json.Marshal(a)
}

// FromJSON creates an agent from JSON data
func FromJSON(data []byte) (*Agent, error) {
	var agent Agent
	err := json.Unmarshal(data, &agent)
	return &agent, err
}
