#include <windows.h>
#include <stdio.h>
#include <tlhelp32.h>

// Beacon API
typedef struct {
    char * original;
    char * buffer;
    int    length;
    int    size;
} datap;

void BeaconDataParse(datap * parser, char * buffer, int size);
int BeaconDataInt(datap * parser);
short BeaconDataShort(datap * parser);
int BeaconDataLength(datap * parser);
char * BeaconDataExtract(datap * parser, int * size);
void BeaconPrintf(int type, char * fmt, ...);

// System information BOF that demonstrates argument parsing
void go(char *args, int length) {
    datap parser;
    int option = 0;
    
    // Parse arguments
    BeaconDataParse(&parser, args, length);
    option = BeaconDataInt(&parser);
    
    BeaconPrintf(0, "[+] Executing sysinfo BOF with option: %d\n", option);
    
    // Get system information based on the option
    switch (option) {
        case 1: // Basic system info
        {
            SYSTEM_INFO sysInfo;
            GetSystemInfo(&sysInfo);
            
            BeaconPrintf(0, "[+] System Information:\n");
            BeaconPrintf(0, "    Processor Architecture: %d\n", sysInfo.wProcessorArchitecture);
            BeaconPrintf(0, "    Number of Processors: %d\n", sysInfo.dwNumberOfProcessors);
            BeaconPrintf(0, "    Page Size: %d bytes\n", sysInfo.dwPageSize);
            
            MEMORYSTATUSEX memInfo;
            memInfo.dwLength = sizeof(MEMORYSTATUSEX);
            if (GlobalMemoryStatusEx(&memInfo)) {
                BeaconPrintf(0, "    Total Physical Memory: %lld MB\n", memInfo.ullTotalPhys / (1024 * 1024));
                BeaconPrintf(0, "    Available Physical Memory: %lld MB\n", memInfo.ullAvailPhys / (1024 * 1024));
                BeaconPrintf(0, "    Memory Load: %d%%\n", memInfo.dwMemoryLoad);
            }
            break;
        }
        
        case 2: // Process list
        {
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hSnapshot == INVALID_HANDLE_VALUE) {
                BeaconPrintf(0, "[-] Failed to create process snapshot\n");
                break;
            }
            
            PROCESSENTRY32 pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32);
            
            BeaconPrintf(0, "[+] Process List:\n");
            if (Process32First(hSnapshot, &pe32)) {
                do {
                    BeaconPrintf(0, "    PID: %d, Name: %s\n", pe32.th32ProcessID, pe32.szExeFile);
                } while (Process32Next(hSnapshot, &pe32));
            }
            
            CloseHandle(hSnapshot);
            break;
        }
        
        case 3: // Network information
        {
            // This would normally use GetAdaptersInfo or similar
            // For simplicity, we'll just print a message
            BeaconPrintf(0, "[+] Network Information:\n");
            BeaconPrintf(0, "    This functionality requires additional libraries\n");
            break;
        }
        
        default:
            BeaconPrintf(0, "[-] Invalid option. Use 1 for system info, 2 for process list, 3 for network info\n");
            break;
    }
    
    BeaconPrintf(0, "[+] Sysinfo BOF completed successfully\n");
}
