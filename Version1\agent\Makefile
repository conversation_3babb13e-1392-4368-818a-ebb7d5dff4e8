.PHONY: all clean agent bofs coffloader

all: agent bofs

agent: coffloader
	@echo "Building agent..."
	go build -o agent.exe

coffloader:
	@echo "Building COFFLOADER..."
	@mkdir -p bin
	@gcc -c -o bin/COFFLoader.o coffloader/COFFLoader.c
	@gcc -c -o bin/beacon_compatibility.o coffloader/beacon_compatibility.c

# Use MinGW for Windows cross-compilation
bofs:
	@echo "Building BOFs..."
	@mkdir -p bin/bofs
	@for file in bofs/*.c; do \
		echo "Building $$file"; \
		x86_64-w64-mingw32-gcc -c -o bin/bofs/$$(basename $$file .c).o $$file || echo "Failed to build $$file"; \
	done

# Alternative using MSVC if available
bofs-msvc:
	@echo "Building BOFs with MSVC..."
	@mkdir -p bin/bofs
	@for file in bofs/*.c; do \
		echo "Building $$file"; \
		cl.exe /c /GS- /Fo:bin/bofs/$$(basename $$file .c).obj $$file || echo "Failed to build $$file"; \
	done

run: agent
	@echo "Running agent..."
	./agent.exe

clean:
	@echo "Cleaning up..."
	rm -f agent.exe
	rm -rf bin
