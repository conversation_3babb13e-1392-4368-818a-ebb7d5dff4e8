* v0.9.1:
  * Dual-Mode shellcode that can run in either x64 for x86 (WOW64) processes.
  * Automatic detection of the CLR version required for .NET Assembly payloads.
  * AMSI bypassing for version .NET 4.8 that ensure all Assemblies can be safely loaded.
  * Modular system for adding bypasses. Your choide of bypass functionality is compiled into payload.exe based on compiler flags.
  * Bypass for Device Guard policy preventing execution of dynamically generated .NET code
  * Better handling of Main functions (Entry Points) that use an object array containing string arrays, rather than an array of strings
* v1.0:
  * Added module overloading for native PE payloads. Hides them in MEM_IMAGE memory backed by a decoy file on disk
  * Added an option to block indefinitely after running the payload
  * Added Dockerfile to create a docker image for generating donut shellcode
  * Added support for binaries without relocation information or with certain edge cases for relocation information
  * Added custom GetProcAddress and LoadLibrary replacement functions that will only call those Win32 API calls as fallbacks
  * Better documentation for debugging, designing with, and integrating Donut.
  * Added moduler bypass system for ETW
  * Change the -y parameter to be an offset, relative to the base address of the host process's executable. This supports using donut for file infection (like BackdoorFactory).
  * Added option for preserving or overwriting PE headers of native payloads
  * When Module Overloading and PE header overwritten are enabled, the payload's headers will be overwritten by those of the decoy module
  * Added support for HTTP Basic Authentication with remote modules
  * Native PE Section permissions are more accurate
  * Fixed some issues with the MingW makefile (#96)
  * Fixed and improved all makefiles
  * Added an X86 MSVC makefile (Makefile_x86.msvc) for ease of use
  * Fixed the Python module
  * Added an inject_local.exe that runs shellcode in the current process for testing purposes
  * C# output generator
  * Python output generator
  * UUID string output generator
  * NTHeaders->OptionalHeader->ImageBase is now updated correctly
  * Sections' PhysicalAddress is now updated correctly
  * Much more detailed debugging output
  * Better wiping of data in memory to evade scanners
  * Improved error handling
  * Default AppDomain is now used when entropy is disabled
  * Updated reference to go-donut
  * Stack pointer is now correctly aligned (Kyle Willmon)
* v1.02
  * Fix mismatch in handling of IL_ONLY assemblies (jackullrich)
  * Added the '_o_exit' exit API used by cmd.exe on Windows 11 (S4ntiagoP)
  * Re-fix stack pointer alignment (wwh1004)
  * Fixed the description of Offset Execution (-y parameter) to align with version 1.0 updates
  * Fixed exe2h getting a buffer overflow
  * Fixed some circular includes (MEhrn00)
  * Fix mingw gcc builds (MEhrn00)
  * Cast Donut_Create to PyCFunction to fix python module (jsf9k)
  * Added a GitHub Action to build the Python package and push to PyPi
