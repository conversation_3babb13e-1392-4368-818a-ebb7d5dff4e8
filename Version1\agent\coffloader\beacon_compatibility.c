#include <windows.h>
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>

#include "beacon_compatibility.h"

/* data API */
void BeaconDataParse(datap* parser, char* buffer, int size) {
    if (parser == NULL) {
        return;
    }

    parser->original = buffer;
    parser->buffer = buffer;
    parser->length = size;
    parser->size = size;
}

int BeaconDataInt(datap* parser) {
    int32_t value;

    if (parser->length < 4) {
        return 0;
    }

    memcpy(&value, parser->buffer, 4);
    parser->buffer += 4;
    parser->length -= 4;

    return value;
}

short BeaconDataShort(datap* parser) {
    int16_t value;

    if (parser->length < 2) {
        return 0;
    }

    memcpy(&value, parser->buffer, 2);
    parser->buffer += 2;
    parser->length -= 2;

    return value;
}

int BeaconDataLength(datap* parser) {
    return parser->length;
}

char* BeaconDataExtract(datap* parser, int* size) {
    uint32_t length;
    char* data;

    if (parser->length < 4) {
        return NULL;
    }

    memcpy(&length, parser->buffer, 4);
    parser->buffer += 4;
    parser->length -= 4;

    if (parser->length < length) {
        return NULL;
    }

    data = parser->buffer;
    parser->buffer += length;
    parser->length -= length;

    if (size != NULL) {
        *size = length;
    }

    return data;
}

/* format API */
void BeaconFormatAlloc(formatp* format, int maxsz) {
    if (format == NULL) {
        return;
    }

    format->original = (char*)malloc(maxsz);
    format->buffer = format->original;
    format->length = 0;
    format->size = maxsz;

    memset(format->original, 0, maxsz);
}

void BeaconFormatReset(formatp* format) {
    if (format == NULL || format->original == NULL) {
        return;
    }

    memset(format->original, 0, format->size);
    format->buffer = format->original;
    format->length = format->size;
}

void BeaconFormatFree(formatp* format) {
    if (format == NULL || format->original == NULL) {
        return;
    }

    free(format->original);
    format->original = NULL;
    format->buffer = NULL;
    format->length = 0;
    format->size = 0;
}

void BeaconFormatAppend(formatp* format, char* text, int len) {
    if (format == NULL || format->buffer == NULL || text == NULL || len <= 0) {
        return;
    }

    if (format->length + len > format->size) {
        len = format->size - format->length;
    }

    if (len <= 0) {
        return;
    }

    memcpy(format->buffer, text, len);
    format->buffer += len;
    format->length += len;
}

void BeaconFormatPrintf(formatp* format, char* fmt, ...) {
    va_list args;
    int length;
    char buffer[8192];

    if (format == NULL || format->buffer == NULL || fmt == NULL) {
        return;
    }

    va_start(args, fmt);
    length = vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);

    if (length <= 0) {
        return;
    }

    BeaconFormatAppend(format, buffer, length);
}

char* BeaconFormatToString(formatp* format, int* size) {
    if (format == NULL || format->original == NULL) {
        return NULL;
    }

    if (size != NULL) {
        *size = format->length;
    }

    return format->original;
}

void BeaconFormatInt(formatp* format, int value) {
    uint32_t val = value;

    if (format == NULL || format->buffer == NULL) {
        return;
    }

    if (format->length + 4 > format->size) {
        return;
    }

    memcpy(format->buffer, &val, 4);
    format->buffer += 4;
    format->length += 4;
}

/* Output Functions */
void BeaconPrintf(int type, char* fmt, ...) {
    va_list args;
    char buffer[8192];

    va_start(args, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);

    printf("%s", buffer);
}

void BeaconOutput(int type, char* data, int len) {
    if (data == NULL || len <= 0) {
        return;
    }

    printf("%.*s", len, data);
}

/* Token Functions */
BOOL BeaconUseToken(HANDLE token) {
    // Not implemented for standalone BOF execution
    return FALSE;
}

void BeaconRevertToken() {
    // Not implemented for standalone BOF execution
}

BOOL BeaconIsAdmin() {
    // Check if the current process has admin privileges
    BOOL isAdmin = FALSE;
    HANDLE token = NULL;
    TOKEN_ELEVATION elevation;
    DWORD size = 0;

    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &token)) {
        return FALSE;
    }

    if (GetTokenInformation(token, TokenElevation, &elevation, sizeof(elevation), &size)) {
        isAdmin = elevation.TokenIsElevated;
    }

    CloseHandle(token);
    return isAdmin;
}

/* Spawn+Inject Functions */
void BeaconGetSpawnTo(BOOL x86, char* buffer, int length) {
    // Not implemented for standalone BOF execution
    if (buffer != NULL && length > 0) {
        memset(buffer, 0, length);
    }
}

void BeaconInjectProcess(HANDLE hProc, int pid, char* payload, int p_len, int p_offset, char* arg, int a_len) {
    // Not implemented for standalone BOF execution
}

void BeaconInjectTemporaryProcess(PROCESS_INFORMATION* pInfo, char* payload, int p_len, int p_offset, char* arg, int a_len) {
    // Not implemented for standalone BOF execution
}

void BeaconCleanupProcess(PROCESS_INFORMATION* pInfo) {
    // Not implemented for standalone BOF execution
}

/* Utility Functions */
BOOL toWideChar(char* src, wchar_t* dst, int max) {
    if (src == NULL || dst == NULL || max <= 0) {
        return FALSE;
    }

    memset(dst, 0, max * sizeof(wchar_t));
    return (MultiByteToWideChar(CP_ACP, 0, src, -1, dst, max) > 0);
}
