# Beacon Object Files (BOFs)

This directory contains Beacon Object Files (BOFs) that can be executed by the agent using the COFFLOADER library.

## What are BOFs?

Beacon Object Files (BOFs) are small C programs compiled to object files that can be loaded and executed in memory. They provide a way to extend the functionality of the agent without having to modify the agent's code.

BOFs are designed to be small, focused tools that perform specific tasks. They are compiled to object files (.o or .obj) and can be loaded and executed directly in memory without writing to disk, making them stealthy and efficient.

## Included BOFs

### whoami.c

A simple BOF that retrieves and displays information about the current user, including:
- Username
- Computer name
- Admin status

### sysinfo.c

A more complex BOF that demonstrates argument parsing and retrieves system information based on the provided option:
- Option 1: Basic system information (CPU, memory)
- Option 2: Process list
- Option 3: Network information

## Compiling BOFs

To compile a BOF, you need to use a C compiler that can generate object files. For Windows, you can use MinGW or MSVC.

### Using MinGW

```bash
x86_64-w64-mingw32-gcc -c -o whoami.o whoami.c
x86_64-w64-mingw32-gcc -c -o sysinfo.o sysinfo.c
```

### Using MSVC

```bash
cl.exe /c /GS- whoami.c
cl.exe /c /GS- sysinfo.c
```

## BOF Format

BOFs should follow a specific format:

1. They should have a `go` function that serves as the entry point
2. The `go` function should take two parameters: a pointer to arguments and the length of the arguments
3. They should be compiled as object files (.o or .obj)

Example:

```c
void go(char *args, int length) {
    // BOF code here
}
```

## Argument Parsing

BOFs can accept arguments using the Beacon API functions:

```c
// Parse the arguments
datap parser;
BeaconDataParse(&parser, args, length);

// Extract different types of arguments
int intValue = BeaconDataInt(&parser);
short shortValue = BeaconDataShort(&parser);
char *stringValue = BeaconDataExtract(&parser, NULL);
```

## Using BOFs with the Agent

The agent can execute BOFs using the `bof` task type. The format is:

```
bof:path:entryPoint:arg1,arg2,...
```

For example:

```
bof:C:\path\to\whoami.o:go:
```

This will execute the `go` function in the `whoami.o` BOF with no arguments.

For BOFs that accept arguments, you can either:

1. Pass simple comma-separated arguments:

```
bof:C:\path\to\sysinfo.o:go:1
```

2. Use the beacon_generate.py script from the COFFLOADER repository to generate properly formatted arguments:

```
bof:C:\path\to\sysinfo.o:go:b'0400000001000000'
```

The second format is more reliable for complex arguments or when you need to pass binary data.
