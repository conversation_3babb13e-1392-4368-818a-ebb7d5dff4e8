#ifndef DONUT_H
#define DONUT_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// target architecture
#define DONUT_ARCH_X86 1 // x86
#define DONUT_ARCH_X64 2 // AMD64
#define DONUT_ARCH_X84 3 // x86 + AMD64

// module type
#define DONUT_MODULE_NET_DLL 1 // .NET DLL. Requires class and method
#define DONUT_MODULE_NET_EXE 2 // .NET EXE. Executes Main if no class and method provided
#define DONUT_MODULE_DLL    3 // Unmanaged DLL, function is optional
#define DONUT_MODULE_EXE    4 // Unmanaged EXE
#define DONUT_MODULE_VBS    5 // VBScript
#define DONUT_MODULE_JS     6 // JavaScript or JScript

// instance type
#define DONUT_INSTANCE_EMBED 1 // Module is embedded
#define DONUT_INSTANCE_HTTP  2 // Module is downloaded from remote HTTP/HTTPS server

// format type
#define DONUT_FORMAT_BINARY     1 // Binary file
#define DONUT_FORMAT_BASE64     2 // Base64 string
#define DONUT_FORMAT_C          3 // C/C++ source
#define DONUT_FORMAT_RUBY       4 // Ruby source
#define DONUT_FORMAT_PYTHON     5 // Python source
#define DONUT_FORMAT_POWERSHELL 6 // PowerShell source
#define DONUT_FORMAT_CSHARP     7 // C# source
#define DONUT_FORMAT_HEX        8 // Hexadecimal string

// compression engine
#define DONUT_COMPRESS_NONE     1 // No compression
#define DONUT_COMPRESS_APLIB    2 // aPLib compression
#define DONUT_COMPRESS_LZNT1    3 // LZNT1 compression
#define DONUT_COMPRESS_XPRESS   4 // Xpress compression

// entropy level
#define DONUT_ENTROPY_NONE      1 // No entropy
#define DONUT_ENTROPY_RANDOM    2 // Random names
#define DONUT_ENTROPY_DEFAULT   3 // Random names + encryption

// AMSI/WLDP/ETW level
#define DONUT_BYPASS_NONE       1 // Disables bypassing AMSI/WDLP/ETW
#define DONUT_BYPASS_ABORT      2 // If bypassing AMSI/WLDP/ETW fails, the loader stops running
#define DONUT_BYPASS_CONTINUE   3 // If bypassing AMSI/WLDP/ETW fails, the loader continues running

// Preserve PE headers options
#define DONUT_HEADERS_OVERWRITE 1 // Overwrite PE headers
#define DONUT_HEADERS_KEEP      2 // Preserve PE headers

// exit options
#define DONUT_OPT_EXIT_THREAD   1 // Exit thread
#define DONUT_OPT_EXIT_PROCESS  2 // Exit process
#define DONUT_OPT_EXIT_BLOCK    3 // Block indefinitely

typedef struct _DONUT_CONFIG {
    int arch;                   // Target architecture for loader
    int bypass;                 // Bypass AMSI/WLDP/ETW
    int compress;               // Compression engine to use
    int entropy;                // Entropy level
    int format;                 // Output format
    int headers;                // Preserve PE headers
    int exit_opt;               // Exit method
    int thread;                 // Run unmanaged EXE as thread
    int unicode;                // Convert parameters to Unicode
    
    char input[256];            // Input file path
    char output[256];           // Output file path
    
    char runtime[256];          // CLR runtime version
    char domain[256];           // AppDomain name to create
    char cls[256];              // Class name
    char method[256];           // Method or function name
    char args[256];             // Arguments for method or function
    
    char server[256];           // Server for HTTP staging
    char modname[256];          // Module name for HTTP staging
    
    char decoy[256];            // Path of decoy module
    
    uint32_t oep;               // Original entry point of host process
} DONUT_CONFIG, *PDONUT_CONFIG;

#ifdef __cplusplus
extern "C" {
#endif

// Function prototypes
int DonutCreate(PDONUT_CONFIG);
int DonutDelete(PDONUT_CONFIG);
const char* DonutError(int);

#ifdef __cplusplus
}
#endif

#endif // DONUT_H
