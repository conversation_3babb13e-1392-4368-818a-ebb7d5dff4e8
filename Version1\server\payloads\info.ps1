# System Information Gathering Script
# This script collects system information and outputs it in a structured format

function Get-SystemInfo {
    Write-Output "===== SYSTEM RECONNAISSANCE REPORT ====="
    Write-Output "Generated: $(Get-Date)"
    Write-Output ""

    # System Information
    Write-Output "===== SYSTEM INFORMATION ====="
    $computerSystem = Get-CimInstance CIM_ComputerSystem
    $operatingSystem = Get-CimInstance CIM_OperatingSystem
    $bios = Get-CimInstance CIM_BIOSElement
    $processor = Get-CimInstance CIM_Processor

    Write-Output "Computer Name: $($computerSystem.Name)"
    Write-Output "Domain: $($computerSystem.Domain)"
    Write-Output "Manufacturer: $($computerSystem.Manufacturer)"
    Write-Output "Model: $($computerSystem.Model)"
    Write-Output "Operating System: $($operatingSystem.Caption) $($operatingSystem.Version)"
    Write-Output "BIOS Version: $($bios.Name)"
    Write-Output "Processor: $($processor.Name)"
    Write-Output "Total Physical Memory: $([math]::Round($computerSystem.TotalPhysicalMemory / 1GB, 2)) GB"
    Write-Output ""

    # Network Information
    Write-Output "===== NETWORK INFORMATION ====="
    $networkAdapters = Get-CimInstance Win32_NetworkAdapterConfiguration | Where-Object { $_.IPAddress -ne $null }
    
    foreach ($adapter in $networkAdapters) {
        Write-Output "Adapter: $($adapter.Description)"
        Write-Output "  IP Address(es): $($adapter.IPAddress -join ', ')"
        Write-Output "  Subnet Mask(s): $($adapter.IPSubnet -join ', ')"
        Write-Output "  Default Gateway: $($adapter.DefaultIPGateway -join ', ')"
        Write-Output "  DNS Servers: $($adapter.DNSServerSearchOrder -join ', ')"
        Write-Output "  MAC Address: $($adapter.MACAddress)"
        Write-Output ""
    }

    # User Information
    Write-Output "===== USER INFORMATION ====="
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object System.Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
    
    Write-Output "Current User: $($currentUser.Name)"
    Write-Output "User is Administrator: $isAdmin"
    
    Write-Output "`nLocal Users:"
    Get-LocalUser | ForEach-Object {
        Write-Output "  - $($_.Name) (Enabled: $($_.Enabled))"
    }
    Write-Output ""

    # Process Information
    Write-Output "===== PROCESS INFORMATION ====="
    $processes = Get-Process | Sort-Object -Property CPU -Descending | Select-Object -First 20
    
    foreach ($process in $processes) {
        Write-Output "  $($process.ProcessName) (PID: $($process.Id), CPU: $($process.CPU), Memory: $([math]::Round($process.WorkingSet / 1MB, 2)) MB)"
    }
    Write-Output "  ... (showing top 20 by CPU usage)"
    Write-Output ""

    # Installed Software
    Write-Output "===== INSTALLED SOFTWARE ====="
    $software = Get-ItemProperty HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\* | 
                Where-Object { $_.DisplayName -ne $null } | 
                Select-Object DisplayName, DisplayVersion, Publisher |
                Sort-Object DisplayName
    
    $count = 0
    foreach ($app in $software) {
        if ($count -lt 50) {
            Write-Output "  $($app.DisplayName) (v$($app.DisplayVersion)) - $($app.Publisher)"
            $count++
        }
    }
    
    if ($software.Count -gt 50) {
        Write-Output "  ... (more software not shown)"
    }
    Write-Output ""

    # Security Products
    Write-Output "===== SECURITY PRODUCTS ====="
    $antivirusProducts = Get-CimInstance -Namespace root/SecurityCenter2 -ClassName AntivirusProduct -ErrorAction SilentlyContinue
    
    if ($antivirusProducts) {
        foreach ($av in $antivirusProducts) {
            Write-Output "Antivirus: $($av.displayName)"
        }
    } else {
        Write-Output "No antivirus products detected through WMI"
        
        # Check for Windows Defender
        $defenderStatus = Get-MpComputerStatus -ErrorAction SilentlyContinue
        if ($defenderStatus) {
            Write-Output "Windows Defender:"
            Write-Output "  Real-time Protection: $($defenderStatus.RealTimeProtectionEnabled)"
            Write-Output "  Antivirus Enabled: $($defenderStatus.AntivirusEnabled)"
            Write-Output "  Antispyware Enabled: $($defenderStatus.AntispywareEnabled)"
        }
    }
    
    # Firewall Status
    $firewallProfiles = Get-NetFirewallProfile -ErrorAction SilentlyContinue
    if ($firewallProfiles) {
        Write-Output "`nFirewall Status:"
        foreach ($profile in $firewallProfiles) {
            Write-Output "  $($profile.Name) Profile: $($profile.Enabled)"
        }
    }
    Write-Output ""

    # Drives Information
    Write-Output "===== DRIVES INFORMATION ====="
    $drives = Get-PSDrive -PSProvider FileSystem
    foreach ($drive in $drives) {
        $driveInfo = [System.IO.DriveInfo]::GetDrives() | Where-Object { $_.Name -eq "$($drive.Name):\" }
        if ($driveInfo) {
            $totalSize = [math]::Round($driveInfo.TotalSize / 1GB, 2)
            $freeSpace = [math]::Round($driveInfo.AvailableFreeSpace / 1GB, 2)
            $usedSpace = $totalSize - $freeSpace
            $percentUsed = [math]::Round(($usedSpace / $totalSize) * 100, 1)
            
            Write-Output "Drive $($drive.Name): ($($drive.Description))"
            Write-Output "  File System: $($driveInfo.DriveFormat)"
            Write-Output "  Total Size: $totalSize GB"
            Write-Output "  Used Space: $usedSpace GB ($percentUsed%)"
            Write-Output "  Free Space: $freeSpace GB"
            Write-Output ""
        }
    }

    # Environment Variables
    Write-Output "===== ENVIRONMENT VARIABLES ====="
    $envVars = Get-ChildItem Env: | Sort-Object Name
    foreach ($var in $envVars) {
        Write-Output "$($var.Name): $($var.Value)"
    }
    Write-Output ""
}

# Run the function and capture output
Get-SystemInfo
