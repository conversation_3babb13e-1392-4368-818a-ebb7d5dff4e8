#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>

#define INFO_BUFFER_SIZE 8192

int main(int argc, char* argv[]) {
    char buffer[INFO_BUFFER_SIZE] = {0};
    int pos = 0;
    
    // Add header
    pos += sprintf(buffer + pos, "===== BASIC SYSTEM INFORMATION =====\n\n");
    
    // Get computer name
    char computerName[MAX_COMPUTERNAME_LENGTH + 1] = {0};
    DWORD computerNameSize = sizeof(computerName);
    if (GetComputerNameA(computerName, &computerNameSize)) {
        pos += sprintf(buffer + pos, "Computer Name: %s\n", computerName);
    }
    
    // Get username
    char userName[256] = {0};
    DWORD userNameSize = sizeof(userName);
    if (GetUserNameA(userName, &userNameSize)) {
        pos += sprintf(buffer + pos, "Current User: %s\n", userName);
    }
    
    // Get Windows version
    OSVERSIONINFOEX osInfo;
    ZeroMemory(&osInfo, sizeof(OSVERSIONINFOEX));
    osInfo.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
    
    // Note: GetVersionEx is deprecated but still works for basic info
    if (GetVersionEx((OSVERSIONINFO*)&osInfo)) {
        pos += sprintf(buffer + pos, "OS Version: %d.%d (Build %d)\n", 
            osInfo.dwMajorVersion, osInfo.dwMinorVersion, osInfo.dwBuildNumber);
        pos += sprintf(buffer + pos, "Service Pack: %d.%d\n", 
            osInfo.wServicePackMajor, osInfo.wServicePackMinor);
    }
    
    // Get system info
    SYSTEM_INFO sysInfo;
    GetNativeSystemInfo(&sysInfo);
    
    pos += sprintf(buffer + pos, "Processor Architecture: ");
    switch (sysInfo.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            pos += sprintf(buffer + pos, "x64 (AMD or Intel)\n");
            break;
        case PROCESSOR_ARCHITECTURE_INTEL:
            pos += sprintf(buffer + pos, "x86\n");
            break;
        case PROCESSOR_ARCHITECTURE_ARM:
            pos += sprintf(buffer + pos, "ARM\n");
            break;
        case PROCESSOR_ARCHITECTURE_ARM64:
            pos += sprintf(buffer + pos, "ARM64\n");
            break;
        default:
            pos += sprintf(buffer + pos, "Unknown\n");
    }
    
    pos += sprintf(buffer + pos, "Number of Processors: %d\n", sysInfo.dwNumberOfProcessors);
    
    // Get memory info
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memInfo)) {
        pos += sprintf(buffer + pos, "Total Physical Memory: %.2f GB\n", 
            (float)memInfo.ullTotalPhys / (1024 * 1024 * 1024));
        pos += sprintf(buffer + pos, "Available Physical Memory: %.2f GB\n", 
            (float)memInfo.ullAvailPhys / (1024 * 1024 * 1024));
        pos += sprintf(buffer + pos, "Memory Load: %d%%\n", memInfo.dwMemoryLoad);
    }
    
    // Get logical drives
    pos += sprintf(buffer + pos, "\n===== DRIVES =====\n");
    DWORD logicalDrives = GetLogicalDrives();
    if (logicalDrives) {
        for (int i = 0; i < 26; i++) {
            if (logicalDrives & (1 << i)) {
                char driveLetter = 'A' + i;
                char rootPath[4] = {driveLetter, ':', '\\', 0};
                
                UINT driveType = GetDriveTypeA(rootPath);
                char driveTypeStr[20] = {0};
                
                switch (driveType) {
                    case DRIVE_REMOVABLE:
                        strcpy(driveTypeStr, "Removable");
                        break;
                    case DRIVE_FIXED:
                        strcpy(driveTypeStr, "Fixed");
                        break;
                    case DRIVE_REMOTE:
                        strcpy(driveTypeStr, "Network");
                        break;
                    case DRIVE_CDROM:
                        strcpy(driveTypeStr, "CD/DVD");
                        break;
                    case DRIVE_RAMDISK:
                        strcpy(driveTypeStr, "RAM Disk");
                        break;
                    default:
                        strcpy(driveTypeStr, "Unknown");
                }
                
                // Get volume information
                char volumeName[MAX_PATH + 1] = {0};
                char fileSystemName[MAX_PATH + 1] = {0};
                DWORD serialNumber = 0;
                DWORD maxComponentLength = 0;
                DWORD fileSystemFlags = 0;
                
                if (GetVolumeInformationA(rootPath, volumeName, sizeof(volumeName),
                    &serialNumber, &maxComponentLength, &fileSystemFlags,
                    fileSystemName, sizeof(fileSystemName))) {
                    
                    // Get free space
                    ULARGE_INTEGER freeBytesAvailable, totalBytes, totalFreeBytes;
                    if (GetDiskFreeSpaceExA(rootPath, &freeBytesAvailable, &totalBytes, &totalFreeBytes)) {
                        pos += sprintf(buffer + pos, "Drive %s (%s) - %s - %s\n", 
                            rootPath, driveTypeStr, 
                            volumeName[0] ? volumeName : "No Label", 
                            fileSystemName);
                        
                        pos += sprintf(buffer + pos, "  Total: %.2f GB, Free: %.2f GB (%.1f%%)\n",
                            (float)totalBytes.QuadPart / (1024 * 1024 * 1024),
                            (float)totalFreeBytes.QuadPart / (1024 * 1024 * 1024),
                            (float)totalFreeBytes.QuadPart * 100 / totalBytes.QuadPart);
                    } else {
                        pos += sprintf(buffer + pos, "Drive %s (%s) - %s - %s\n", 
                            rootPath, driveTypeStr, 
                            volumeName[0] ? volumeName : "No Label", 
                            fileSystemName);
                    }
                } else {
                    pos += sprintf(buffer + pos, "Drive %s (%s) - Could not get volume info\n", 
                        rootPath, driveTypeStr);
                }
            }
        }
    }
    
    // Get network adapters (simplified)
    pos += sprintf(buffer + pos, "\n===== NETWORK =====\n");
    
    // Get hostname
    char hostname[256] = {0};
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        pos += sprintf(buffer + pos, "Hostname: %s\n", hostname);
    }
    
    // Check if admin
    pos += sprintf(buffer + pos, "\n===== SECURITY =====\n");
    BOOL isAdmin = FALSE;
    SID_IDENTIFIER_AUTHORITY NtAuthority = SECURITY_NT_AUTHORITY;
    PSID AdministratorsGroup;
    
    if (AllocateAndInitializeSid(&NtAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID, DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &AdministratorsGroup)) {
        if (CheckTokenMembership(NULL, AdministratorsGroup, &isAdmin)) {
            pos += sprintf(buffer + pos, "User is Administrator: %s\n", isAdmin ? "Yes" : "No");
        }
        FreeSid(AdministratorsGroup);
    }
    
    // Get environment variables
    pos += sprintf(buffer + pos, "\n===== ENVIRONMENT =====\n");
    
    char* envVars[] = {
        "COMPUTERNAME", "USERNAME", "USERPROFILE", "HOMEDRIVE", "HOMEPATH",
        "APPDATA", "LOCALAPPDATA", "TEMP", "PATH", "PROCESSOR_ARCHITECTURE",
        "NUMBER_OF_PROCESSORS", "PROCESSOR_IDENTIFIER", "OS", "COMSPEC"
    };
    
    for (int i = 0; i < sizeof(envVars) / sizeof(envVars[0]); i++) {
        char* value = getenv(envVars[i]);
        if (value) {
            pos += sprintf(buffer + pos, "%s: %s\n", envVars[i], value);
        }
    }
    
    // Output the information
    printf("%s\n", buffer);
    
    return 0;
}
