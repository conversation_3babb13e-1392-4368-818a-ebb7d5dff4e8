#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>
#include <winsock2.h>
#include <iphlpapi.h>
#include <shlwapi.h>
#include <userenv.h>
#include <lm.h>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "userenv.lib")
#pragma comment(lib, "netapi32.lib")

#define INFO_BUFFER_SIZE 32768
#define MALLOC(x) HeapAlloc(GetProcessHeap(), 0, (x))
#define FREE(x) HeapFree(GetProcessHeap(), 0, (x))

// Function prototypes
void GetSystemInfo(char* buffer, size_t bufferSize);
void GetNetworkInfo(char* buffer, size_t bufferSize);
void GetUserInfo(char* buffer, size_t bufferSize);
void GetProcessInfo(char* buffer, size_t bufferSize);
void GetInstalledSoftware(char* buffer, size_t bufferSize);
void GetAVInfo(char* buffer, size_t bufferSize);
void AppendToBuffer(char* buffer, size_t* currentPos, size_t maxSize, const char* format, ...);

int main(int argc, char* argv[]) {
    char* infoBuffer = (char*)malloc(INFO_BUFFER_SIZE);
    if (infoBuffer == NULL) {
        printf("Memory allocation failed\n");
        return 1;
    }
    
    // Initialize buffer
    memset(infoBuffer, 0, INFO_BUFFER_SIZE);
    size_t bufferPos = 0;
    
    // Add header
    AppendToBuffer(infoBuffer, &bufferPos, INFO_BUFFER_SIZE, 
        "===== SYSTEM RECONNAISSANCE REPORT =====\n"
        "Generated: %s\n\n", 
        __TIMESTAMP__);
    
    // Gather information
    GetSystemInfo(infoBuffer + bufferPos, INFO_BUFFER_SIZE - bufferPos);
    bufferPos = strlen(infoBuffer);
    
    GetNetworkInfo(infoBuffer + bufferPos, INFO_BUFFER_SIZE - bufferPos);
    bufferPos = strlen(infoBuffer);
    
    GetUserInfo(infoBuffer + bufferPos, INFO_BUFFER_SIZE - bufferPos);
    bufferPos = strlen(infoBuffer);
    
    GetProcessInfo(infoBuffer + bufferPos, INFO_BUFFER_SIZE - bufferPos);
    bufferPos = strlen(infoBuffer);
    
    GetInstalledSoftware(infoBuffer + bufferPos, INFO_BUFFER_SIZE - bufferPos);
    bufferPos = strlen(infoBuffer);
    
    GetAVInfo(infoBuffer + bufferPos, INFO_BUFFER_SIZE - bufferPos);
    
    // Output the information
    printf("%s\n", infoBuffer);
    
    // Clean up
    free(infoBuffer);
    return 0;
}

void AppendToBuffer(char* buffer, size_t* currentPos, size_t maxSize, const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    int bytesWritten = vsnprintf(buffer + *currentPos, maxSize - *currentPos, format, args);
    if (bytesWritten > 0) {
        *currentPos += bytesWritten;
    }
    
    va_end(args);
}

void GetSystemInfo(char* buffer, size_t bufferSize) {
    SYSTEM_INFO sysInfo;
    OSVERSIONINFOEX osInfo;
    MEMORYSTATUSEX memInfo;
    DWORD logicalDrives;
    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD computerNameSize = sizeof(computerName);
    
    // Initialize buffer
    size_t currentPos = 0;
    
    // Add section header
    AppendToBuffer(buffer, &currentPos, bufferSize, "===== SYSTEM INFORMATION =====\n");
    
    // Get computer name
    if (GetComputerNameA(computerName, &computerNameSize)) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Computer Name: %s\n", computerName);
    }
    
    // Get system info
    GetNativeSystemInfo(&sysInfo);
    
    // Processor architecture
    AppendToBuffer(buffer, &currentPos, bufferSize, "Processor Architecture: ");
    switch (sysInfo.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            AppendToBuffer(buffer, &currentPos, bufferSize, "x64 (AMD or Intel)\n");
            break;
        case PROCESSOR_ARCHITECTURE_INTEL:
            AppendToBuffer(buffer, &currentPos, bufferSize, "x86\n");
            break;
        case PROCESSOR_ARCHITECTURE_ARM:
            AppendToBuffer(buffer, &currentPos, bufferSize, "ARM\n");
            break;
        case PROCESSOR_ARCHITECTURE_ARM64:
            AppendToBuffer(buffer, &currentPos, bufferSize, "ARM64\n");
            break;
        default:
            AppendToBuffer(buffer, &currentPos, bufferSize, "Unknown\n");
    }
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "Number of Processors: %d\n", sysInfo.dwNumberOfProcessors);
    
    // Get OS version info
    ZeroMemory(&osInfo, sizeof(OSVERSIONINFOEX));
    osInfo.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
    
    // Note: GetVersionEx is deprecated, but still works for basic info
    if (GetVersionEx((OSVERSIONINFO*)&osInfo)) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "OS Version: %d.%d (Build %d)\n", 
            osInfo.dwMajorVersion, osInfo.dwMinorVersion, osInfo.dwBuildNumber);
        AppendToBuffer(buffer, &currentPos, bufferSize, "Service Pack: %d.%d\n", 
            osInfo.wServicePackMajor, osInfo.wServicePackMinor);
    }
    
    // Get memory info
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memInfo)) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Total Physical Memory: %.2f GB\n", 
            (float)memInfo.ullTotalPhys / (1024 * 1024 * 1024));
        AppendToBuffer(buffer, &currentPos, bufferSize, "Available Physical Memory: %.2f GB\n", 
            (float)memInfo.ullAvailPhys / (1024 * 1024 * 1024));
        AppendToBuffer(buffer, &currentPos, bufferSize, "Memory Load: %d%%\n", memInfo.dwMemoryLoad);
    }
    
    // Get logical drives
    logicalDrives = GetLogicalDrives();
    if (logicalDrives) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Logical Drives: ");
        for (int i = 0; i < 26; i++) {
            if (logicalDrives & (1 << i)) {
                AppendToBuffer(buffer, &currentPos, bufferSize, "%c: ", 'A' + i);
            }
        }
        AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
    }
    
    // Get domain information
    char domainName[256] = {0};
    DWORD domainNameSize = sizeof(domainName);
    if (GetComputerNameExA(ComputerNameDnsDomain, domainName, &domainNameSize) && domainNameSize > 0) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Domain: %s\n", domainName);
    } else {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Domain: Not joined to a domain\n");
    }
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
}

void GetNetworkInfo(char* buffer, size_t bufferSize) {
    ULONG outBufLen = 0;
    DWORD dwRetVal = 0;
    
    // Initialize buffer
    size_t currentPos = 0;
    
    // Add section header
    AppendToBuffer(buffer, &currentPos, bufferSize, "===== NETWORK INFORMATION =====\n");
    
    // Initialize Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "WSAStartup failed\n\n");
        return;
    }
    
    // Get hostname
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Hostname: %s\n", hostname);
    }
    
    // Get adapter info
    PIP_ADAPTER_INFO pAdapterInfo = NULL;
    PIP_ADAPTER_INFO pAdapter = NULL;
    
    // First call to GetAdaptersInfo to get the size needed
    GetAdaptersInfo(NULL, &outBufLen);
    pAdapterInfo = (IP_ADAPTER_INFO*)MALLOC(outBufLen);
    
    if (pAdapterInfo) {
        if (GetAdaptersInfo(pAdapterInfo, &outBufLen) == NO_ERROR) {
            pAdapter = pAdapterInfo;
            while (pAdapter) {
                AppendToBuffer(buffer, &currentPos, bufferSize, "\nAdapter Name: %s\n", pAdapter->AdapterName);
                AppendToBuffer(buffer, &currentPos, bufferSize, "Adapter Description: %s\n", pAdapter->Description);
                AppendToBuffer(buffer, &currentPos, bufferSize, "Adapter Address: ");
                for (UINT i = 0; i < pAdapter->AddressLength; i++) {
                    if (i == (pAdapter->AddressLength - 1)) {
                        AppendToBuffer(buffer, &currentPos, bufferSize, "%.2X\n", (int)pAdapter->Address[i]);
                    } else {
                        AppendToBuffer(buffer, &currentPos, bufferSize, "%.2X-", (int)pAdapter->Address[i]);
                    }
                }
                
                AppendToBuffer(buffer, &currentPos, bufferSize, "IP Address: %s\n", pAdapter->IpAddressList.IpAddress.String);
                AppendToBuffer(buffer, &currentPos, bufferSize, "Subnet Mask: %s\n", pAdapter->IpAddressList.IpMask.String);
                AppendToBuffer(buffer, &currentPos, bufferSize, "Gateway: %s\n", pAdapter->GatewayList.IpAddress.String);
                
                if (pAdapter->DhcpEnabled) {
                    AppendToBuffer(buffer, &currentPos, bufferSize, "DHCP Enabled: Yes\n");
                    AppendToBuffer(buffer, &currentPos, bufferSize, "DHCP Server: %s\n", pAdapter->DhcpServer.IpAddress.String);
                } else {
                    AppendToBuffer(buffer, &currentPos, bufferSize, "DHCP Enabled: No\n");
                }
                
                pAdapter = pAdapter->Next;
            }
        }
        FREE(pAdapterInfo);
    }
    
    // Clean up Winsock
    WSACleanup();
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
}

void GetUserInfo(char* buffer, size_t bufferSize) {
    DWORD sessionId = 0;
    DWORD userNameSize = 256;
    DWORD domainNameSize = 256;
    char userName[256] = {0};
    char domainName[256] = {0};
    
    // Initialize buffer
    size_t currentPos = 0;
    
    // Add section header
    AppendToBuffer(buffer, &currentPos, bufferSize, "===== USER INFORMATION =====\n");
    
    // Get current username
    userNameSize = sizeof(userName);
    if (GetUserNameA(userName, &userNameSize)) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Current User: %s\n", userName);
    }
    
    // Check if user is admin
    BOOL isAdmin = FALSE;
    SID_IDENTIFIER_AUTHORITY NtAuthority = SECURITY_NT_AUTHORITY;
    PSID AdministratorsGroup;
    
    if (AllocateAndInitializeSid(&NtAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID, DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &AdministratorsGroup)) {
        if (CheckTokenMembership(NULL, AdministratorsGroup, &isAdmin)) {
            AppendToBuffer(buffer, &currentPos, bufferSize, "User is Administrator: %s\n", isAdmin ? "Yes" : "No");
        }
        FreeSid(AdministratorsGroup);
    }
    
    // Get all user accounts on the system
    LPUSER_INFO_0 pBuf = NULL;
    LPUSER_INFO_0 pTmpBuf;
    DWORD dwLevel = 0;
    DWORD dwPrefMaxLen = MAX_PREFERRED_LENGTH;
    DWORD dwEntriesRead = 0;
    DWORD dwTotalEntries = 0;
    DWORD dwResumeHandle = 0;
    NET_API_STATUS nStatus;
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\nUser Accounts on System:\n");
    
    do {
        nStatus = NetUserEnum(NULL, dwLevel, FILTER_NORMAL_ACCOUNT, (LPBYTE*)&pBuf, dwPrefMaxLen, &dwEntriesRead, &dwTotalEntries, &dwResumeHandle);
        
        if ((nStatus == NERR_Success) || (nStatus == ERROR_MORE_DATA)) {
            if (pBuf == NULL) {
                break;
            }
            
            pTmpBuf = pBuf;
            for (DWORD i = 0; i < dwEntriesRead; i++) {
                if (pTmpBuf == NULL) {
                    break;
                }
                
                AppendToBuffer(buffer, &currentPos, bufferSize, "  - %s\n", pTmpBuf->usri0_name);
                pTmpBuf++;
            }
        }
        
        if (pBuf != NULL) {
            NetApiBufferFree(pBuf);
            pBuf = NULL;
        }
    } while (nStatus == ERROR_MORE_DATA);
    
    if (pBuf != NULL) {
        NetApiBufferFree(pBuf);
    }
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
}

void GetProcessInfo(char* buffer, size_t bufferSize) {
    HANDLE hProcessSnap;
    PROCESSENTRY32 pe32;
    
    // Initialize buffer
    size_t currentPos = 0;
    
    // Add section header
    AppendToBuffer(buffer, &currentPos, bufferSize, "===== PROCESS INFORMATION =====\n");
    
    // Take a snapshot of all processes
    hProcessSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hProcessSnap == INVALID_HANDLE_VALUE) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "CreateToolhelp32Snapshot failed\n\n");
        return;
    }
    
    // Set the size of the structure before using it
    pe32.dwSize = sizeof(PROCESSENTRY32);
    
    // Get the first process
    if (!Process32First(hProcessSnap, &pe32)) {
        CloseHandle(hProcessSnap);
        AppendToBuffer(buffer, &currentPos, bufferSize, "Process32First failed\n\n");
        return;
    }
    
    // List all running processes
    int count = 0;
    do {
        AppendToBuffer(buffer, &currentPos, bufferSize, "  [%d] %s (PID: %d)\n", 
            count++, pe32.szExeFile, pe32.th32ProcessID);
    } while (Process32Next(hProcessSnap, &pe32) && count < 50); // Limit to 50 processes to avoid buffer overflow
    
    if (count >= 50) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "  ... (more processes not shown)\n");
    }
    
    // Clean up the snapshot
    CloseHandle(hProcessSnap);
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
}

void GetInstalledSoftware(char* buffer, size_t bufferSize) {
    HKEY hUninstKey = NULL;
    HKEY hAppKey = NULL;
    WCHAR sAppKeyName[MAX_PATH];
    WCHAR sSubKey[MAX_PATH];
    WCHAR sDisplayName[MAX_PATH];
    WCHAR sVersion[MAX_PATH];
    DWORD dwBufferSize;
    
    // Initialize buffer
    size_t currentPos = 0;
    
    // Add section header
    AppendToBuffer(buffer, &currentPos, bufferSize, "===== INSTALLED SOFTWARE =====\n");
    
    // Open the "Uninstall" key
    if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall", 0, KEY_READ, &hUninstKey) != ERROR_SUCCESS) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Failed to open registry key\n\n");
        return;
    }
    
    // Enumerate all subkeys
    DWORD dwIndex = 0;
    DWORD dwResult;
    DWORD dwSubKeyNameLength = MAX_PATH;
    int count = 0;
    
    while ((dwResult = RegEnumKeyExW(hUninstKey, dwIndex, sAppKeyName, &dwSubKeyNameLength, NULL, NULL, NULL, NULL)) != ERROR_NO_MORE_ITEMS) {
        if (dwResult != ERROR_SUCCESS) {
            dwIndex++;
            dwSubKeyNameLength = MAX_PATH;
            continue;
        }
        
        // Open the subkey
        wcscpy_s(sSubKey, MAX_PATH, L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\");
        wcscat_s(sSubKey, MAX_PATH, sAppKeyName);
        
        if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, sSubKey, 0, KEY_READ, &hAppKey) != ERROR_SUCCESS) {
            dwIndex++;
            dwSubKeyNameLength = MAX_PATH;
            continue;
        }
        
        // Get the display name
        dwBufferSize = sizeof(sDisplayName);
        if (RegQueryValueExW(hAppKey, L"DisplayName", NULL, NULL, (LPBYTE)sDisplayName, &dwBufferSize) == ERROR_SUCCESS) {
            // Get the version
            dwBufferSize = sizeof(sVersion);
            if (RegQueryValueExW(hAppKey, L"DisplayVersion", NULL, NULL, (LPBYTE)sVersion, &dwBufferSize) != ERROR_SUCCESS) {
                wcscpy_s(sVersion, MAX_PATH, L"Unknown");
            }
            
            // Convert to ASCII and append to buffer
            char displayName[MAX_PATH];
            char version[MAX_PATH];
            WideCharToMultiByte(CP_ACP, 0, sDisplayName, -1, displayName, MAX_PATH, NULL, NULL);
            WideCharToMultiByte(CP_ACP, 0, sVersion, -1, version, MAX_PATH, NULL, NULL);
            
            AppendToBuffer(buffer, &currentPos, bufferSize, "  %s (v%s)\n", displayName, version);
            count++;
            
            // Limit to 50 entries to avoid buffer overflow
            if (count >= 50) {
                AppendToBuffer(buffer, &currentPos, bufferSize, "  ... (more software not shown)\n");
                RegCloseKey(hAppKey);
                break;
            }
        }
        
        RegCloseKey(hAppKey);
        dwIndex++;
        dwSubKeyNameLength = MAX_PATH;
    }
    
    RegCloseKey(hUninstKey);
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
}

void GetAVInfo(char* buffer, size_t bufferSize) {
    HKEY hKey;
    char avName[256] = {0};
    DWORD avNameSize = sizeof(avName);
    
    // Initialize buffer
    size_t currentPos = 0;
    
    // Add section header
    AppendToBuffer(buffer, &currentPos, bufferSize, "===== SECURITY PRODUCTS =====\n");
    
    // Check Windows Security Center for AV info
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Security Center\\Monitoring\\AntiVirusProduct", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        if (RegQueryValueExA(hKey, "DisplayName", NULL, NULL, (LPBYTE)avName, &avNameSize) == ERROR_SUCCESS) {
            AppendToBuffer(buffer, &currentPos, bufferSize, "Antivirus: %s\n", avName);
        } else {
            AppendToBuffer(buffer, &currentPos, bufferSize, "Antivirus: Unknown (Windows Security Center entry exists)\n");
        }
        RegCloseKey(hKey);
    } else if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows Defender", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Antivirus: Windows Defender\n");
        RegCloseKey(hKey);
    } else {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Antivirus: Not detected\n");
    }
    
    // Check for firewall
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\StandardProfile", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        DWORD enableFirewall = 0;
        DWORD dataSize = sizeof(DWORD);
        
        if (RegQueryValueExA(hKey, "EnableFirewall", NULL, NULL, (LPBYTE)&enableFirewall, &dataSize) == ERROR_SUCCESS) {
            AppendToBuffer(buffer, &currentPos, bufferSize, "Windows Firewall: %s\n", enableFirewall ? "Enabled" : "Disabled");
        } else {
            AppendToBuffer(buffer, &currentPos, bufferSize, "Windows Firewall: Unknown status\n");
        }
        RegCloseKey(hKey);
    } else {
        AppendToBuffer(buffer, &currentPos, bufferSize, "Windows Firewall: Not detected\n");
    }
    
    AppendToBuffer(buffer, &currentPos, bufferSize, "\n");
}
