// Package crypto provides simple encryption utilities for educational purposes.
// 
// IMPORTANT: This uses XOR encryption which is NOT secure for real-world use!
// It's designed to be simple and easy to understand for learning purposes.
package crypto

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
)

// Key represents an encryption key
type Key []byte

// DefaultKey is a simple key for educational purposes
// In real C2s, keys would be randomly generated and securely distributed
var Default<PERSON><PERSON> = Key("educational-key-123")

// NewRandomKey generates a random key of the specified length
// This demonstrates how real C2s might generate keys
func NewRandomKey(length int) (Key, error) {
	key := make([]byte, length)
	_, err := rand.Read(key)
	if err != nil {
		return nil, fmt.Errorf("failed to generate random key: %w", err)
	}
	return Key(key), nil
}

// XOREncrypt performs XOR encryption on the data
// XOR is used here for educational purposes - it's simple to understand
// but NOT secure for real-world use!
//
// How XOR encryption works:
// 1. Take each byte of data
// 2. XOR it with the corresponding byte of the key (cycling through key)
// 3. The result is the encrypted byte
//
// XOR properties (why it's educational but not secure):
// - Encryption and decryption are the same operation
// - If you know plaintext and ciphertext, you can recover the key
// - Patterns in plaintext show up in ciphertext
func (k Key) XOREncrypt(data []byte) []byte {
	if len(k) == 0 {
		return data // No encryption if no key
	}
	
	result := make([]byte, len(data))
	for i, b := range data {
		// XOR the data byte with the key byte (cycling through key)
		result[i] = b ^ k[i%len(k)]
	}
	return result
}

// XORDecrypt performs XOR decryption (same as encryption due to XOR properties)
func (k Key) XORDecrypt(data []byte) []byte {
	// XOR is symmetric - encryption and decryption are identical
	return k.XOREncrypt(data)
}

// EncryptString encrypts a string and returns base64-encoded result
// This is a convenience function for string data
func (k Key) EncryptString(plaintext string) string {
	encrypted := k.XOREncrypt([]byte(plaintext))
	return base64.StdEncoding.EncodeToString(encrypted)
}

// DecryptString decrypts a base64-encoded string
func (k Key) DecryptString(ciphertext string) (string, error) {
	// Decode from base64
	encrypted, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}
	
	// Decrypt using XOR
	decrypted := k.XORDecrypt(encrypted)
	return string(decrypted), nil
}

// GenerateSessionID creates a random session identifier
// Used for tracking agent sessions
func GenerateSessionID() (string, error) {
	// Generate 16 random bytes
	bytes := make([]byte, 16)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate session ID: %w", err)
	}
	
	// Return as base64 string for easy transmission
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// ValidateKey checks if a key is suitable for use
// In real C2s, this would be much more sophisticated
func ValidateKey(key Key) error {
	if len(key) == 0 {
		return fmt.Errorf("key cannot be empty")
	}
	
	if len(key) < 8 {
		return fmt.Errorf("key too short (minimum 8 bytes)")
	}
	
	// Check for all-zero key (weak)
	allZero := true
	for _, b := range key {
		if b != 0 {
			allZero = false
			break
		}
	}
	if allZero {
		return fmt.Errorf("key cannot be all zeros")
	}
	
	return nil
}

// Example demonstrates how the encryption works
// This is for educational purposes to show the concepts
func Example() {
	fmt.Println("=== Educational Crypto Example ===")
	
	// Create a key
	key := Key("example-key")
	
	// Original message
	message := "Hello, World!"
	fmt.Printf("Original: %s\n", message)
	
	// Encrypt
	encrypted := key.EncryptString(message)
	fmt.Printf("Encrypted (base64): %s\n", encrypted)
	
	// Decrypt
	decrypted, _ := key.DecryptString(encrypted)
	fmt.Printf("Decrypted: %s\n", decrypted)
	
	fmt.Println("\nNote: This XOR encryption is for learning only!")
	fmt.Println("Real C2s use AES, ChaCha20, or other secure algorithms.")
}
