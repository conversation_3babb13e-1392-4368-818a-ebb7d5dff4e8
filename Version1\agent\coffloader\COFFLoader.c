#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <windows.h>

#include "COFFLoader.h"
#include "beacon_compatibility.h"

// Function pointer type for the entry point of a BOF
typedef void (*BofEntryPoint)(char* args, int alen);

// Structure to hold information about a loaded COFF file
typedef struct {
    unsigned char* data;
    uint32_t size;
    void* memory;
    uint32_t memory_size;
    void* entry_point;
} CoffFile;

// Parse the COFF file header
static int ParseCOFFHeader(CoffFile* coff, coff_file_header_t* header) {
    if (coff->size < sizeof(coff_file_header_t)) {
        printf("[-] File too small to be a COFF file\n");
        return 0;
    }

    memcpy(header, coff->data, sizeof(coff_file_header_t));

    if (header->Machine != MACHINETYPE_AMD64) {
        printf("[-] Unsupported machine type: 0x%04x\n", header->Machine);
        return 0;
    }

    return 1;
}

// Allocate memory for the COFF sections
static int AllocateCOFFMemory(CoffFile* coff, coff_file_header_t* header) {
    uint32_t total_size = 0;
    coff_sect_t* sections = (coff_sect_t*)(coff->data + sizeof(coff_file_header_t));

    // Calculate total memory needed
    for (int i = 0; i < header->NumberOfSections; i++) {
        total_size += sections[i].VirtualSize > 0 ? sections[i].VirtualSize : sections[i].SizeOfRawData;
        // Align to 16 bytes
        total_size = (total_size + 15) & ~15;
    }

    // Allocate memory
    coff->memory = VirtualAlloc(NULL, total_size, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (coff->memory == NULL) {
        printf("[-] Failed to allocate memory for COFF sections\n");
        return 0;
    }

    coff->memory_size = total_size;
    return 1;
}

// Load the COFF sections into memory
static int LoadCOFFSections(CoffFile* coff, coff_file_header_t* header) {
    coff_sect_t* sections = (coff_sect_t*)(coff->data + sizeof(coff_file_header_t));
    uint8_t* memory_ptr = (uint8_t*)coff->memory;
    uint32_t offset = 0;

    for (int i = 0; i < header->NumberOfSections; i++) {
        uint32_t size = sections[i].VirtualSize > 0 ? sections[i].VirtualSize : sections[i].SizeOfRawData;
        
        // Copy section data if it exists
        if (sections[i].PointerToRawData > 0 && sections[i].SizeOfRawData > 0) {
            memcpy(memory_ptr + offset, coff->data + sections[i].PointerToRawData, sections[i].SizeOfRawData);
        } else {
            // Zero initialize section
            memset(memory_ptr + offset, 0, size);
        }

        // Update section virtual address to point to our allocated memory
        sections[i].VirtualAddress = offset;

        // Move to next section (aligned to 16 bytes)
        offset += size;
        offset = (offset + 15) & ~15;
    }

    return 1;
}

// Process relocations in the COFF file
static int ProcessCOFFRelocations(CoffFile* coff, coff_file_header_t* header) {
    coff_sect_t* sections = (coff_sect_t*)(coff->data + sizeof(coff_file_header_t));
    uint8_t* memory_base = (uint8_t*)coff->memory;
    
    // Get symbol table
    coff_sym_t* symbols = (coff_sym_t*)(coff->data + header->PointerToSymbolTable);
    
    // Process relocations for each section
    for (int i = 0; i < header->NumberOfSections; i++) {
        if (sections[i].NumberOfRelocations > 0) {
            coff_reloc_t* relocs = (coff_reloc_t*)(coff->data + sections[i].PointerToRelocations);
            
            for (int j = 0; j < sections[i].NumberOfRelocations; j++) {
                coff_reloc_t* reloc = &relocs[j];
                coff_sym_t* symbol = &symbols[reloc->SymbolTableIndex];
                
                // Get the target section and address
                int target_section = symbol->SectionNumber - 1;
                uint64_t target_address = 0;
                
                if (target_section >= 0 && target_section < header->NumberOfSections) {
                    target_address = (uint64_t)(memory_base + sections[target_section].VirtualAddress + symbol->Value);
                } else {
                    // External symbol - try to resolve it
                    char symbol_name[9] = {0};
                    if (symbol->first.Name[0] == 0 && symbol->first.Name[1] == 0 && symbol->first.Name[2] == 0 && symbol->first.Name[3] == 0) {
                        // Symbol name is in the string table
                        uint32_t str_offset = symbol->first.value[1];
                        char* str_table = (char*)(coff->data + header->PointerToSymbolTable + header->NumberOfSymbols * sizeof(coff_sym_t));
                        char* name = str_table + str_offset;
                        
                        // Try to resolve the external symbol
                        HMODULE module = GetModuleHandleA("kernel32.dll");
                        if (module != NULL) {
                            target_address = (uint64_t)GetProcAddress(module, name);
                        }
                        
                        if (target_address == 0) {
                            module = GetModuleHandleA("ntdll.dll");
                            if (module != NULL) {
                                target_address = (uint64_t)GetProcAddress(module, name);
                            }
                        }
                        
                        if (target_address == 0) {
                            printf("[-] Failed to resolve external symbol: %s\n", name);
                            continue;
                        }
                    } else {
                        // Symbol name is in the symbol table
                        memcpy(symbol_name, symbol->first.Name, 8);
                        
                        // Try to resolve the external symbol
                        HMODULE module = GetModuleHandleA("kernel32.dll");
                        if (module != NULL) {
                            target_address = (uint64_t)GetProcAddress(module, symbol_name);
                        }
                        
                        if (target_address == 0) {
                            module = GetModuleHandleA("ntdll.dll");
                            if (module != NULL) {
                                target_address = (uint64_t)GetProcAddress(module, symbol_name);
                            }
                        }
                        
                        if (target_address == 0) {
                            printf("[-] Failed to resolve external symbol: %s\n", symbol_name);
                            continue;
                        }
                    }
                }
                
                // Apply the relocation
                uint8_t* reloc_address = memory_base + sections[i].VirtualAddress + reloc->VirtualAddress;
                
                switch (reloc->Type) {
                    case IMAGE_REL_AMD64_REL32:
                    {
                        // 32-bit relative address from the byte following the relocation
                        uint32_t* rel32 = (uint32_t*)reloc_address;
                        *rel32 = (uint32_t)(target_address - ((uint64_t)reloc_address + 4));
                        break;
                    }
                    case IMAGE_REL_AMD64_ADDR64:
                    {
                        // 64-bit address
                        uint64_t* addr64 = (uint64_t*)reloc_address;
                        *addr64 = target_address;
                        break;
                    }
                    case IMAGE_REL_AMD64_ADDR32NB:
                    {
                        // 32-bit address without image base
                        uint32_t* addr32 = (uint32_t*)reloc_address;
                        *addr32 = (uint32_t)target_address;
                        break;
                    }
                    default:
                        printf("[-] Unsupported relocation type: 0x%04x\n", reloc->Type);
                        break;
                }
            }
        }
    }
    
    return 1;
}

// Find the entry point function in the COFF file
static int FindCOFFEntryPoint(CoffFile* coff, coff_file_header_t* header, const char* function_name) {
    coff_sect_t* sections = (coff_sect_t*)(coff->data + sizeof(coff_file_header_t));
    uint8_t* memory_base = (uint8_t*)coff->memory;
    
    // Get symbol table
    coff_sym_t* symbols = (coff_sym_t*)(coff->data + header->PointerToSymbolTable);
    
    // Find the entry point symbol
    for (int i = 0; i < header->NumberOfSymbols; i++) {
        char symbol_name[9] = {0};
        char* name = NULL;
        
        if (symbols[i].first.Name[0] == 0 && symbols[i].first.Name[1] == 0 && symbols[i].first.Name[2] == 0 && symbols[i].first.Name[3] == 0) {
            // Symbol name is in the string table
            uint32_t str_offset = symbols[i].first.value[1];
            char* str_table = (char*)(coff->data + header->PointerToSymbolTable + header->NumberOfSymbols * sizeof(coff_sym_t));
            name = str_table + str_offset;
        } else {
            // Symbol name is in the symbol table
            memcpy(symbol_name, symbols[i].first.Name, 8);
            name = symbol_name;
        }
        
        if (strcmp(name, function_name) == 0) {
            int section = symbols[i].SectionNumber - 1;
            if (section >= 0 && section < header->NumberOfSections) {
                coff->entry_point = memory_base + sections[section].VirtualAddress + symbols[i].Value;
                return 1;
            }
        }
        
        // Skip auxiliary symbols
        i += symbols[i].NumberOfAuxSymbols;
    }
    
    printf("[-] Entry point function '%s' not found\n", function_name);
    return 0;
}

// Set memory protection for COFF sections
static int SetCOFFMemoryProtection(CoffFile* coff, coff_file_header_t* header) {
    coff_sect_t* sections = (coff_sect_t*)(coff->data + sizeof(coff_file_header_t));
    uint8_t* memory_base = (uint8_t*)coff->memory;
    
    for (int i = 0; i < header->NumberOfSections; i++) {
        DWORD protection = PAGE_READWRITE;
        
        // Determine protection based on section characteristics
        if (sections[i].Characteristics & IMAGE_SCN_MEM_EXECUTE) {
            if (sections[i].Characteristics & IMAGE_SCN_MEM_WRITE) {
                protection = PAGE_EXECUTE_READWRITE;
            } else {
                protection = PAGE_EXECUTE_READ;
            }
        } else if (sections[i].Characteristics & IMAGE_SCN_MEM_WRITE) {
            protection = PAGE_READWRITE;
        } else if (sections[i].Characteristics & IMAGE_SCN_MEM_READ) {
            protection = PAGE_READONLY;
        }
        
        // Set memory protection
        DWORD old_protection;
        uint32_t size = sections[i].VirtualSize > 0 ? sections[i].VirtualSize : sections[i].SizeOfRawData;
        
        if (size > 0) {
            if (!VirtualProtect(memory_base + sections[i].VirtualAddress, size, protection, &old_protection)) {
                printf("[-] Failed to set memory protection for section %d\n", i);
                return 0;
            }
        }
    }
    
    return 1;
}

// Unhexlify a hex string to binary
unsigned char* unhexlify(unsigned char* value, int* outlen) {
    size_t len = strlen((char*)value);
    unsigned char* result = (unsigned char*)malloc(len / 2);
    *outlen = len / 2;
    
    for (size_t i = 0; i < len; i += 2) {
        char byte[3] = {value[i], value[i + 1], 0};
        result[i / 2] = (unsigned char)strtol(byte, NULL, 16);
    }
    
    return result;
}

// Run a COFF file
int RunCOFF(char* functionname, unsigned char* coff_data, uint32_t filesize, unsigned char* argumentdata, int argumentSize) {
    CoffFile coff = {0};
    coff_file_header_t header = {0};
    
    // Initialize the COFF file structure
    coff.data = coff_data;
    coff.size = filesize;
    
    // Parse the COFF header
    if (!ParseCOFFHeader(&coff, &header)) {
        return 0;
    }
    
    // Allocate memory for the COFF sections
    if (!AllocateCOFFMemory(&coff, &header)) {
        return 0;
    }
    
    // Load the COFF sections into memory
    if (!LoadCOFFSections(&coff, &header)) {
        VirtualFree(coff.memory, 0, MEM_RELEASE);
        return 0;
    }
    
    // Process relocations
    if (!ProcessCOFFRelocations(&coff, &header)) {
        VirtualFree(coff.memory, 0, MEM_RELEASE);
        return 0;
    }
    
    // Find the entry point function
    if (!FindCOFFEntryPoint(&coff, &header, functionname)) {
        VirtualFree(coff.memory, 0, MEM_RELEASE);
        return 0;
    }
    
    // Set memory protection for sections
    if (!SetCOFFMemoryProtection(&coff, &header)) {
        VirtualFree(coff.memory, 0, MEM_RELEASE);
        return 0;
    }
    
    // Execute the entry point function
    BofEntryPoint entry = (BofEntryPoint)coff.entry_point;
    entry((char*)argumentdata, argumentSize);
    
    // Clean up
    VirtualFree(coff.memory, 0, MEM_RELEASE);
    
    return 1;
}
