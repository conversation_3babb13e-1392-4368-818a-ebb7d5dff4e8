using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Threading;

namespace HelloDotNet
{
    class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Hello from .NET via Donut-generated shellcode!");
            
            // Print command line arguments
            Console.WriteLine("\nArguments:");
            for (int i = 0; i < args.Length; i++)
            {
                Console.WriteLine($"  [{i}] {args[i]}");
            }
            
            // Get system information
            Console.WriteLine("\nSystem Information:");
            Console.WriteLine($"  OS Version: {Environment.OSVersion}");
            Console.WriteLine($"  64-bit OS: {Environment.Is64BitOperatingSystem}");
            Console.WriteLine($"  64-bit Process: {Environment.Is64BitProcess}");
            Console.WriteLine($"  Processor Count: {Environment.ProcessorCount}");
            Console.WriteLine($"  CLR Version: {Environment.Version}");
            
            // Get current process information
            Process currentProcess = Process.GetCurrentProcess();
            Console.WriteLine("\nProcess Information:");
            Console.WriteLine($"  Process ID: {currentProcess.Id}");
            Console.WriteLine($"  Process Name: {currentProcess.ProcessName}");
            Console.WriteLine($"  Start Time: {currentProcess.StartTime}");
            Console.WriteLine($"  Working Set: {currentProcess.WorkingSet64 / 1024 / 1024} MB");
            
            // Get current user information
            WindowsIdentity identity = WindowsIdentity.GetCurrent();
            Console.WriteLine("\nUser Information:");
            Console.WriteLine($"  Current User: {identity.Name}");
            Console.WriteLine($"  Is Admin: {new WindowsPrincipal(identity).IsInRole(WindowsBuiltInRole.Administrator)}");
            
            // Sleep for a moment to allow output to be seen
            Console.WriteLine("\nSleeping for 5 seconds...");
            Thread.Sleep(5000);
        }
    }
}
