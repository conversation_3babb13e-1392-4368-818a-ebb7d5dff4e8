package coffloader

// #cgo CFLAGS: -I.
// #cgo LDFLAGS: -lntdll -lkernel32
// #include "COFFLoader.h"
// #include "beacon_compatibility.h"
// #include <stdlib.h>
import "C"
import (
	"encoding/hex"
	"fmt"
	"unsafe"
)

// RunCOFF executes a COFF (Beacon Object File) with the specified entry point and arguments
func RunCOFF(functionName string, coffData []byte, arguments []byte) error {
	// Convert function name to C string
	cFunctionName := C.CString(functionName)
	defer C.free(unsafe.Pointer(cFunctionName))

	// Convert COFF data to C bytes
	cCoffData := C.CBytes(coffData)
	defer C.free(cCoffData)

	// Convert arguments to C bytes
	var cArgData unsafe.Pointer
	var argSize C.int
	if len(arguments) > 0 {
		cArgData = C.CBytes(arguments)
		defer C.free(cArgData)
		argSize = C.int(len(arguments))
	} else {
		cArgData = nil
		argSize = 0
	}

	// Run the COFF file
	result := C.RunCOFF(
		cFunctionName,
		(*C.uchar)(cCoffData),
		C.uint32_t(len(coffData)),
		(*C.uchar)(cArgData),
		argSize,
	)

	if result == 0 {
		return fmt.Errorf("failed to run COFF file")
	}

	return nil
}

// UnhexlifyString converts a hex string to binary data
func UnhexlifyString(hexStr string) ([]byte, error) {
	return hex.DecodeString(hexStr)
}
