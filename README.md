# Educational Command & Control (CNC) Framework

## 🎓 Learning Objectives

This is a **minimal, educational** Command & Control framework designed specifically for:
- **Cybersecurity students** learning about C2 infrastructure
- **Red team professionals** understanding C2 fundamentals
- **Security researchers** studying malware communication patterns
- **Educators** teaching cybersecurity concepts

## ⚠️ Educational Use Only

**IMPORTANT**: This framework is designed for educational purposes only. Use only in controlled environments with proper authorization.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Operator      │    │   CNC Server    │    │     Agent       │
│   (SSH CLI)     │◄──►│   (HTTP API)    │◄──►│   (Beacon)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

1. **Authentication & RBAC** - Role-based access control
2. **CNC Server** - Central command and control
3. **Agent Management** - Handle agent registration and tasking
4. **Communication** - Simple HTTP-based protocol
5. **Encryption** - Basic XOR encryption (educational)

## 🚀 Quick Start

### 1. Build the Server
```bash
go build -o cnc-server ./cmd/server
```

### 2. Build the Agent
```bash
go build -o cnc-agent ./cmd/agent
```

### 3. Run the Server
```bash
./cnc-server
```

### 4. Connect as Operator
```bash
ssh admin@localhost -p 2222
# Default password: admin123
```

### 5. Run an Agent
```bash
./cnc-agent
```

## 👥 User Roles

- **Admin**: Full access to all commands and user management
- **Operator**: Can execute commands on agents
- **Viewer**: Read-only access to agent information

## 📚 Learning Path

1. **Start Here**: Read the source code in order of complexity
2. **Basic Concepts**: Understand agent registration and heartbeat
3. **Command Flow**: Follow how commands are queued and executed
4. **Security**: Learn about authentication and encryption
5. **Advanced**: Explore RBAC and session management

## 🔧 Key Features

- ✅ Minimal codebase for easy understanding
- ✅ Well-commented code explaining concepts
- ✅ Role-based access control (RBAC)
- ✅ JWT authentication
- ✅ Simple XOR encryption
- ✅ HTTP-based communication
- ✅ SSH operator interface
- ✅ Agent lifecycle management

## 📖 Documentation

- [Tutorial Guide](docs/TUTORIAL.md) - Step-by-step learning
- [Architecture](docs/ARCHITECTURE.md) - System design
- [Security](docs/SECURITY.md) - Security considerations

## 🛡️ Security Considerations

This framework uses simplified security for educational purposes:
- XOR encryption (easily breakable)
- Simple authentication
- No advanced evasion techniques

**Real-world C2s use much more sophisticated techniques!**

## 🤝 Contributing

This is an educational project. Contributions should focus on:
- Improving code clarity and comments
- Adding educational documentation
- Fixing bugs or security issues
- Adding simple, well-explained features

## 📄 License

MIT License - See LICENSE file for details.

---

**Remember**: Always use this responsibly and only in authorized environments!
