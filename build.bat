@echo off
echo Building Educational CNC Framework...
echo.

echo Building server...
go build -o cnc-server.exe ./cmd/server
if %errorlevel% neq 0 (
    echo Failed to build server
    exit /b 1
)

echo Building agent...
go build -o cnc-agent.exe ./cmd/agent
if %errorlevel% neq 0 (
    echo Failed to build agent
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.
echo To run:
echo   1. Start server: cnc-server.exe
echo   2. Connect via SSH: ssh admin@localhost -p 2222 (password: admin123)
echo   3. Start agent: cnc-agent.exe
echo.
echo ⚠️  EDUCATIONAL USE ONLY ⚠️
