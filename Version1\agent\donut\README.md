# Donut Integration for C2 Agent

This directory contains the integration of the Donut shellcode generator into the C2 agent. Donut allows for converting various Windows payloads (EXE, DLL, .NET assemblies, VBS, JS) into position-independent shellcode that can be executed in memory.

## Overview

Donut provides a more flexible approach to executing payloads compared to BOFs:

1. **Wider Payload Support**: Donut can convert almost any Windows executable format into shellcode, including:
   - Native EXE/DLL files
   - .NET assemblies (both EXE and DLL)
   - VBScript and JavaScript files

2. **Stealth Features**:
   - AMSI/WLDP/ETW bypasses
   - In-memory execution without touching disk
   - PE header manipulation
   - Encryption and obfuscation options

3. **Flexibility**:
   - Can run payloads with command-line arguments
   - Supports both x86 and x64 architectures

## Usage

The agent supports executing payloads using Donut via the `donut` task type:

```
donut:path_to_payload:arguments
```

For example:
```
donut:C:\path\to\payload.exe:arg1 arg2
```

## Implementation Details

The integration consists of:

1. **donut.h**: C header file defining the Donut API structure (simplified version)
2. **donut.go**: Go wrapper that calls the Donut executable
3. **shellcode.go**: Functions for executing shellcode in memory

### Workflow

1. The agent receives a `donut` task
2. The payload file path and arguments are passed to the Donut executable
3. Donut generates shellcode and saves it to a temporary file
4. The agent reads the shellcode from the temporary file
5. The shellcode is executed in memory using VirtualAlloc/CreateThread

### Requirements

To use the Donut functionality:

1. Install the Donut executable:
   - Download from https://github.com/TheWover/donut/releases
   - Add the Donut executable to your PATH

2. Build the agent:
   ```
   cd agent
   go build -o agent.exe
   ```

### Testing

A PowerShell script is provided to test the Donut functionality:

```
.\agent\donut\examples\test_donut.ps1
```

## Comparison with BOFs

| Feature | BOFs | Donut |
|---------|------|-------|
| Payload Types | Small C programs compiled to object files | EXE, DLL, .NET, VBS, JS |
| Size | Very small | Can be larger |
| Complexity | Simple | More complex |
| Flexibility | Limited | Very flexible |
| Dependencies | Minimal | More dependencies |

## Security Considerations

- Donut-generated shellcode may be detected by some security products
- The shellcode execution technique (VirtualAlloc + CreateThread) is well-known
- Consider implementing additional obfuscation or evasion techniques

## References

- [Donut GitHub Repository](https://github.com/TheWover/donut)
- [Writing Beacon Object Files](https://www.coresecurity.com/core-labs/articles/writing-beacon-object-files-flexibie-stealthy-and-compatible)
