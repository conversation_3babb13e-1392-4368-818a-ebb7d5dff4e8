#pragma once

#include <string>
#include <map>
#include <vector>
#include <windows.h>
#include <winhttp.h>

namespace common {
namespace http {

// HTTP request structure
struct HttpRequest {
    std::wstring url;
    std::wstring method;
    std::wstring userAgent;
    std::wstring contentType;
    std::wstring accept;
    std::wstring acceptEncoding;
    std::wstring acceptLanguage;
    std::wstring cacheControl;
    std::wstring connection;
    std::string data;
    std::wstring cookie;
    std::map<std::wstring, std::wstring> headers;
};

// HTTP response structure
struct HttpResponse {
    bool success;
    int statusCode;
    std::string body;
    std::string error;
    std::map<std::string, std::string> headers;
};

// Send HTTP request
HttpResponse sendRequest(const HttpRequest& request);

// Parse URL
bool parseUrl(const std::wstring& url, std::wstring& hostname, std::wstring& path, INTERNET_PORT& port, bool& secure);

// Convert between string and wstring
std::wstring stringToWstring(const std::string& str);
std::string wstringToString(const std::wstring& wstr);

// Download file to memory
std::vector<uint8_t> downloadFile(const std::wstring& url);

// Encode URL parameters
std::wstring urlEncode(const std::wstring& value);

} // namespace http
} // namespace common
