// Package auth provides authentication and role-based access control (RBAC)
// for the educational CNC framework.
//
// This demonstrates how real C2 frameworks implement user management and
// access control to ensure only authorized operators can execute commands.
package auth

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"sync"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// Role represents a user role in the system
type Role string

const (
	// RoleAdmin has full access to all commands and user management
	RoleAdmin Role = "admin"

	// RoleOperator can execute commands on agents but cannot manage users
	RoleOperator Role = "operator"

	// RoleViewer can only view agent information, no command execution
	RoleViewer Role = "viewer"
)

// User represents a system user with authentication and authorization info
type User struct {
	Username     string    `json:"username"`
	PasswordHash string    `json:"password_hash"`
	Role         Role      `json:"role"`
	CreatedAt    time.Time `json:"created_at"`
	LastLogin    time.Time `json:"last_login"`
	Active       bool      `json:"active"`
}

// Session represents an active user session
type Session struct {
	SessionID string    `json:"session_id"`
	Username  string    `json:"username"`
	Role      Role      `json:"role"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

// AuthManager handles authentication and authorization
type AuthManager struct {
	users    map[string]*User
	sessions map[string]*Session
	mutex    sync.RWMutex
	jwtKey   []byte
}

// Command permissions define what each role can do
var rolePermissions = map[Role][]string{
	RoleAdmin: {
		"list", "agents", "shell", "task", "results", "clear-results",
		"alias", "debug", "help", "clear", "users", "adduser", "deluser",
		"passwd", "promote", "demote",
	},
	RoleOperator: {
		"list", "agents", "shell", "task", "results", "clear-results",
		"alias", "debug", "help", "clear",
	},
	RoleViewer: {
		"list", "agents", "results", "debug", "help", "clear",
	},
}

// NewAuthManager creates a new authentication manager
func NewAuthManager() (*AuthManager, error) {
	// Generate a random JWT signing key
	jwtKey := make([]byte, 32)
	if _, err := rand.Read(jwtKey); err != nil {
		return nil, fmt.Errorf("failed to generate JWT key: %w", err)
	}

	am := &AuthManager{
		users:    make(map[string]*User),
		sessions: make(map[string]*Session),
		jwtKey:   jwtKey,
	}

	// Create default admin user
	if err := am.createDefaultAdmin(); err != nil {
		return nil, fmt.Errorf("failed to create default admin: %w", err)
	}

	return am, nil
}

// createDefaultAdmin creates the default admin user for initial access
func (am *AuthManager) createDefaultAdmin() error {
	// Hash the default password
	hash, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	admin := &User{
		Username:     "admin",
		PasswordHash: string(hash),
		Role:         RoleAdmin,
		CreatedAt:    time.Now(),
		Active:       true,
	}

	am.users["admin"] = admin
	return nil
}

// Authenticate verifies user credentials and returns a session
func (am *AuthManager) Authenticate(username, password string) (*Session, error) {
	am.mutex.RLock()
	user, exists := am.users[username]
	am.mutex.RUnlock()

	if !exists || !user.Active {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Generate session ID
	sessionBytes := make([]byte, 16)
	if _, err := rand.Read(sessionBytes); err != nil {
		return nil, fmt.Errorf("failed to generate session: %w", err)
	}
	sessionID := hex.EncodeToString(sessionBytes)

	// Create session
	session := &Session{
		SessionID: sessionID,
		Username:  username,
		Role:      user.Role,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24 hour sessions
	}

	// Store session
	am.mutex.Lock()
	am.sessions[sessionID] = session
	user.LastLogin = time.Now()
	am.mutex.Unlock()

	return session, nil
}

// ValidateSession checks if a session is valid and returns the user info
func (am *AuthManager) ValidateSession(sessionID string) (*Session, error) {
	am.mutex.RLock()
	session, exists := am.sessions[sessionID]
	am.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("invalid session")
	}

	if time.Now().After(session.ExpiresAt) {
		// Session expired, clean it up
		am.mutex.Lock()
		delete(am.sessions, sessionID)
		am.mutex.Unlock()
		return nil, fmt.Errorf("session expired")
	}

	return session, nil
}

// HasPermission checks if a user role has permission to execute a command
func (am *AuthManager) HasPermission(role Role, command string) bool {
	permissions, exists := rolePermissions[role]
	if !exists {
		return false
	}

	for _, perm := range permissions {
		if perm == command {
			return true
		}
	}
	return false
}

// AddUser creates a new user (admin only)
func (am *AuthManager) AddUser(username, password string, role Role) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	// Check if user already exists
	if _, exists := am.users[username]; exists {
		return fmt.Errorf("user already exists")
	}

	// Validate role
	if role != RoleAdmin && role != RoleOperator && role != RoleViewer {
		return fmt.Errorf("invalid role")
	}

	// Hash password
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &User{
		Username:     username,
		PasswordHash: string(hash),
		Role:         role,
		CreatedAt:    time.Now(),
		Active:       true,
	}

	am.users[username] = user
	return nil
}

// DeleteUser removes a user (admin only)
func (am *AuthManager) DeleteUser(username string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	// Cannot delete admin user
	if username == "admin" {
		return fmt.Errorf("cannot delete admin user")
	}

	// Check if user exists
	if _, exists := am.users[username]; !exists {
		return fmt.Errorf("user not found")
	}

	delete(am.users, username)

	// Invalidate all sessions for this user
	for sessionID, session := range am.sessions {
		if session.Username == username {
			delete(am.sessions, sessionID)
		}
	}

	return nil
}

// ChangePassword changes a user's password
func (am *AuthManager) ChangePassword(username, newPassword string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	user, exists := am.users[username]
	if !exists {
		return fmt.Errorf("user not found")
	}

	// Hash new password
	hash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	user.PasswordHash = string(hash)
	return nil
}

// ListUsers returns all users (admin only)
func (am *AuthManager) ListUsers() []*User {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	users := make([]*User, 0, len(am.users))
	for _, user := range am.users {
		// Don't include password hash in the response
		userCopy := *user
		userCopy.PasswordHash = ""
		users = append(users, &userCopy)
	}

	return users
}

// Logout invalidates a session
func (am *AuthManager) Logout(sessionID string) {
	am.mutex.Lock()
	defer am.mutex.Unlock()
	delete(am.sessions, sessionID)
}

// CleanupExpiredSessions removes expired sessions
func (am *AuthManager) CleanupExpiredSessions() {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	now := time.Now()
	for sessionID, session := range am.sessions {
		if now.After(session.ExpiresAt) {
			delete(am.sessions, sessionID)
		}
	}
}

// GetUserRole returns the role of a user
func (am *AuthManager) GetUserRole(username string) (Role, error) {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	user, exists := am.users[username]
	if !exists {
		return "", fmt.Errorf("user not found")
	}

	return user.Role, nil
}

// FormatRolePermissions returns a formatted string of role permissions
func FormatRolePermissions() string {
	var sb strings.Builder
	sb.WriteString("Role Permissions:\n\n")

	for role, perms := range rolePermissions {
		sb.WriteString(fmt.Sprintf("%s:\n", strings.ToUpper(string(role))))
		for _, perm := range perms {
			sb.WriteString(fmt.Sprintf("  - %s\n", perm))
		}
		sb.WriteString("\n")
	}

	return sb.String()
}
