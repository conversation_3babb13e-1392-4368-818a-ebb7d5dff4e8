#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <windows.h>
#include <wincrypt.h>

namespace c2 {
namespace crypto {

// Base64 encoding/decoding
std::string base64_encode(const std::string& input);
std::string base64_decode(const std::string& input);

// XOR encryption/decryption (same function for both)
std::string xor_encrypt(const std::string& data, const std::string& key);

// AES encryption/decryption
std::string aes_encrypt(const std::string& plaintext, const std::string& key, const std::string& iv);
std::string aes_decrypt(const std::string& ciphertext, const std::string& key, const std::string& iv);

// Utility functions
std::vector<uint8_t> string_to_bytes(const std::string& str);
std::string bytes_to_string(const std::vector<uint8_t>& bytes);

} // namespace crypto
} // namespace c2
