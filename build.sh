#!/bin/bash

echo "Building Educational CNC Framework..."
echo

echo "Building server..."
go build -o cnc-server ./cmd/server
if [ $? -ne 0 ]; then
    echo "Failed to build server"
    exit 1
fi

echo "Building agent..."
go build -o cnc-agent ./cmd/agent
if [ $? -ne 0 ]; then
    echo "Failed to build agent"
    exit 1
fi

echo
echo "✅ Build completed successfully!"
echo
echo "To run:"
echo "  1. Start server: ./cnc-server"
echo "  2. Connect via SSH: ssh admin@localhost -p 2222 (password: admin123)"
echo "  3. Start agent: ./cnc-agent"
echo
echo "⚠️  EDUCATIONAL USE ONLY ⚠️"
