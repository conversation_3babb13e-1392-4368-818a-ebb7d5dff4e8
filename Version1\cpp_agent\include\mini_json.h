#pragma once

#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <iomanip>
#include <cstdint>

namespace c2 {
namespace json {

// Forward declarations
class Value;

// JSON value types
enum class ValueType {
    Null,
    Boolean,
    Number,
    String,
    Array,
    Object
};

// JSON value class
class Value {
public:
    // Constructors
    Value() : type(ValueType::Null) {}
    Value(bool value) : type(ValueType::Boolean), boolValue(value) {}
    Value(int value) : type(ValueType::Number), numberValue(static_cast<double>(value)) {}
    Value(double value) : type(ValueType::Number), numberValue(value) {}
    Value(const std::string& value) : type(ValueType::String), stringValue(value) {}
    Value(const char* value) : type(ValueType::String), stringValue(value) {}

    // Type checking
    bool isNull() const { return type == ValueType::Null; }
    bool isBoolean() const { return type == ValueType::Boolean; }
    bool isNumber() const { return type == ValueType::Number; }
    bool isString() const { return type == ValueType::String; }
    bool isArray() const { return type == ValueType::Array; }
    bool isObject() const { return type == ValueType::Object; }

    // Value getters
    bool asBool() const { return boolValue; }
    double asNumber() const { return numberValue; }
    std::string asString() const { return stringValue; }

    // Array operations
    void push_back(const Value& value) {
        if (type != ValueType::Array) {
            type = ValueType::Array;
            arrayValues.clear();
        }
        arrayValues.push_back(value);
    }

    // Object operations
    Value& operator[](const std::string& key) {
        if (type != ValueType::Object) {
            type = ValueType::Object;
            objectValues.clear();
        }
        return objectValues[key];
    }

    bool contains(const std::string& key) const {
        if (type != ValueType::Object) return false;
        return objectValues.find(key) != objectValues.end();
    }

    // Serialization
    std::string dump() const {
        std::stringstream ss;
        serialize(ss);
        return ss.str();
    }

private:
    ValueType type;
    bool boolValue = false;
    double numberValue = 0.0;
    std::string stringValue;
    std::vector<Value> arrayValues;
    std::map<std::string, Value> objectValues;

    void serialize(std::stringstream& ss) const {
        switch (type) {
            case ValueType::Null:
                ss << "null";
                break;
            case ValueType::Boolean:
                ss << (boolValue ? "true" : "false");
                break;
            case ValueType::Number:
                ss << numberValue;
                break;
            case ValueType::String:
                ss << "\"" << escapeString(stringValue) << "\"";
                break;
            case ValueType::Array:
                ss << "[";
                for (size_t i = 0; i < arrayValues.size(); ++i) {
                    if (i > 0) ss << ",";
                    arrayValues[i].serialize(ss);
                }
                ss << "]";
                break;
            case ValueType::Object:
                ss << "{";
                bool first = true;
                for (const auto& pair : objectValues) {
                    if (!first) ss << ",";
                    first = false;
                    ss << "\"" << escapeString(pair.first) << "\":";
                    pair.second.serialize(ss);
                }
                ss << "}";
                break;
        }
    }

    std::string escapeString(const std::string& str) const {
        std::stringstream ss;
        for (char c : str) {
            switch (c) {
                case '\"': ss << "\\\""; break;
                case '\\': ss << "\\\\"; break;
                case '\b': ss << "\\b"; break;
                case '\f': ss << "\\f"; break;
                case '\n': ss << "\\n"; break;
                case '\r': ss << "\\r"; break;
                case '\t': ss << "\\t"; break;
                default:
                    if (c < 32) {
                        ss << "\\u" << std::hex << std::setw(4) << std::setfill('0') << static_cast<int>(c);
                    } else {
                        ss << c;
                    }
            }
        }
        return ss.str();
    }
};

// Simple JSON parser
class Parser {
public:
    static Value parse(const std::string& json) {
        size_t pos = 0;
        return parseValue(json, pos);
    }

private:
    static Value parseValue(const std::string& json, size_t& pos) {
        skipWhitespace(json, pos);

        if (pos >= json.length()) {
            return Value();
        }

        char c = json[pos];

        if (c == 'n') {
            // Parse null
            if (pos + 4 <= json.length() && json.substr(pos, 4) == "null") {
                pos += 4;
                return Value();
            }
        } else if (c == 't') {
            // Parse true
            if (pos + 4 <= json.length() && json.substr(pos, 4) == "true") {
                pos += 4;
                return Value(true);
            }
        } else if (c == 'f') {
            // Parse false
            if (pos + 5 <= json.length() && json.substr(pos, 5) == "false") {
                pos += 5;
                return Value(false);
            }
        } else if (c == '"') {
            // Parse string
            return parseString(json, pos);
        } else if (c == '[') {
            // Parse array
            return parseArray(json, pos);
        } else if (c == '{') {
            // Parse object
            return parseObject(json, pos);
        } else if ((c >= '0' && c <= '9') || c == '-') {
            // Parse number
            return parseNumber(json, pos);
        }

        // Invalid JSON
        return Value();
    }

    static void skipWhitespace(const std::string& json, size_t& pos) {
        while (pos < json.length() && (json[pos] == ' ' || json[pos] == '\t' || json[pos] == '\n' || json[pos] == '\r')) {
            pos++;
        }
    }

    static Value parseString(const std::string& json, size_t& pos) {
        pos++; // Skip opening quote
        std::string str;

        while (pos < json.length() && json[pos] != '"') {
            if (json[pos] == '\\' && pos + 1 < json.length()) {
                pos++;
                switch (json[pos]) {
                    case '"': str += '"'; break;
                    case '\\': str += '\\'; break;
                    case '/': str += '/'; break;
                    case 'b': str += '\b'; break;
                    case 'f': str += '\f'; break;
                    case 'n': str += '\n'; break;
                    case 'r': str += '\r'; break;
                    case 't': str += '\t'; break;
                    default: str += json[pos];
                }
            } else {
                str += json[pos];
            }
            pos++;
        }

        if (pos < json.length()) {
            pos++; // Skip closing quote
        }

        return Value(str);
    }

    static Value parseNumber(const std::string& json, size_t& pos) {
        size_t start = pos;

        // Skip sign
        if (json[pos] == '-') {
            pos++;
        }

        // Skip digits
        while (pos < json.length() && json[pos] >= '0' && json[pos] <= '9') {
            pos++;
        }

        // Skip decimal point and fraction
        if (pos < json.length() && json[pos] == '.') {
            pos++;
            while (pos < json.length() && json[pos] >= '0' && json[pos] <= '9') {
                pos++;
            }
        }

        // Skip exponent
        if (pos < json.length() && (json[pos] == 'e' || json[pos] == 'E')) {
            pos++;
            if (pos < json.length() && (json[pos] == '+' || json[pos] == '-')) {
                pos++;
            }
            while (pos < json.length() && json[pos] >= '0' && json[pos] <= '9') {
                pos++;
            }
        }

        std::string numStr = json.substr(start, pos - start);
        return Value(std::stod(numStr));
    }

    static Value parseArray(const std::string& json, size_t& pos) {
        pos++; // Skip opening bracket
        Value array;

        skipWhitespace(json, pos);

        if (pos < json.length() && json[pos] == ']') {
            pos++; // Skip closing bracket
            return array;
        }

        while (pos < json.length()) {
            Value element = parseValue(json, pos);
            array.push_back(element);

            skipWhitespace(json, pos);

            if (pos < json.length() && json[pos] == ']') {
                pos++; // Skip closing bracket
                break;
            }

            if (pos < json.length() && json[pos] == ',') {
                pos++; // Skip comma
                skipWhitespace(json, pos);
            } else {
                break;
            }
        }

        return array;
    }

    static Value parseObject(const std::string& json, size_t& pos) {
        pos++; // Skip opening brace
        Value object;

        skipWhitespace(json, pos);

        if (pos < json.length() && json[pos] == '}') {
            pos++; // Skip closing brace
            return object;
        }

        while (pos < json.length()) {
            skipWhitespace(json, pos);

            if (pos < json.length() && json[pos] != '"') {
                break;
            }

            Value key = parseString(json, pos);

            skipWhitespace(json, pos);

            if (pos < json.length() && json[pos] == ':') {
                pos++; // Skip colon
            } else {
                break;
            }

            skipWhitespace(json, pos);

            Value value = parseValue(json, pos);
            object[key.asString()] = value;

            skipWhitespace(json, pos);

            if (pos < json.length() && json[pos] == '}') {
                pos++; // Skip closing brace
                break;
            }

            if (pos < json.length() && json[pos] == ',') {
                pos++; // Skip comma
                skipWhitespace(json, pos);
            } else {
                break;
            }
        }

        return object;
    }
};

} // namespace json
} // namespace c2
