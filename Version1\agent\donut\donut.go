package donut

import (
	"fmt"
	"os"
	"os/exec"
)

// Config represents the configuration for Donut shellcode generation
type Config struct {
	// Target architecture for loader
	Arch int
	// Bypass AMSI/WLDP/ETW
	Bypass int
	// Compression engine to use
	Compress int
	// Entropy level
	Entropy int
	// Output format
	Format int
	// Preserve PE headers
	Headers int
	// Exit method
	ExitOpt int
	// Run unmanaged EXE as thread
	Thread int
	// Convert parameters to Unicode
	Unicode int

	// Input file path
	Input string
	// Output file path
	Output string

	// CLR runtime version
	Runtime string
	// AppDomain name to create
	Domain string
	// Class name
	Class string
	// Method or function name
	Method string
	// Arguments for method or function
	Args string

	// Server for HTTP staging
	Server string
	// Module name for HTTP staging
	ModuleName string

	// Path of decoy module
	Decoy string

	// Original entry point of host process
	OEP uint32
}

// DefaultConfig returns a Config with default values
func DefaultConfig() Config {
	return Config{
		Arch:     ArchX84,
		Bypass:   BypassContinue,
		Compress: CompressNone,
		Entropy:  EntropyDefault,
		Format:   FormatBinary,
		Headers:  HeadersOverwrite,
		ExitOpt:  ExitThread,
		Thread:   0,
		Unicode:  0,
		Output:   "loader.bin",
	}
}

// Create generates shellcode using Donut with the provided configuration
func Create(config Config) ([]byte, error) {
	// For now, we'll use a simplified approach since we don't have the full Donut library integrated
	// In a real implementation, we would call the Donut C library to generate shellcode

	// Create a temporary file to store the shellcode
	tempFile, err := os.CreateTemp("", "donut-*.bin")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp file: %w", err)
	}
	defer os.Remove(tempFile.Name())
	tempFile.Close()

	// Build the donut command
	cmd := exec.Command("donut", "-f", "1", "-o", tempFile.Name())

	// Add input file
	cmd.Args = append(cmd.Args, config.Input)

	// Add optional arguments
	if config.Arch != 0 {
		cmd.Args = append(cmd.Args, "-a", fmt.Sprintf("%d", config.Arch))
	}

	if config.Bypass != 0 {
		cmd.Args = append(cmd.Args, "-b", fmt.Sprintf("%d", config.Bypass))
	}

	if config.Class != "" {
		cmd.Args = append(cmd.Args, "-c", config.Class)
	}

	if config.Method != "" {
		cmd.Args = append(cmd.Args, "-m", config.Method)
	}

	if config.Args != "" {
		cmd.Args = append(cmd.Args, "-p", config.Args)
	}

	if config.ExitOpt != 0 {
		cmd.Args = append(cmd.Args, "-x", fmt.Sprintf("%d", config.ExitOpt))
	}

	// Run the command
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("donut failed: %w\nOutput: %s", err, string(output))
	}

	// Read the shellcode from the temp file
	shellcode, err := os.ReadFile(tempFile.Name())
	if err != nil {
		return nil, fmt.Errorf("failed to read shellcode: %w", err)
	}

	return shellcode, nil
}

// CreateFromFile is a convenience function to generate shellcode from a file
func CreateFromFile(filePath string, args string) ([]byte, error) {
	config := DefaultConfig()
	config.Input = filePath
	config.Args = args

	return Create(config)
}

// GetModuleType determines the module type based on file extension
func GetModuleType(filePath string) int {
	ext := ""
	for i := len(filePath) - 1; i >= 0; i-- {
		if filePath[i] == '.' {
			ext = filePath[i:]
			break
		}
	}

	switch ext {
	case ".exe":
		return ModuleEXE
	case ".dll":
		return ModuleDLL
	case ".vbs":
		return ModuleVBS
	case ".js":
		return ModuleJS
	default:
		return 0
	}
}

// Constants for Donut
const (
	// Architecture
	ArchX86 = 1 // x86
	ArchX64 = 2 // AMD64
	ArchX84 = 3 // x86 + AMD64

	// Module types
	ModuleNetDLL = 1 // .NET DLL
	ModuleNetEXE = 2 // .NET EXE
	ModuleDLL    = 3 // Unmanaged DLL
	ModuleEXE    = 4 // Unmanaged EXE
	ModuleVBS    = 5 // VBScript
	ModuleJS     = 6 // JavaScript

	// Instance types
	InstanceEmbed = 1 // Module is embedded
	InstanceHTTP  = 2 // Module is downloaded from remote server

	// Format types
	FormatBinary     = 1 // Binary file
	FormatBase64     = 2 // Base64 string
	FormatC          = 3 // C/C++ source
	FormatRuby       = 4 // Ruby source
	FormatPython     = 5 // Python source
	FormatPowershell = 6 // PowerShell source
	FormatCSharp     = 7 // C# source
	FormatHex        = 8 // Hexadecimal string

	// Compression engines
	CompressNone   = 1 // No compression
	CompressAplib  = 2 // aPLib compression
	CompressLZNT1  = 3 // LZNT1 compression
	CompressXpress = 4 // Xpress compression

	// Entropy levels
	EntropyNone    = 1 // No entropy
	EntropyRandom  = 2 // Random names
	EntropyDefault = 3 // Random names + encryption

	// Bypass options
	BypassNone     = 1 // Disables bypassing AMSI/WDLP/ETW
	BypassAbort    = 2 // If bypassing AMSI/WLDP/ETW fails, the loader stops running
	BypassContinue = 3 // If bypassing AMSI/WLDP/ETW fails, the loader continues running

	// PE headers options
	HeadersOverwrite = 1 // Overwrite PE headers
	HeadersKeep      = 2 // Preserve PE headers

	// Exit options
	ExitThread  = 1 // Exit thread
	ExitProcess = 2 // Exit process
	ExitBlock   = 3 // Block indefinitely
)

// This function is implemented in shellcode.go
// func ExecuteShellcode(shellcode []byte) error
