#include "http.h"
#include <iostream>
#include <sstream>
#include <codecvt>
#include <locale>

namespace common {
namespace http {

// Convert string to wstring
std::wstring stringToWstring(const std::string& str) {
    if (str.empty()) return L"";
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// Convert wstring to string
std::string wstringToString(const std::wstring& wstr) {
    if (wstr.empty()) return "";
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

// Parse URL into components
bool parseUrl(const std::wstring& url, std::wstring& hostname, std::wstring& path, INTERNET_PORT& port, bool& secure) {
    URL_COMPONENTS urlComp;
    ZeroMemory(&urlComp, sizeof(urlComp));
    urlComp.dwStructSize = sizeof(urlComp);

    // Set required component lengths to non-zero so that they are cracked
    urlComp.dwSchemeLength = -1;
    urlComp.dwHostNameLength = -1;
    urlComp.dwUrlPathLength = -1;
    urlComp.dwExtraInfoLength = -1;

    if (!WinHttpCrackUrl(url.c_str(), (DWORD)url.length(), 0, &urlComp)) {
        std::cerr << "Failed to parse URL: " << GetLastError() << std::endl;
        return false;
    }

    // Extract hostname
    hostname = std::wstring(urlComp.lpszHostName, urlComp.dwHostNameLength);

    // Extract path
    path = std::wstring(urlComp.lpszUrlPath, urlComp.dwUrlPathLength);
    if (urlComp.lpszExtraInfo) {
        path += std::wstring(urlComp.lpszExtraInfo, urlComp.dwExtraInfoLength);
    }

    // Extract port
    port = urlComp.nPort;

    // Determine if secure
    secure = (urlComp.nScheme == INTERNET_SCHEME_HTTPS);

    return true;
}

// Send HTTP request
HttpResponse sendRequest(const HttpRequest& request) {
    HttpResponse response;
    response.success = false;
    response.statusCode = 0;

    // Parse URL
    std::wstring hostname, path;
    INTERNET_PORT port;
    bool secure;
    if (!parseUrl(request.url, hostname, path, port, secure)) {
        response.error = "Failed to parse URL";
        return response;
    }

    // Initialize WinHTTP
    HINTERNET hSession = WinHttpOpen(
        request.userAgent.c_str(),
        WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
        WINHTTP_NO_PROXY_NAME,
        WINHTTP_NO_PROXY_BYPASS,
        0
    );

    if (!hSession) {
        response.error = "Failed to initialize WinHTTP: " + std::to_string(GetLastError());
        return response;
    }

    // Connect to server
    HINTERNET hConnect = WinHttpConnect(
        hSession,
        hostname.c_str(),
        port,
        0
    );

    if (!hConnect) {
        response.error = "Failed to connect to server: " + std::to_string(GetLastError());
        WinHttpCloseHandle(hSession);
        return response;
    }

    // Create request
    HINTERNET hRequest = WinHttpOpenRequest(
        hConnect,
        request.method.c_str(),
        path.c_str(),
        NULL,
        WINHTTP_NO_REFERER,
        WINHTTP_DEFAULT_ACCEPT_TYPES,
        secure ? WINHTTP_FLAG_SECURE : 0
    );

    if (!hRequest) {
        response.error = "Failed to create request: " + std::to_string(GetLastError());
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return response;
    }

    // Add headers
    if (!request.contentType.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Content-Type: " + request.contentType).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    if (!request.accept.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Accept: " + request.accept).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    if (!request.acceptEncoding.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Accept-Encoding: " + request.acceptEncoding).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    if (!request.acceptLanguage.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Accept-Language: " + request.acceptLanguage).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    if (!request.cacheControl.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Cache-Control: " + request.cacheControl).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    if (!request.connection.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Connection: " + request.connection).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    if (!request.cookie.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Cookie: " + request.cookie).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    // Add custom headers
    for (const auto& header : request.headers) {
        WinHttpAddRequestHeaders(
            hRequest,
            (header.first + L": " + header.second).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    // Send request
    BOOL bResults = WinHttpSendRequest(
        hRequest,
        WINHTTP_NO_ADDITIONAL_HEADERS,
        0,
        request.data.empty() ? WINHTTP_NO_REQUEST_DATA : (LPVOID)request.data.c_str(),
        request.data.empty() ? 0 : (DWORD)request.data.length(),
        request.data.empty() ? 0 : (DWORD)request.data.length(),
        0
    );

    if (!bResults) {
        response.error = "Failed to send request: " + std::to_string(GetLastError());
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return response;
    }

    // Receive response
    bResults = WinHttpReceiveResponse(hRequest, NULL);
    if (!bResults) {
        response.error = "Failed to receive response: " + std::to_string(GetLastError());
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return response;
    }

    // Get status code
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(statusCode);
    WinHttpQueryHeaders(
        hRequest,
        WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
        WINHTTP_HEADER_NAME_BY_INDEX,
        &statusCode,
        &statusCodeSize,
        WINHTTP_NO_HEADER_INDEX
    );
    response.statusCode = statusCode;

    // Read response data
    DWORD bytesAvailable = 0;
    DWORD bytesRead = 0;
    std::string responseData;

    do {
        bytesAvailable = 0;
        if (!WinHttpQueryDataAvailable(hRequest, &bytesAvailable)) {
            response.error = "Error in WinHttpQueryDataAvailable: " + std::to_string(GetLastError());
            break;
        }

        if (bytesAvailable == 0) {
            break;
        }

        std::vector<char> buffer(bytesAvailable + 1);
        if (!WinHttpReadData(hRequest, buffer.data(), bytesAvailable, &bytesRead)) {
            response.error = "Error in WinHttpReadData: " + std::to_string(GetLastError());
            break;
        }

        buffer[bytesRead] = '\0';
        responseData.append(buffer.data(), bytesRead);
    } while (bytesAvailable > 0);

    response.body = responseData;
    response.success = true;

    // Clean up
    WinHttpCloseHandle(hRequest);
    WinHttpCloseHandle(hConnect);
    WinHttpCloseHandle(hSession);

    return response;
}

// Download file to memory
std::vector<uint8_t> downloadFile(const std::wstring& url) {
    std::vector<uint8_t> fileData;

    // Create HTTP request
    HttpRequest request;
    request.url = url;
    request.method = L"GET";
    request.userAgent = L"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    request.accept = L"*/*";
    request.acceptEncoding = L"gzip, deflate";
    request.acceptLanguage = L"en-US,en;q=0.9";
    request.cacheControl = L"no-cache";
    request.connection = L"Keep-Alive";

    // Send request
    HttpResponse response = sendRequest(request);
    if (!response.success) {
        std::cerr << "Failed to download file: " << response.error << std::endl;
        return fileData;
    }

    // Convert response body to vector<uint8_t>
    fileData.assign(response.body.begin(), response.body.end());
    return fileData;
}

// URL encode
std::wstring urlEncode(const std::wstring& value) {
    std::wostringstream escaped;
    escaped.fill('0');
    escaped << std::hex;

    for (auto i = value.begin(); i != value.end(); ++i) {
        wchar_t c = *i;
        // Keep alphanumeric and other accepted characters intact
        if (isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            escaped << c;
            continue;
        }

        // Any other characters are percent-encoded
        escaped << L'%' << std::setw(2) << int((unsigned char)c);
    }

    return escaped.str();
}

} // namespace http
} // namespace common
