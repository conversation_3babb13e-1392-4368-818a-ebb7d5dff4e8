#include <windows.h>
#include <stdio.h>

// Simple BOF that gets the current username
void go(char *args, int length) {
    DWORD size = 256;
    char username[256];

    // Print a message to show the BOF is running
    printf("[+] Executing whoami BOF...\n");

    // Get the current username
    if (GetUserNameA(username, &size)) {
        printf("[+] Current user: %s\n", username);
    } else {
        printf("[-] Failed to get username. Error: %d\n", GetLastError());
    }

    // Get domain information if available
    char computerName[256];
    DWORD computerNameSize = sizeof(computerName);
    if (GetComputerNameA(computerName, &computerNameSize)) {
        printf("[+] Computer name: %s\n", computerName);
    }

    // Check if running as admin
    BOOL isElevated = FALSE;
    HANDLE token = NULL;
    if (OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &token)) {
        TOKEN_ELEVATION elevation;
        DWORD size = sizeof(TOKEN_ELEVATION);
        if (GetTokenInformation(token, TokenElevation, &elevation, sizeof(elevation), &size)) {
            isElevated = elevation.TokenIsElevated;
        }
        CloseHandle(token);
    }

    printf("[+] Running as admin: %s\n", isElevated ? "Yes" : "No");

    printf("[+] Whoami BOF completed successfully\n");
}
