# C++ Agent Makefile for Windows with MinGW

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -pedantic -O2 -DUNICODE -D_UNICODE -D_CRT_SECURE_NO_WARNINGS

# Directories
SRC_DIR = src
INCLUDE_DIR = include
BIN_DIR = bin
OBJ_DIR = obj

# Source files
SOURCES = $(SRC_DIR)/main.cpp $(SRC_DIR)/agent.cpp $(SRC_DIR)/http.cpp $(SRC_DIR)/crypto.cpp $(SRC_DIR)/shellcode.cpp $(SRC_DIR)/coffloader.cpp
OBJECTS = $(OBJ_DIR)/main.o $(OBJ_DIR)/agent.o $(OBJ_DIR)/http.o $(OBJ_DIR)/crypto.o $(OBJ_DIR)/shellcode.o $(OBJ_DIR)/coffloader.o

# Target executable
TARGET = $(BIN_DIR)/cpp_agent.exe

# Libraries
LIBS = -lwinhttp -lcrypt32

# Include paths
INCLUDES = -I$(INCLUDE_DIR)

# Default target
all: prepare $(TARGET)

# Prepare directories
prepare:
	if not exist $(BIN_DIR) mkdir $(BIN_DIR)
	if not exist $(OBJ_DIR) mkdir $(OBJ_DIR)
	if not exist $(INCLUDE_DIR)\nlohmann mkdir $(INCLUDE_DIR)\nlohmann
	if not exist $(INCLUDE_DIR)\nlohmann\json.hpp (
		echo Downloading nlohmann/json...
		powershell -Command "Invoke-WebRequest -Uri 'https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp' -OutFile '$(INCLUDE_DIR)\nlohmann\json.hpp'"
	)

# Compile source files
$(OBJ_DIR)/main.o: $(SRC_DIR)/main.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/agent.o: $(SRC_DIR)/agent.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/http.o: $(SRC_DIR)/http.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/crypto.o: $(SRC_DIR)/crypto.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/shellcode.o: $(SRC_DIR)/shellcode.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/coffloader.o: $(SRC_DIR)/coffloader.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Link object files
$(TARGET): $(OBJECTS)
	$(CXX) $^ $(LIBS) -o $@
	copy $(TARGET) cpp_agent.exe
	echo Build successful!
	echo Executable is located at: $(TARGET)

# Clean build files
clean:
	if exist $(OBJ_DIR) rmdir /s /q $(OBJ_DIR)
	if exist $(BIN_DIR) rmdir /s /q $(BIN_DIR)
	if exist cpp_agent.exe del cpp_agent.exe

# Run the agent
run: all
	cpp_agent.exe

# Run the agent in debug mode
debug: all
	cpp_agent.exe --debug

.PHONY: all prepare clean run debug
