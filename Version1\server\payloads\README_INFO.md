# Information Gathering Payloads

This directory contains several information gathering payloads that can be used with the C2 framework to collect system information from target agents.

## Available Payloads

### 1. info.c (Full-featured C Information Gatherer)

This is a comprehensive C program that collects detailed system information including:
- System details (OS, CPU, memory)
- Network configuration
- User accounts
- Running processes
- Installed software
- Security products (AV, firewall)

**Compilation:**
```
compile_info.bat
```

**Usage:**
```
donut <agent_id> info.exe
```

### 2. info_simple.c (Simplified C Information Gatherer)

A lighter version with fewer dependencies that collects basic system information:
- System details
- Disk drives
- Network hostname
- Admin status
- Environment variables

**Compilation:**
```
compile_info_simple.bat
```

**Usage:**
```
donut <agent_id> info_simple.exe
```

### 3. info.ps1 (PowerShell Information Gatherer)

A PowerShell script that uses built-in cmdlets to gather system information:
- System details
- Network configuration
- User accounts
- Running processes
- Installed software
- Security products
- Drives information
- Environment variables

**Usage:**
```
shell <agent_id> powershell -ExecutionPolicy Bypass -File C:\path\to\info.ps1
```

## Compilation Requirements

To compile the C programs, you need one of the following:
- Visual Studio with C/C++ tools
- MinGW GCC compiler

The batch files will automatically detect which compiler is available and use it.

## Notes

- The information gathered by these payloads is sent back to the C2 server and can be viewed using the `results` command.
- The PowerShell script requires PowerShell to be available on the target system.
- The C programs are standalone executables and don't require any additional dependencies to run.

## Example Workflow

1. Compile the desired payload:
   ```
   cd server/payloads
   compile_info_simple.bat
   ```

2. Set an alias for the agent (optional):
   ```
   alias <agent_id> target1
   ```

3. Execute the payload:
   ```
   donut target1 info_simple.exe
   ```

4. View the results:
   ```
   results target1
   ```
