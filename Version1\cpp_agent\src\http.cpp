#include "../include/http.h"
#include <iostream>
#include <sstream>

namespace c2 {
namespace http {

HttpResponse sendRequest(const HttpRequest& request) {
    HttpResponse response;
    response.success = false;

    // Parse URL
    std::wstring hostname;
    std::wstring path;
    INTERNET_PORT port;
    bool isHttps;

    if (!parseUrl(request.url, hostname, path, port, isHttps)) {
        response.error = "Failed to parse URL";
        return response;
    }

    // Initialize WinHTTP
    HINTERNET hSession = WinHttpOpen(
        request.userAgent.c_str(),
        WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
        WINHTTP_NO_PROXY_NAME,
        WINHTTP_NO_PROXY_BYPASS,
        0
    );

    if (!hSession) {
        response.error = "Failed to initialize WinHTTP";
        return response;
    }

    // Connect to server
    HINTERNET hConnect = WinHttpConnect(
        hSession,
        hostname.c_str(),
        port,
        0
    );

    if (!hConnect) {
        WinHttpCloseHandle(hSession);
        response.error = "Failed to connect to server";
        return response;
    }

    // Create request
    DWORD flags = isHttps ? WINHTTP_FLAG_SECURE : 0;
    HINTERNET hRequest = WinHttpOpenRequest(
        hConnect,
        request.method.c_str(),
        path.c_str(),
        NULL,
        WINHTTP_NO_REFERER,
        WINHTTP_DEFAULT_ACCEPT_TYPES,
        flags
    );

    if (!hRequest) {
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        response.error = "Failed to create request";
        return response;
    }

    // Add headers individually to ensure they're set correctly
    WinHttpAddRequestHeaders(
        hRequest,
        (L"Content-Type: " + request.contentType).c_str(),
        -1,
        WINHTTP_ADDREQ_FLAG_ADD
    );

    WinHttpAddRequestHeaders(
        hRequest,
        (L"Accept: " + request.accept).c_str(),
        -1,
        WINHTTP_ADDREQ_FLAG_ADD
    );

    WinHttpAddRequestHeaders(
        hRequest,
        (L"Accept-Encoding: " + request.acceptEncoding).c_str(),
        -1,
        WINHTTP_ADDREQ_FLAG_ADD
    );

    WinHttpAddRequestHeaders(
        hRequest,
        (L"Accept-Language: " + request.acceptLanguage).c_str(),
        -1,
        WINHTTP_ADDREQ_FLAG_ADD
    );

    WinHttpAddRequestHeaders(
        hRequest,
        (L"Cache-Control: " + request.cacheControl).c_str(),
        -1,
        WINHTTP_ADDREQ_FLAG_ADD
    );

    WinHttpAddRequestHeaders(
        hRequest,
        (L"Connection: " + request.connection).c_str(),
        -1,
        WINHTTP_ADDREQ_FLAG_ADD
    );

    if (!request.cookie.empty()) {
        WinHttpAddRequestHeaders(
            hRequest,
            (L"Cookie: " + request.cookie).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    // Add any additional headers
    for (const auto& header : request.headers) {
        WinHttpAddRequestHeaders(
            hRequest,
            (header.first + L": " + header.second).c_str(),
            -1,
            WINHTTP_ADDREQ_FLAG_ADD
        );
    }

    // Send request
    BOOL result = WinHttpSendRequest(
        hRequest,
        WINHTTP_NO_ADDITIONAL_HEADERS,
        0,
        request.data.empty() ? NULL : (LPVOID)request.data.c_str(),
        request.data.empty() ? 0 : (DWORD)request.data.length(),
        request.data.empty() ? 0 : (DWORD)request.data.length(),
        0
    );

    if (!result) {
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        response.error = "Failed to send request";
        return response;
    }

    // Receive response
    result = WinHttpReceiveResponse(hRequest, NULL);

    if (!result) {
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        response.error = "Failed to receive response";
        return response;
    }

    // Get status code
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(DWORD);
    WinHttpQueryHeaders(
        hRequest,
        WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
        WINHTTP_HEADER_NAME_BY_INDEX,
        &statusCode,
        &statusCodeSize,
        WINHTTP_NO_HEADER_INDEX
    );

    response.statusCode = statusCode;

    // Read response body
    DWORD bytesAvailable = 0;
    DWORD bytesRead = 0;
    std::string responseBody;

    do {
        bytesAvailable = 0;
        WinHttpQueryDataAvailable(hRequest, &bytesAvailable);

        if (bytesAvailable == 0) {
            break;
        }

        char* buffer = new char[bytesAvailable + 1];
        ZeroMemory(buffer, bytesAvailable + 1);

        WinHttpReadData(hRequest, buffer, bytesAvailable, &bytesRead);
        responseBody.append(buffer, bytesRead);

        delete[] buffer;
    } while (bytesAvailable > 0);

    // Clean up
    WinHttpCloseHandle(hRequest);
    WinHttpCloseHandle(hConnect);
    WinHttpCloseHandle(hSession);

    response.body = responseBody;
    response.success = true;

    return response;
}

bool parseUrl(const std::wstring& url, std::wstring& hostname, std::wstring& path, INTERNET_PORT& port, bool& isHttps) {
    URL_COMPONENTS urlComp;
    ZeroMemory(&urlComp, sizeof(urlComp));
    urlComp.dwStructSize = sizeof(urlComp);

    // Set required component lengths to non-zero to get the components
    urlComp.dwSchemeLength = -1;
    urlComp.dwHostNameLength = -1;
    urlComp.dwUrlPathLength = -1;

    // Crack the URL
    if (!WinHttpCrackUrl(url.c_str(), (DWORD)url.length(), 0, &urlComp)) {
        return false;
    }

    // Extract components
    hostname = std::wstring(urlComp.lpszHostName, urlComp.dwHostNameLength);
    path = std::wstring(urlComp.lpszUrlPath, urlComp.dwUrlPathLength);
    port = urlComp.nPort;

    // Check if HTTPS
    isHttps = (urlComp.nScheme == INTERNET_SCHEME_HTTPS);

    return true;
}

} // namespace http
} // namespace c2
