# Educational CNC Framework Architecture

## 🏗️ System Overview

The Educational CNC Framework is designed as a minimal, well-documented Command & Control system for learning cybersecurity concepts. It demonstrates core C2 principles while maintaining simplicity for educational purposes.

## 📊 Component Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Educational CNC Framework                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │   Operators     │    │   CNC Server    │    │     Agents      │ │
│  │   (SSH CLI)     │◄──►│   (HTTP API)    │◄──►│   (Beacons)     │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                          Security Layer                         │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │      RBAC       │    │   Encryption    │    │ Authentication  │ │
│  │   (Roles &      │    │   (XOR for      │    │  (Password &    │ │
│  │  Permissions)   │    │   Education)    │    │   Sessions)     │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 Core Components

### 1. CNC Server (`internal/server/`)

**Purpose**: Central command and control hub

**Key Files**:
- `server.go` - Core server logic and lifecycle management
- `handlers.go` - HTTP API endpoints for agent communication
- `ssh.go` - SSH interface for operator interaction

**Responsibilities**:
- Agent registration and management
- Task queuing and distribution
- Operator authentication and session management
- Command processing and result collection

**Network Interfaces**:
- HTTP API (default port 8080) - Agent communication
- SSH Server (default port 2222) - Operator interface

### 2. Authentication & RBAC (`internal/auth/`)

**Purpose**: User management and access control

**Key Features**:
- Role-based access control (Admin, Operator, Viewer)
- Password hashing with bcrypt
- Session management with expiration
- Command permission validation

**Roles & Permissions**:
```
Admin:    Full system access + user management
Operator: Agent control + command execution
Viewer:   Read-only access to agent information
```

### 3. Agent Management (`internal/agent/`)

**Purpose**: Agent lifecycle and task management

**Key Structures**:
- `Agent` - Represents a compromised system
- `Task` - Commands to be executed
- `TaskResult` - Execution results
- `BeaconData` - Agent check-in information

**Agent States**:
- `Active` - Recently checked in (< 10 minutes)
- `Stale` - Not seen recently (10 minutes - 1 hour)
- `Inactive` - Offline (> 1 hour)

### 4. Encryption (`internal/crypto/`)

**Purpose**: Simple encryption for educational purposes

**Implementation**: XOR encryption with shared key
- **Why XOR?** Easy to understand and implement
- **Educational Value**: Demonstrates encryption concepts
- **Real-world Note**: Production systems use AES, ChaCha20, etc.

### 5. Agent Implementation (`cmd/agent/`)

**Purpose**: Simulates a compromised system

**Capabilities**:
- Periodic beacons to C2 server
- Command execution (shell commands)
- Result reporting
- System information collection

**Communication Flow**:
1. Generate beacon with system info and task results
2. Encrypt beacon data
3. Send HTTP POST to server
4. Receive and decrypt response
5. Execute any received tasks

## 🔄 Communication Protocols

### Agent-to-Server (HTTP)

**Endpoint**: `POST /api/v1/beacon`

**Request Flow**:
```
1. Agent creates BeaconData structure
2. Converts to JSON
3. Encrypts with shared key
4. Sends as HTTP POST body
5. Server decrypts and processes
6. Server responds with encrypted task (if any)
```

**Beacon Data Structure**:
```json
{
  "agent_id": "hostname-timestamp",
  "hostname": "computer-name",
  "session_id": "random-session-id",
  "timestamp": "2024-01-15T10:30:00Z",
  "system_info": {
    "os": "windows",
    "architecture": "amd64",
    "username": "current-user",
    "process_id": 1234,
    "internal_ip": "*************"
  },
  "task_results": [...]
}
```

### Operator-to-Server (SSH)

**Protocol**: SSH with password authentication

**Command Interface**:
- Interactive shell with role-based commands
- Real-time agent status and control
- Task result viewing and management

**Session Flow**:
```
1. Operator connects via SSH
2. Server authenticates credentials
3. Creates session with role permissions
4. Provides interactive command interface
5. Validates commands against role permissions
6. Executes authorized operations
```

## 🛡️ Security Model

### Authentication Layers

1. **Operator Authentication**:
   - SSH password authentication
   - Session-based access control
   - Automatic session expiration

2. **Agent Authentication**:
   - Shared encryption key
   - Session ID validation
   - Timestamp verification

### Authorization Model

**Role-Based Access Control (RBAC)**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│   Command   │    Admin    │  Operator   │   Viewer    │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ list        │      ✓      │      ✓      │      ✓      │
│ shell       │      ✓      │      ✓      │      ✗      │
│ results     │      ✓      │      ✓      │      ✓      │
│ adduser     │      ✓      │      ✗      │      ✗      │
│ deluser     │      ✓      │      ✗      │      ✗      │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### Encryption Strategy

**Educational XOR Encryption**:
- Shared key between server and agents
- Simple XOR cipher for data protection
- Base64 encoding for transport
- **Note**: Not secure for production use!

## 📁 Directory Structure

```
educational-cnc/
├── cmd/
│   ├── server/          # Server executable
│   └── agent/           # Agent executable
├── internal/
│   ├── auth/            # Authentication & RBAC
│   ├── agent/           # Agent management
│   ├── server/          # Core server logic
│   └── crypto/          # Encryption utilities
├── docs/
│   ├── TUTORIAL.md      # Step-by-step guide
│   ├── ARCHITECTURE.md  # This document
│   └── SECURITY.md      # Security considerations
├── go.mod               # Go module definition
├── README.md            # Project overview
├── build.sh             # Linux build script
└── build.bat            # Windows build script
```

## 🔄 Data Flow

### Agent Registration Flow

```
1. Agent starts up
2. Collects system information
3. Generates unique agent ID
4. Sends initial beacon to server
5. Server registers new agent
6. Server responds with acknowledgment
7. Agent enters beacon loop
```

### Command Execution Flow

```
1. Operator issues command via SSH
2. Server validates operator permissions
3. Server creates task and queues for agent
4. Agent sends beacon (periodic check-in)
5. Server responds with queued task
6. Agent executes task
7. Agent includes result in next beacon
8. Operator views results via SSH
```

## 🎯 Design Principles

### Educational Focus
- **Simplicity**: Easy to understand and modify
- **Documentation**: Extensive comments and guides
- **Transparency**: No obfuscation or complex evasion

### Professional Structure
- **Modular Design**: Clear separation of concerns
- **Error Handling**: Proper error management
- **Logging**: Comprehensive logging for debugging

### Security Awareness
- **Minimal Permissions**: Least privilege principle
- **Input Validation**: Sanitize all inputs
- **Session Management**: Proper session lifecycle

## 🚀 Extensibility

### Adding New Commands
1. Define command in SSH handler
2. Add to role permissions
3. Implement agent-side execution
4. Update help documentation

### Adding New Agent Types
1. Extend BeaconData structure
2. Add new task types
3. Implement platform-specific handlers
4. Update server processing logic

### Improving Security
1. Replace XOR with AES encryption
2. Add certificate-based authentication
3. Implement traffic obfuscation
4. Add anti-analysis features

## 📚 Learning Outcomes

After studying this architecture, students should understand:

1. **C2 Communication Patterns**: How malware communicates with infrastructure
2. **RBAC Implementation**: Role-based access control in practice
3. **Network Protocols**: HTTP and SSH in security contexts
4. **Encryption Basics**: Symmetric encryption and key management
5. **System Design**: Modular architecture and separation of concerns

---

**Remember**: This is for educational purposes only. Real-world C2 frameworks are much more sophisticated and should only be studied for defensive purposes.
