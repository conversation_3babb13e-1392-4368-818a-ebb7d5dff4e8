#include "crypto.h"
#include <algorithm>
#include <iostream>
#include <sstream>
#include <iomanip>

namespace common {
namespace crypto {

// Base64 encoding table
static const char base64_chars[] =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

// Base64 encoding
std::string base64_encode(const std::string& input) {
    std::string ret;
    int i = 0;
    int j = 0;
    unsigned char char_array_3[3];
    unsigned char char_array_4[4];
    size_t in_len = input.size();
    const unsigned char* bytes_to_encode = reinterpret_cast<const unsigned char*>(input.c_str());

    while (in_len--) {
        char_array_3[i++] = *(bytes_to_encode++);
        if (i == 3) {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for (i = 0; i < 4; i++)
                ret += base64_chars[char_array_4[i]];
            i = 0;
        }
    }

    if (i) {
        for (j = i; j < 3; j++)
            char_array_3[j] = '\0';

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
        char_array_4[3] = char_array_3[2] & 0x3f;

        for (j = 0; j < i + 1; j++)
            ret += base64_chars[char_array_4[j]];

        while (i++ < 3)
            ret += '=';
    }

    return ret;
}

// Base64 decoding
std::string base64_decode(const std::string& encoded_string) {
    size_t in_len = encoded_string.size();
    int i = 0;
    int j = 0;
    int in_ = 0;
    unsigned char char_array_4[4], char_array_3[3];
    std::string ret;

    auto is_base64 = [](unsigned char c) -> bool {
        return (isalnum(c) || (c == '+') || (c == '/'));
    };

    while (in_len-- && (encoded_string[in_] != '=') && is_base64(encoded_string[in_])) {
        char_array_4[i++] = encoded_string[in_]; in_++;
        if (i == 4) {
            for (i = 0; i < 4; i++)
                char_array_4[i] = static_cast<unsigned char>(std::string(base64_chars).find(char_array_4[i]));

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; i < 3; i++)
                ret += char_array_3[i];
            i = 0;
        }
    }

    if (i) {
        for (j = i; j < 4; j++)
            char_array_4[j] = 0;

        for (j = 0; j < 4; j++)
            char_array_4[j] = static_cast<unsigned char>(std::string(base64_chars).find(char_array_4[j]));

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

        for (j = 0; j < i - 1; j++)
            ret += char_array_3[j];
    }

    return ret;
}

// AES encryption
std::vector<uint8_t> aes_encrypt(const std::vector<uint8_t>& plaintext, const std::vector<uint8_t>& key, const std::vector<uint8_t>& iv) {
    // Ensure key and IV are the correct size
    if (key.size() != 16 || iv.size() != 16) {
        std::cerr << "Error: AES key and IV must be 16 bytes" << std::endl;
        return {};
    }

    // Add PKCS7 padding
    std::vector<uint8_t> padded = pkcs7_pad(plaintext, 16);
    std::vector<uint8_t> ciphertext(padded.size());

    // Initialize crypto provider
    HCRYPTPROV hProv = 0;
    if (!CryptAcquireContext(&hProv, NULL, MS_ENH_RSA_AES_PROV, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
        std::cerr << "CryptAcquireContext failed: " << GetLastError() << std::endl;
        return {};
    }

    // Create a new key
    HCRYPTKEY hKey = 0;
    struct {
        BLOBHEADER hdr;
        DWORD keySize;
        BYTE keyData[16];
    } keyBlob;

    keyBlob.hdr.bType = PLAINTEXTKEYBLOB;
    keyBlob.hdr.bVersion = CUR_BLOB_VERSION;
    keyBlob.hdr.reserved = 0;
    keyBlob.hdr.aiKeyAlg = CALG_AES_128;
    keyBlob.keySize = 16;
    memcpy(keyBlob.keyData, key.data(), 16);

    if (!CryptImportKey(hProv, (BYTE*)&keyBlob, sizeof(keyBlob), 0, 0, &hKey)) {
        std::cerr << "CryptImportKey failed: " << GetLastError() << std::endl;
        CryptReleaseContext(hProv, 0);
        return {};
    }

    // Set the IV
    DWORD mode = CRYPT_MODE_CBC;
    if (!CryptSetKeyParam(hKey, KP_MODE, (BYTE*)&mode, 0)) {
        std::cerr << "CryptSetKeyParam (mode) failed: " << GetLastError() << std::endl;
        CryptDestroyKey(hKey);
        CryptReleaseContext(hProv, 0);
        return {};
    }

    if (!CryptSetKeyParam(hKey, KP_IV, (BYTE*)iv.data(), 0)) {
        std::cerr << "CryptSetKeyParam (IV) failed: " << GetLastError() << std::endl;
        CryptDestroyKey(hKey);
        CryptReleaseContext(hProv, 0);
        return {};
    }

    // Copy the padded text to a buffer
    std::vector<BYTE> buffer = padded;

    // Encrypt the data
    DWORD dataLen = (DWORD)buffer.size();
    if (!CryptEncrypt(hKey, 0, TRUE, 0, buffer.data(), &dataLen, (DWORD)buffer.size())) {
        std::cerr << "CryptEncrypt failed: " << GetLastError() << std::endl;
        CryptDestroyKey(hKey);
        CryptReleaseContext(hProv, 0);
        return {};
    }

    // Clean up
    CryptDestroyKey(hKey);
    CryptReleaseContext(hProv, 0);

    return buffer;
}

// AES decryption
std::vector<uint8_t> aes_decrypt(const std::vector<uint8_t>& ciphertext, const std::vector<uint8_t>& key, const std::vector<uint8_t>& iv) {
    // Ensure key and IV are the correct size
    if (key.size() != 16 || iv.size() != 16) {
        std::cerr << "Error: AES key and IV must be 16 bytes" << std::endl;
        return {};
    }

    // Ensure ciphertext is a multiple of the block size
    if (ciphertext.size() % 16 != 0) {
        std::cerr << "Error: Ciphertext must be a multiple of 16 bytes" << std::endl;
        return {};
    }

    std::vector<uint8_t> plaintext(ciphertext.size());

    // Initialize crypto provider
    HCRYPTPROV hProv = 0;
    if (!CryptAcquireContext(&hProv, NULL, MS_ENH_RSA_AES_PROV, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
        std::cerr << "CryptAcquireContext failed: " << GetLastError() << std::endl;
        return {};
    }

    // Create a new key
    HCRYPTKEY hKey = 0;
    struct {
        BLOBHEADER hdr;
        DWORD keySize;
        BYTE keyData[16];
    } keyBlob;

    keyBlob.hdr.bType = PLAINTEXTKEYBLOB;
    keyBlob.hdr.bVersion = CUR_BLOB_VERSION;
    keyBlob.hdr.reserved = 0;
    keyBlob.hdr.aiKeyAlg = CALG_AES_128;
    keyBlob.keySize = 16;
    memcpy(keyBlob.keyData, key.data(), 16);

    if (!CryptImportKey(hProv, (BYTE*)&keyBlob, sizeof(keyBlob), 0, 0, &hKey)) {
        std::cerr << "CryptImportKey failed: " << GetLastError() << std::endl;
        CryptReleaseContext(hProv, 0);
        return {};
    }

    // Set the IV
    DWORD mode = CRYPT_MODE_CBC;
    if (!CryptSetKeyParam(hKey, KP_MODE, (BYTE*)&mode, 0)) {
        std::cerr << "CryptSetKeyParam (mode) failed: " << GetLastError() << std::endl;
        CryptDestroyKey(hKey);
        CryptReleaseContext(hProv, 0);
        return {};
    }

    if (!CryptSetKeyParam(hKey, KP_IV, (BYTE*)iv.data(), 0)) {
        std::cerr << "CryptSetKeyParam (IV) failed: " << GetLastError() << std::endl;
        CryptDestroyKey(hKey);
        CryptReleaseContext(hProv, 0);
        return {};
    }

    // Copy the ciphertext to a buffer
    std::vector<BYTE> buffer = ciphertext;

    // Decrypt the data
    DWORD dataLen = (DWORD)buffer.size();
    if (!CryptDecrypt(hKey, 0, TRUE, 0, buffer.data(), &dataLen)) {
        std::cerr << "CryptDecrypt failed: " << GetLastError() << std::endl;
        CryptDestroyKey(hKey);
        CryptReleaseContext(hProv, 0);
        return {};
    }

    // Clean up
    CryptDestroyKey(hKey);
    CryptReleaseContext(hProv, 0);

    // Remove PKCS7 padding
    return pkcs7_unpad(buffer);
}

// XOR encryption
std::vector<uint8_t> xor_encrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key) {
    if (key.empty()) {
        return {};
    }

    std::vector<uint8_t> result(data.size());
    for (size_t i = 0; i < data.size(); i++) {
        result[i] = data[i] ^ key[i % key.size()];
    }
    return result;
}

// XOR decryption (same as encryption)
std::vector<uint8_t> xor_decrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key) {
    return xor_encrypt(data, key); // XOR is symmetric
}

// Generate random data
std::vector<uint8_t> generate_random(size_t length) {
    std::vector<uint8_t> result(length);
    
    HCRYPTPROV hProv = 0;
    if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
        std::cerr << "CryptAcquireContext failed: " << GetLastError() << std::endl;
        return result;
    }
    
    if (!CryptGenRandom(hProv, (DWORD)length, result.data())) {
        std::cerr << "CryptGenRandom failed: " << GetLastError() << std::endl;
    }
    
    CryptReleaseContext(hProv, 0);
    return result;
}

// Calculate SHA-256 hash
std::string calculate_sha256(const std::vector<uint8_t>& data) {
    HCRYPTPROV hProv = 0;
    HCRYPTHASH hHash = 0;
    BYTE rgbHash[32];
    DWORD cbHash = 0;
    std::string result;

    // Get handle to the crypto provider
    if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
        return "";
    }

    // Create hash object
    if (!CryptCreateHash(hProv, CALG_SHA_256, 0, 0, &hHash)) {
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Hash the data
    if (!CryptHashData(hHash, data.data(), (DWORD)data.size(), 0)) {
        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Get hash value
    cbHash = sizeof(rgbHash);
    if (!CryptGetHashParam(hHash, HP_HASHVAL, rgbHash, &cbHash, 0)) {
        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Convert hash to hex string
    std::stringstream ss;
    for (DWORD i = 0; i < cbHash; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)rgbHash[i];
    }

    // Clean up
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);

    return ss.str();
}

// Calculate MD5 hash
std::string calculate_md5(const std::vector<uint8_t>& data) {
    HCRYPTPROV hProv = 0;
    HCRYPTHASH hHash = 0;
    BYTE rgbHash[16];
    DWORD cbHash = 0;
    std::string result;

    // Get handle to the crypto provider
    if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
        return "";
    }

    // Create hash object
    if (!CryptCreateHash(hProv, CALG_MD5, 0, 0, &hHash)) {
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Hash the data
    if (!CryptHashData(hHash, data.data(), (DWORD)data.size(), 0)) {
        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Get hash value
    cbHash = sizeof(rgbHash);
    if (!CryptGetHashParam(hHash, HP_HASHVAL, rgbHash, &cbHash, 0)) {
        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);
        return "";
    }

    // Convert hash to hex string
    std::stringstream ss;
    for (DWORD i = 0; i < cbHash; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)rgbHash[i];
    }

    // Clean up
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);

    return ss.str();
}

// PKCS7 padding
std::vector<uint8_t> pkcs7_pad(const std::vector<uint8_t>& data, size_t block_size) {
    size_t padding = block_size - (data.size() % block_size);
    if (padding == 0) {
        padding = block_size; // If data is already aligned, add a full block of padding
    }

    std::vector<uint8_t> padded = data;
    padded.resize(data.size() + padding, static_cast<uint8_t>(padding));
    return padded;
}

// PKCS7 unpadding
std::vector<uint8_t> pkcs7_unpad(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return {};
    }

    uint8_t padding = data.back();
    if (padding == 0 || padding > data.size()) {
        return {}; // Invalid padding
    }

    // Verify padding
    for (size_t i = data.size() - padding; i < data.size(); i++) {
        if (data[i] != padding) {
            return {}; // Invalid padding
        }
    }

    return std::vector<uint8_t>(data.begin(), data.end() - padding);
}

} // namespace crypto
} // namespace common
