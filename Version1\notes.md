C2

Agent (Implant): 
Language: C#
1. Targeting WindowsOS machines strictly.
2. Use HTTP via HTTP beaconing as communication to Master C2 Server. 
2.1 (/api/v1/health) and custom headers mimicking SaaS platforms.
2.2 POST requests with encrypted metadata
3. Start with basic reverse shell functionality
4. Use direct syscalls in C# agents to bypass EDR hooks (e.g., NtAllocateVirtualMemory)
5. Process Hollowing into svchost.exe for execution

Master Server (C2 Server):
Language: Golang
1. SSH server via port 1337
2. CLI that emulates a real terminal (Like SliverC2)
3. Functionality to interact with agents with Base64-encoded tasks
4. Track agents in memory with periodic backups to encrypted flat files